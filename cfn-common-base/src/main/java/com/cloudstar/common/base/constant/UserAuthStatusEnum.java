package com.cloudstar.common.base.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * 用户身份验证状态枚举
 *
 * <AUTHOR>
 * @description: TODO
 * @date 2022/7/21 13:59
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum UserAuthStatusEnum {
    UPLOAD("UPLOAD", "待认证"),
    AUDIT("AUDIT", "认证中"),
    VERIFIED("VERIFIED", "已认证"),
    
    PREVENT("PREVENT", "认证失败"),
    PENDDING("pendding", "待审批"),
    APPROVAL("approval", "审批通过"),
    REFUSE("refuse", "审批拒绝");
    
    /**
     * 缓存key
     */
    private String type;
    
    /**
     * 描述
     */
    private String desc;
    
    public static String getDesc(String type) {
        for (UserAuthStatusEnum value : UserAuthStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return null;
    }
}
