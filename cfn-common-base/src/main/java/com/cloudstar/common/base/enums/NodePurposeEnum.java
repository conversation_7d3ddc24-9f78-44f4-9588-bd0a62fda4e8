package com.cloudstar.common.base.enums;

/**
 * 节点用途枚举类
 */
public enum NodePurposeEnum {
    ALL("all", "全部"),
    TRAINING("training", "训练"),
    DEVELOP("develop", "开发"),
    INFERENCE("inference", "推理");

    private final String code;
    private final String description;

    NodePurposeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 节点用途代码
     *
     * @return 描述
     */
    public static String getDescriptionByCode(String code) {
        for (NodePurposeEnum purpose : NodePurposeEnum.values()) {
            if (purpose.code.equals(code)) {
                return purpose.description;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return code;
    }
}