package com.cloudstar.common.base.enums;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型来源枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResModelFromEnum {

    // 容器镜像
    CONTAINER_IMAGE("container_image", "容器镜像"),
    //对象存储
    OBJECT_STORAGE("object_storage", "对象存储"),
    //训练作业
    TRAIN_JOB("train_job", "训练作业");

    private final String type;

    private final String desc;

    public static String getDescByType(String type) {
        if (Strings.isNullOrEmpty(type)) {
            return StrUtil.EMPTY;
        }
        for (ResModelFromEnum value : ResModelFromEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return StrUtil.EMPTY;
    }
}
