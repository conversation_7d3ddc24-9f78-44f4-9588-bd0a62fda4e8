package com.cloudstar.common.base.pojo;


import lombok.Data;

import javax.validation.constraints.Digits;

/**
 * 基本要求 The type BaseRequest.
 *
 * <AUTHOR>
 * @date 2019/7/1
 */
@Data
public class BaseRequest {
    
    /**
     * 每页的条数
     */
    @Digits(integer = 10, fraction = 0)
    private String pageSize;
    
    /**
     * 页数
     */
    @Digits(integer = 10, fraction = 0)
    private String pageNo;
    
    /**
     * 排序的字段
     */
    private String sortdatafield;
    
    /**
     * 升序/降序
     */
    private String sortorder;
    
    /**
     * 角色类型
     */
    private String roleType;
}
