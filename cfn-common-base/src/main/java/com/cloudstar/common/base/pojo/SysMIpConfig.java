package com.cloudstar.common.base.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * IP访问控制表
 *
 * @TableName sys_m_ip_config
 */
@Data
public class SysMIpConfig implements Serializable {
    
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database table sys_m_ip_config
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private Long id;
    
    /**
     * IP地址或网段
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String ip;
    
    /**
     * 描述
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String description;
    
    /**
     * 访问控制类型（ 0:允许访问的IP地址/网段 1:拒绝访问的IP地址/网段）
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private Integer ipType;
    
    /**
     * 控制台类型所对应ID
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String controlType;
    
    /**
     * 所有者ID
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String ownerId;
    
    /**
     * 组织ID
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private Long orgSid;
    
    /**
     * 创建者组织ID
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String createdOrgSid;
    
    /**
     * 版本号
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private Long version;
    
    /**
     * 创建人
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String createdBy;
    
    /**
     * 创建时间
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private Date createdDt;
    
    /**
     * 更新人
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private String updatedBy;
    
    /**
     * 更新时间
     *
     * @mbg.generated 2021-09-14 16:24:53
     */
    private Date updatedDt;
}
