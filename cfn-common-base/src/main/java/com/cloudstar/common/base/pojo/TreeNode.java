package com.cloudstar.common.base.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 树节点
 *
 * <AUTHOR>
 * @date 2018-11-09
 */
@Data
public class TreeNode implements Serializable {
    
    private String id;
    
    private String name;
    
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String icon;
    
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String special;
    
    private Map<String, Object> specials;
    
    private Object value;
    
    private List<TreeNode> children;
    
    public TreeNode() {
        this.children = new ArrayList<>();
    }
    
}
