package com.cloudstar.common.base.pojo.result;


import lombok.Data;


/**
 * 休息 返回体
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
@Data
public class Rest<T> {

    /**
     * 状态码
     */
    private int code;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 是否成功
     */
    private boolean success;


    /**
     * 好吧
     *
     * @param t t
     *
     * @return {@link Rest}<{@link T}>
     */
    public static <T> Rest<T> ok(T t) {
        return ok(t, RestCodeEnum.SUCCESS, "操作成功");
    }

    /**
     * 好吧
     *
     * @param code 代码
     * @param message 消息
     *
     * @return {@link Rest}<{@link String}>
     */
    public static Rest<String> ok(RestCodeEnum code, String message) {
        return ok(null, code, message);
    }

    /**
     * 好吧
     *
     * @return {@link Rest}<{@link String}>
     */
    public static Rest<String> ok() {
        return ok(RestCodeEnum.SUCCESS, "操作成功");
    }

    /**
     * 好吧
     *
     * @param t t
     * @param code 代码
     * @param message 消息
     *
     * @return {@link Rest}<{@link T}>
     */
    public static <T> Rest<T> ok(T t, RestCodeEnum code, String message) {
        Rest<T> rest = new Rest<>();
        rest.setData(t);
        rest.setCode(code.getCode());
        rest.setSuccess(true);
        rest.setMessage(message);
        return rest;
    }

    /**
     * e
     *
     * @param t t
     * @param message 消息
     *
     * @return {@link Rest}<{@link T}>
     */
    public static <T> Rest<T> e(T t, String message) {
        return e(t, RestCodeEnum.SERVER_ERROR, message);
    }

    /**
     * e
     *
     * @param message 消息
     *
     * @return {@link Rest}<{@link String}>
     */
    public static Rest<String> e(String message) {
        return e(null, RestCodeEnum.SERVER_ERROR, message);
    }

    /**
     * e
     *
     * @return {@link Rest}<{@link String}>
     */
    public static Rest<String> e() {
        return e(null, RestCodeEnum.SERVER_ERROR, "系统错误");
    }

    /**
     * e
     *
     * @param t t
     * @param code 代码
     * @param message 消息
     *
     * @return {@link Rest}<{@link T}>
     */
    public static <T> Rest<T> e(T t, RestCodeEnum code, String message) {
        Rest<T> rest = new Rest<>();
        rest.setData(t);
        rest.setCode(code.getCode());
        rest.setMessage(message);
        rest.setSuccess(false);
        return rest;
    }

    /**
     * e
     *
     * @param code 代码
     * @param message 消息
     *
     * @return {@link Rest}<{@link T}>
     */
    public static <T> Rest<T> e(Integer code, String message) {
        Rest<T> rest = new Rest<>();
        rest.setCode(code);
        rest.setMessage(message);
        rest.setSuccess(false);
        return rest;
    }


}
