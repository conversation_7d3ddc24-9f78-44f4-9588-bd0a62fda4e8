package com.cloudstar.common.base.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.util.excel.service.ExportDataServer;

import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.io.FileUtil;


/**
 * excel跑龙套 excel工具类
 *
 * <AUTHOR>
 */
public class ExcelUtil {
    
    
    public static final String XLSX = ".xlsx";
    
    private static final String ZIP = ".zip";
    
    /**
     * 导入
     *
     * @param in    文件
     * @param clazz 模板表
     */
    public static <T> List<T> excelImport(InputStream in, Class<T> clazz) {
        return EasyExcel.read(in).head(clazz).sheet().doReadSync();
    }
    
    
    /**
     * 导出
     *
     * @param fileName  文件名  不用加文件后缀
     * @param sheetName sheet名
     * @param data      数据
     * @param clazz     模板表
     */
    public static <T> void export(String fileName, String sheetName, List<T> data, Class<T> clazz,
            HttpServletResponse res) {
        fillResponse(res, fileName);
        try {
            EasyExcel.write(res.getOutputStream(), clazz).sheet(sheetName).doWrite(data);
        } catch (IOException e) {
            throw new BizException(e);
        }
    }
    
    
    /**
     * 导出
     *
     * @param sheetName sheet名
     * @param data      数据
     * @param clazz     模板表
     */
    public static <T> InputStream export(String sheetName, List<T> data, Class<T> clazz) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        EasyExcel.write(out, clazz).sheet(sheetName).doWrite(data);
        return new ByteArrayInputStream(out.toByteArray());
    }
    
    public static <T> InputStream export(List<T> data, Class<T> clazz) {
        return export(null, data, clazz);
    }
    
    
    /**
     * 导出
     *
     * @param sheetName sheet名
     * @param data      数据
     * @param clazz     模板表
     * @param out       输出流
     */
    public static <T> void export(String sheetName, List<T> data, Class<T> clazz, OutputStream out) {
        EasyExcel.write(out, clazz).sheet(sheetName).doWrite(data);
    }
    
    
    public static <T> void export(List<T> data, Class<T> clazz, OutputStream out) {
        export(null, data, clazz, out);
    }
    
    
    /**
     * 动态导出
     */
    public static <T> void exportResource(HttpServletResponse res, ExportDataServer<T> resource) {
        try {
            fillResponse(res, resource.info().getFileName());
            exportResource(res.getOutputStream(), resource);
        } catch (IOException e) {
            throw new BizException(e);
        }
    }
    
    /**
     * 出口资源 动态导出
     *
     * @param out      出
     * @param resource 资源
     */
    public static <T> void exportResource(OutputStream out, ExportDataServer<T> resource) {
        ExcelWriterBuilder write = EasyExcel.write(out);
        //设置样式
        if (!ObjectUtils.isEmpty(resource.info()) && !ObjectUtils.isEmpty(resource.info().getStyle())) {
            write.registerWriteHandler(resource.info().getStyle());
        }
        List<List<Object>> data = resource.resource().stream().map(it -> resource.data(it).build())
                .collect(Collectors.toList());
        write.head(resource.head().build())
                .sheet(ObjectUtils.isEmpty(resource.info()) ? null : resource.info().getSheetName()).doWrite(data);
    }
    
    
    /**
     * 动态导出
     */
    public static <T> InputStream exportResource(ExportDataServer<T> resource) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        exportResource(out, resource);
        return new ByteArrayInputStream(out.toByteArray());
    }
    
    
    /**
     * 设置表格格式 - example
     */
    public static HorizontalCellStyleStrategy createCellStyle() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 20);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 20);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
    
    
    /**
     * 初始化HttpServletResponse
     */
    public static void fillResponse(HttpServletResponse res, String fileName) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        
        fileName = !ObjectUtils.isEmpty(fileName) ? fileName.replace(XLSX, "") : "";
        fileName = dateFormat.format(new Date()) + fileName + XLSX;
        res.addHeader("Content-Disposition", "filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        //设置类型，扩展名为.xls
        res.setContentType("application/vnd.ms-excel");
    }
    
    /**
     * 初始化HttpServletResponse
     *
     * @param fileName 文件名字
     * @param timePrefix 是否生成时间前缀 格式：yyyy-MM-dd HH:mm:ss
     */
    public static void fillResponseZip(HttpServletResponse res, String fileName, boolean timePrefix) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String suffix = FileUtil.getPrefix(fileName) + ZIP;
        fileName = timePrefix ? dateFormat.format(new Date()) + suffix : suffix;
        res.addHeader("Content-Disposition", "filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        //设置类型，扩展名为.zip
        res.setContentType("application/octet-stream;charset=UTF-8");
    }


}
