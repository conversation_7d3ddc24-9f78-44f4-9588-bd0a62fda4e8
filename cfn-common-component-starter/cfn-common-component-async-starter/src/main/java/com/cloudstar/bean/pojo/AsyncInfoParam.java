package com.cloudstar.bean.pojo;


import com.cloudstar.bean.enums.BizOperationType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 异步基础参数
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AsyncInfoParam {
    
    /**
     * 用户id
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户类型
     */
    private String userType;
    
    /**
     * 组织id
     */
    private Long orgSid;


    /**
     * 接收邮箱
     */
    private String email;


    /**
     * 操作类型
     */
    private BizOperationType operationType;
    
}
