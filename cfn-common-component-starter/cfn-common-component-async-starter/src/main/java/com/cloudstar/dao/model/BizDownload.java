package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cloudstar.bean.enums.BizOperationType;
import com.cloudstar.bean.enums.DownloadStatus;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptClass;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptField;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 异步下载任务
 *
 * @TableName biz_download
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EncryptDecryptClass
public class BizDownload implements Serializable {
    
    /**
     * 下载ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long downloadId;
    
    /**
     * 单号
     */
    private String downloadNum;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户类型
     */
    private String userType;
    
    /**
     * 组织ID
     */
    private Long orgSid;
    
    /**
     * 操作类型(根据业务而定)
     */
    private BizOperationType operationType;
    
    /**
     * 下载路径
     */
    @EncryptDecryptField
    private String downloadPath;
    
    /**
     * 请求参数
     */
    private String param;
    
    /**
     * 文件名
     */
    @EncryptDecryptField
    private String fileName;

    /**
     * 加密密码
     */
    @EncryptDecryptField
    private String compressPassword;
    
    /**
     * 状态（DOWNLOADING下载中，SUCCESS下载成功，FAILED下载失败）
     */
    private DownloadStatus status;
    
    /**
     * 下载结果描述
     */
    private String remark;
    
    /**
     * 乐观锁
     */
    private String version;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private Date createdDt;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 更新时间
     */
    private Date updatedDt;
    
    private static final long serialVersionUID = 1L;

}