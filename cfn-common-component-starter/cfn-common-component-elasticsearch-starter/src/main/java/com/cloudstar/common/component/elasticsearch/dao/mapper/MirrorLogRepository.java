package com.cloudstar.common.component.elasticsearch.dao.mapper;

import com.cloudstar.common.component.elasticsearch.dao.model.MirrorLog;

import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 镜像日志Repository
 *
 * <AUTHOR>
 * @date 2025/2/7 14:11
 */
@Repository
public interface MirrorLogRepository extends ElasticsearchRepository<MirrorLog, String> {

    List<MirrorLog> findByMirrorNameOrderByTimestampDesc(String mirrorName);
}
