package com.cloudstar.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cloudstar.bean.req.NotifyRecordRequest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.SysMNotifyRecordMapper;
import com.cloudstar.dao.model.SysMNotifyRecord;
import com.cloudstar.service.SysMNotifyRecordService;

import org.springframework.stereotype.Service;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 系统消息通知实现
 *
 * <AUTHOR>
 * @description 针对表【sys_m_notify_record(系统消息通知记录表)】的数据库操作Service实现
 * @createDate 2022-09-20 15:27:52
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class SysMNotifyRecordServiceImpl extends ServiceImpl<SysMNotifyRecordMapper, SysMNotifyRecord> implements
        SysMNotifyRecordService {

    SysMNotifyRecordMapper sysMNotifyRecordMapper;

    @Override
    public Page<SysMNotifyRecord> getNotifyRecordPage(NotifyRecordRequest notifyRecordRequest, PageForm pageForm) {
        Page<SysMNotifyRecord> sysMNotifyRecordPage = sysMNotifyRecordMapper.selectPage(pageForm.pageRequest(),
                                                                                        SpecWrapperUtil.filter(
                                                                                                notifyRecordRequest));

        return sysMNotifyRecordPage;
    }
}




