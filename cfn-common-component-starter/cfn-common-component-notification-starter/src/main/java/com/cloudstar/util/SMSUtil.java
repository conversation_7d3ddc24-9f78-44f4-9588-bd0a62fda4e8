package com.cloudstar.util;

import com.cloudstar.bean.pojo.SmsAliParam;
import com.cloudstar.bean.pojo.SmsHuaweiParam;
import com.cloudstar.config.SmsConfig;
import com.cloudstar.dao.model.SysMSmsTemplate;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.net.URL;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.micrometer.core.instrument.util.StringEscapeUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * 短信工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SMSUtil extends SmsBaseUtil {

    private static final String AUTH_HEADER_VALUE = "WSSE realm=\"SDP\",profile=\"UsernameToken\",type=\"Appkey\"";


    /**
     * aliyun-发送短信
     *
     * @param phone 接收人手机号
     * @param templateCode 模板编码
     * @param templateParams 模板参数
     * @param smsConfig 短信配置
     */
    public static boolean sendAliSms(String phone, String templateCode, Map<String, String> templateParams,
                                     SmsConfig smsConfig) {
        return sendAliSms(phone, templateCode, templateParams, smsConfig.createSmsAliParam());
    }


    /**
     * aliyun-发送短信
     *
     * @param phone 接收人手机号
     * @param templateCode 模板编码
     * @param templateParams 模板参数
     * @param smsAliParam 短信配置参数（需自己构建）
     */
    public static boolean sendAliSms(String phone, String templateCode, Map<String, String> templateParams,
                                     SmsAliParam smsAliParam) {
        if (!smsAliParam.isEnable()) {
            return false;
        }
        try {
            //初始化短信参数
            String param = initAliSmsParams(phone, templateCode, templateParams, smsAliParam);
            //初始化签名
            String signature = initAliSmsSign(param, smsAliParam.getSecretKey());
            //请求
            String req = "Signature=" + signature + "&" + param;
            String result = sendSmsBase(smsAliParam.getUrl(), req);
            return "OK".equals(result);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("ali sms send failed:{}", e.getMessage());
            return false;
        }
    }


    /**
     * huawei-发送短信
     *
     * @param phone 接收人手机号
     * @param template 模板
     * @param templateParams 模板参数
     * @param smsConfig 短信配置
     */
    public static boolean sendHuaweiSms(String phone, SysMSmsTemplate template, Map<String, String> templateParams,
                                        SmsConfig smsConfig) {
        return sendHuaweiSms(phone, template, templateParams, smsConfig.createSmsHuaweiParam());
    }

    /**
     * huawei-发送短信
     *
     * @param phone 接收人手机号
     * @param template 模板
     * @param templateParams 模板参数
     * @param smsHuaweiParam 短信配置参数（需自己构建）
     */

    @SuppressFBWarnings("URLCONNECTION_SSRF_FD")
    public static boolean sendHuaweiSms(String phone, SysMSmsTemplate template, Map<String, String> templateParams,
                                        SmsHuaweiParam smsHuaweiParam) {
        //选填,短信状态报告接收地址,推荐使用域名,为空或者不填表示不接收状态报告
        String statusCallBack = "";
        //String templateParas = "[\"369751\"}]"; 模板变量，此处以单变量验证码短信为例，请客户自行生成6位验证码，并定义为字符串类型，以杜绝首位0丢失的问题（例如：002569变成了2569）。
        String templateParas = createTemplateParas(template, templateParams);
        //请求Body,不携带签名名称时,signature请填null
        String body = buildRequestBody(smsHuaweiParam.getSignNumber(),
                                                   phone,
                                                   template.getSmsPlatformCode(),
                                                   templateParas, statusCallBack,
                                                   smsHuaweiParam.getSign());
        if (ObjectUtils.isEmpty(body)) {
            log.debug("huawei sms body is null");
            return false;
        }
        //请求Headers中的X-WSSE参数值
        String wsseHeader = buildWsseHeader(smsHuaweiParam.getAppKey(), smsHuaweiParam.getAppSecret());
        if (ObjectUtils.isEmpty(wsseHeader)) {
            log.debug("huawei sms wsse header is null");
            return false;
        }
        Writer out = null;
        BufferedReader in = null;
        StringBuffer result = new StringBuffer();
        HttpsURLConnection connection = null;
        InputStream is = null;


        HostnameVerifier hv = new HostnameVerifier() {

            @SuppressFBWarnings("WEAK_HOSTNAME_VERIFIER")
            @Override
            public boolean verify(String hostname, SSLSession session) {
                return true;
            }
        };
        try {
            trustAllHttpsCertificates();
        }catch (Exception e){
            log.error("trustAllHttpsCertificates throws Exception",e);
            return false;
        }

        try {
            URL realUrl = new URL(smsHuaweiParam.getUrl());
            connection = (HttpsURLConnection) realUrl.openConnection();

            connection.setHostnameVerifier(hv);
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(true);
            //请求方法
            connection.setRequestMethod("POST");
            //请求Headers参数
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Authorization", AUTH_HEADER_VALUE);
            connection.setRequestProperty("X-WSSE", wsseHeader);

            connection.connect();
            OutputStream outputStream = connection.getOutputStream();
            out = new OutputStreamWriter(outputStream,"UTF-8");
            out.write(body); //发送请求Body参数
            out.flush();
            out.close();

            int status = connection.getResponseCode();
            if (200 == status) { //200
                is = connection.getInputStream();
            } else { //400/401
                is = connection.getErrorStream();
            }
            in = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            String line = "";
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            //打印响应消息实体
            log.info("响应消息实体:{}", StringEscapeUtils.escapeJson(result.toString()));
            JSONObject resultJB = JSONUtil.parseObj(result.toString());
            String code = resultJB.getStr("code");
            if("000000".equals(code)){
                return true;
            }else{
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (null != out) {
                    out.close();
                }
                if (null != is) {
                    is.close();
                }
                if (null != in) {
                    in.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }





}
