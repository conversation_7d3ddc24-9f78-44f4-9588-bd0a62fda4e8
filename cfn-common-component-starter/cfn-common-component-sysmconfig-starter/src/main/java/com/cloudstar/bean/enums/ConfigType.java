package com.cloudstar.bean.enums;


import com.cloudstar.config.AgentLoginConfig;
import com.cloudstar.config.AgentSysEnvConfig;
import com.cloudstar.config.BmsJobMetricsConfig;
import com.cloudstar.config.BssAiMarketConfig;
import com.cloudstar.config.BssClientConfig;
import com.cloudstar.config.CertExpireWarnConfig;
import com.cloudstar.config.CoordinationConfig;
import com.cloudstar.config.EmailServerConfig;
import com.cloudstar.config.FileSizeConfig;
import com.cloudstar.config.HcsClientConfig;
import com.cloudstar.config.HigressConfig;
import com.cloudstar.config.K8sYamlConfig;
import com.cloudstar.config.LicenseConfig;
import com.cloudstar.config.LoginAccountConfig;
import com.cloudstar.config.LoginTipsConfig;
import com.cloudstar.config.MenuDevConfig;
import com.cloudstar.config.MinioConfig;
import com.cloudstar.config.NexusAdminConfig;
import com.cloudstar.config.NotebookSshConfig;
import com.cloudstar.config.ObsAdminConfig;
import com.cloudstar.config.ObsTemporaryVoucherConfig;
import com.cloudstar.config.PasswordStrategyConfig;
import com.cloudstar.config.PasswordValidTimeConfig;
import com.cloudstar.config.SelfMonitorConfig;
import com.cloudstar.config.ServiceAccountRoleConfig;
import com.cloudstar.config.SlurmCommandTemplateConfig;
import com.cloudstar.config.SmsConfig;
import com.cloudstar.config.SynergyBigScreenConfig;
import com.cloudstar.config.SystemConfig;
import com.cloudstar.config.UserAuthSizeLimitConfig;
import com.cloudstar.config.BusinessConfig;
import com.cloudstar.config.MyOperationManagementConfig;
import com.cloudstar.config.OperationManagementConfig;
import com.cloudstar.config.IntelligentAssistantConfig;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * sys_m_config key
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public class ConfigType<T> {

    public static final ConfigType<SmsConfig> SMS_CONFIG =
            new ConfigType<>("短信配置", SmsConfig.class);
    public static final ConfigType<LoginAccountConfig> LOGIN_ACCOUNT_CONFIG =
            new ConfigType<>("用户登录配置", LoginAccountConfig.class);
    public static final ConfigType<PasswordStrategyConfig> PASSWORD_STRATEGY_CONFIG =
            new ConfigType<>("密码校验规则", PasswordStrategyConfig.class);
    public static final ConfigType<PasswordValidTimeConfig> PASSWORD_VALID_TIME_CONFIG =
            new ConfigType<>("密码天数校验配置", PasswordValidTimeConfig.class);
    public static final ConfigType<UserAuthSizeLimitConfig> USER_AUTH_SIZE_LIMIT_CONFIG =
            new ConfigType<>("用户授权配置", UserAuthSizeLimitConfig.class);
    public static final ConfigType<MinioConfig> MINIO_CONFIG =
            new ConfigType<>("minio配置", MinioConfig.class);
    public static final ConfigType<EmailServerConfig> EMAIL_SERVER_CONFIG =
            new ConfigType<>("邮件服务配置", EmailServerConfig.class);
    public static final ConfigType<FileSizeConfig> FILE_SIZE_CONFIG =
            new ConfigType<>("文件大小限制配置", FileSizeConfig.class);
    public static final ConfigType<MenuDevConfig> MENU_DEV_CONFIG =
            new ConfigType<>("菜单管理开发模式开关", MenuDevConfig.class);
    public static final ConfigType<LoginTipsConfig> LOGIN_TIPS_CONFIG =
            new ConfigType<>("登录提示页面", LoginTipsConfig.class);
    public static final ConfigType<SystemConfig> SYSTEM_CONFIG =
            new ConfigType<>("基础配置", SystemConfig.class);
    public static final ConfigType<CertExpireWarnConfig> CERT_EXPIRE_WARN_CONFIG =
            new ConfigType<>("安全证书预警配置", CertExpireWarnConfig.class);
    public static final ConfigType<ObsTemporaryVoucherConfig> OBS_TEMPORARY_VOUCHER_CONFIG =
            new ConfigType<>("obs临时授权访问配置", ObsTemporaryVoucherConfig.class);
    public static final ConfigType<SynergyBigScreenConfig> SYNERGY_BIG_SCREEN_CONFIG =
            new ConfigType<>("协同作业大屏配置", SynergyBigScreenConfig.class);
    public static final ConfigType<CoordinationConfig> COORDINATION_CONFIG =
            new ConfigType<>("协同作业配置", CoordinationConfig.class);
    public static final ConfigType<LicenseConfig> LICENSE_CONFIG =
            new ConfigType<>("许可证配置", LicenseConfig.class);
    public static final ConfigType<SlurmCommandTemplateConfig> SLURM_COMMAND_TEMPLATE_CONFIG =
            new ConfigType<>("商汤默认命令配置", SlurmCommandTemplateConfig.class);
    public static final ConfigType<BssClientConfig> BSS_CLIENT_CONFIG =
            new ConfigType<>("BSS客户端配置", BssClientConfig.class);
    public static final ConfigType<ObsAdminConfig> OBS_ADMIN_CONFIG =
            new ConfigType<>("OBS管理员配置", ObsAdminConfig.class);
    public static final ConfigType<NexusAdminConfig> NEXUS_ADMIN_CONFIG =
            new ConfigType<>("Nexus管理员配置", NexusAdminConfig.class);

    public static final ConfigType<AgentLoginConfig> AGENT_LOGIN_CONFIG =
            new ConfigType<>("agent登录配置", AgentLoginConfig.class);

    public static final ConfigType<AgentSysEnvConfig> AGENT_SYS_ENV_CONFIG =
            new ConfigType<>("agent环境配置", AgentSysEnvConfig.class);

    public static final ConfigType<BmsJobMetricsConfig> BMS_JOB_METRICS_CONFIG =
            new ConfigType<>("裸金属作业监控配置", BmsJobMetricsConfig.class);

    public static final ConfigType<NotebookSshConfig> NOTEBOOK_SSH_CONFIG =
            new ConfigType<>("开发环境SSH配置", NotebookSshConfig.class);

    public static final ConfigType<BssAiMarketConfig> BSS_AI_MARKET_CONFIG =
            new ConfigType<>("BSS模型集市相关配置", BssAiMarketConfig.class);

    public static final ConfigType<HcsClientConfig> HCS_CLIENT_CONFIG =
            new ConfigType<>("HCS客户端相关配置", HcsClientConfig.class);

    public static final ConfigType<BusinessConfig> BUSINESS_CONFIG =
            new ConfigType<>("业务配置", BusinessConfig.class);

    public static final ConfigType<IntelligentAssistantConfig> INTELLIGENT_ASSISTANT_CONFIG =
            new ConfigType<>("智能助手配置", IntelligentAssistantConfig.class);

    public static final ConfigType<OperationManagementConfig> OPERATION_MANAGEMENT_CONFIG =
            new ConfigType<>("运营支撑管理端配置", OperationManagementConfig.class);

    public static final ConfigType<MyOperationManagementConfig> MY_OPERATION_MANAGEMENT_CONFIG =
            new ConfigType<>("运营支撑管理端配置", MyOperationManagementConfig.class);

    public static final ConfigType<ServiceAccountRoleConfig> SERVICE_ACCOUNT_ROLE_CONFIG =
            new ConfigType<>("服务账户角色配置", ServiceAccountRoleConfig.class);

    public static final ConfigType<SelfMonitorConfig> SELF_MONITOR_CONFIG_CONFIG_TYPE =
            new ConfigType<>("自监控配置", SelfMonitorConfig.class);
    public static final ConfigType<K8sYamlConfig> K8S_YAML_CONFIG =
            new ConfigType<>("k8s yaml配置", K8sYamlConfig.class);
    public static final ConfigType<HigressConfig> HIGRESS_CONFIG =
            new ConfigType<>("higress配置", HigressConfig.class);

    /**
     * 名称
     */
    private final String name;
    /**
     * 映射表类型
     */
    private final Class<T> clazz;

    /**
     * 获取所有配置
     */
    public static List<Class<?>> classValues() {
        Field[] fields = ConfigType.class.getDeclaredFields();
        List<Class<?>> classes = new ArrayList<>();
        for (Field field : fields) {
            field.setAccessible(true);
            if (!field.getType().equals(ConfigType.class)) {
                continue;
            }
            try {
                Class clazz = ((ConfigType) field.get(null)).getClazz();
                classes.add(clazz);
            } catch (IllegalAccessException e) {
                continue;
            }
        }
        return classes;
    }

}
