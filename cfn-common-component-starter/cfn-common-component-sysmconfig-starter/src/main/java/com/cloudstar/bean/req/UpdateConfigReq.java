package com.cloudstar.bean.req;


import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统配置共通请
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateConfigReq {

    /**
     * 配置SID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long configSid;

    /**
     * 配置类型
     */
    private String configType;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置Key
     */
    private String configKey;

    /**
     * 配置value
     */
    private String configValue;


}
