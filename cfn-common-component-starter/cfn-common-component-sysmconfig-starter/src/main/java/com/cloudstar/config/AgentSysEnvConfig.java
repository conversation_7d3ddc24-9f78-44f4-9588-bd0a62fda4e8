package com.cloudstar.config;

import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;

import lombok.Data;


/**
 * k8s agent环境变量信息
 *
 * <AUTHOR>
 * @date 2024/06/17
 */
@Data
@ConfigBean(type = "agent_sys_env_info", description = "agent环境变量信息", libraryName = "agent")
public class AgentSysEnvConfig {

    @ConfigDes(description = "最后一次同步时间", key = "cluster_final_sync")
    private ConfigProperty finalSync;

    @ConfigDes(description = "适配器地址", key = "cluster_gateway_address")
    private ConfigProperty gatewayAddress;

    @ConfigDes(description = "集群端口", key = "cluster_port")
    private ConfigProperty clusterPort;

    @ConfigDes(description = "云环境ak", key = "cluster_access_key")
    private ConfigProperty accessKey;

    @ConfigDes(description = "云环境sk", key = "cluster_security_key")
    private ConfigProperty securityKey;

    @ConfigDes(description = "集群名称", key = "cluster_name")
    private ConfigProperty clusterName;

    @ConfigDes(description = "心跳检测频率", key = "heart_check")
    private ConfigProperty heartCheck;

    @ConfigDes(description = "数据采集频率", key = "data_collect")
    private ConfigProperty dataCollect;

    @ConfigDes(description = "集群状态", key = "cluster_status")
    private ConfigProperty clusterStatus;

    @ConfigDes(description = "监控采集频率", key = "monitor_collect")
    private ConfigProperty monitorCollect;

    @ConfigDes(description = "集群UUID", key = "agent_cluster_uuid")
    private ConfigProperty clusterUuid;

    @ConfigDes(description = "云环境地址", key = "cluster_address")
    private ConfigProperty clusterAddress;

    @ConfigDes(description = "集群类型", key = "cluster_type")
    private ConfigProperty clusterType;

    @ConfigDes(description = "训练作业脚本下载地址", key = "download_script_url")
    private ConfigProperty downloadScriptUrl;

    @ConfigDes(description = "开发环境地址前缀", key = "notebook_prefix_url")
    private ConfigProperty notebookPrefixUrl;

    @ConfigDes(description = "对象存储地址", key = "obs.url")
    private ConfigProperty obsUrl;



}
