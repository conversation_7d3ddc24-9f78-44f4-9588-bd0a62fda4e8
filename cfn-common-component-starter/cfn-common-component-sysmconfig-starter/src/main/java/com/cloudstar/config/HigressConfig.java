package com.cloudstar.config;


import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;
import lombok.Data;

@Data
@ConfigBean(type = "higress_config", description = "higress配置")
public class HigressConfig {

    @ConfigDes(key = "higress_url", name = "k8s中的Higress console的地址")
    private ConfigProperty higressUrl;

    @ConfigDes(key = "higress_username", name = "登录用户名")
    private ConfigProperty higressUsername;

    @ConfigDes(key = "higress_password", name = "用户密码")
    private ConfigProperty higressPassword;
}
