package com.cloudstar.config;


import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ConfigBean(type = "operation_management_config", description = "运营支撑管理端配置")
public class OperationManagementConfig implements Serializable {
    @ConfigDes(key = "operation.platform", name = "是否集成运营平台")
    private ConfigProperty operationPlatform;

    @ConfigDes(key = "order.management.url", name = "订单管理地址")
    private ConfigProperty orderManagement;

    @ConfigDes(key = "income.and.expenditure.url", name = "收支明细地址")
    private ConfigProperty incomeAndExpenditure;

    @ConfigDes(key = "billing.management.url", name = "账单管理地址")
    private ConfigProperty billingManagement;

    @ConfigDes(key = "asset.approval.url", name = "资产审批地址")
    private ConfigProperty assetApproval;

    @ConfigDes(key = "asset.classification.url", name = "资产分类地址")
    private ConfigProperty assetClassification;

    @ConfigDes(key = "asset.management.url", name = "资产管理地址")
    private ConfigProperty assetManagement;

    @ConfigDes(key = "subscription.management.url", name = "订阅管理地址")
    private ConfigProperty subscriptionManagement;

    @ConfigDes(key = "operation.url", name = "运营地址")
    private ConfigProperty operation;
}
