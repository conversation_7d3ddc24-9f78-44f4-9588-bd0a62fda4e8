/**
 * Copyright (c) 2018—2022 CloudStar.Co.Ltd. All rights reserved.
 */

package com.cloudstar.common.util.support;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 凭证
 *
 * <AUTHOR>
 * @date 2022-08-17
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
public class Credential {
    
    /**
     * 机密数据
     */
    String secretData;
    
    /**
     * 盐
     */
    String salt;
    
    /**
     * 哈希迭代
     */
    int hashIteration;
    
    /**
     * 算法
     */
    String algorithm;
}
