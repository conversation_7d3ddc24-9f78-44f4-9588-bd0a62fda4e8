package com.cloudstar.common.util.page;


import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;

import org.apache.ibatis.reflection.property.PropertyNamer;
import org.springframework.util.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 分页传参
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageForm {


    /**
     * 页码
     */
    private int pageNo = 0;

    /**
     * 每页大小
     */
    private int pageSize = 10;


    /**
     * 排序
     */
    private String sortDataField;


    /**
     * 是否升序 true:升序，false:降序
     */
    private Boolean asc;


    /**
     * 查询时间
     *
     * <p>Example:
     *
     * <p>"createTime,开始时间戳1,结束时间戳2;updateTime,开始时间戳1,结束时间戳2;"
     *
     * <p>注：可以是时间戳，也可以是YYYY-mm-dd HH:mm:ss格式字符串。
     *
     * <p>---注意时间戳必须是毫秒级；
     *
     * <p>注：SpecWrapperUtil 工具专用
     */
    private String times;


    /**
     * 查询的多个字段排序
     *
     * @param sortDataField 需要排序的字段
     *
     * @return 分页
     */
    public <T> Page<T> pageRequest(String... sortDataField) {
        Page<T> tPage = new Page<>(pageNo, pageSize);
        for (String s : sortDataField) {
            OrderItem order = new OrderItem(SpecWrapperUtil.camelToUnderline(s), false);
            tPage.addOrder(order);
        }
        return tPage;
    }

    /**
     * 查询的多个字段排序
     *
     * @param asc 升序
     * @param sortDataField 排序的字段
     */
    public <T> Page<T> pageRequest(boolean asc, String... sortDataField) {
        Page<T> tPage = new Page<>(pageNo, pageSize);
        for (String s : sortDataField) {
            OrderItem order = new OrderItem(SpecWrapperUtil.camelToUnderline(s), !ObjectUtils.isEmpty(asc) && asc);
            tPage.addOrder(order);
        }
        return tPage;
    }

    /**
     * 默认查询
     *
     * @param <T> 类型
     *
     * @return 分页
     */
    public <T> Page<T> pageRequest() {
        Page<T> page = new Page<>(pageNo, pageSize);
        if (!ObjectUtils.isEmpty(sortDataField)) {
            OrderItem order = new OrderItem(SpecWrapperUtil.camelToUnderline(sortDataField), asc);
            page.addOrder(order);
        }
        return page;
    }

    /**
     * 查询的多个字段排序<br> example: pageRequest(ManagerEntity::getSort);
     *
     * @param sortDataFieldFunc 排序字段
     */
    @SafeVarargs
    public final <T> Page<T> pageRequest(SFunction<T, ?>... sortDataFieldFunc) {
        return pageRequest(true, sortDataFieldFunc);
    }


    /**
     * 查询的多个字段排序<br>
     *
     * <p>example:<br>
     * pageRequest(true, ManagerEntity::getSort);
     *
     * @param asc 升序
     * @param sortDataFieldFunc 排序字段
     */
    @SafeVarargs
    public final <T> Page<T> pageRequest(boolean asc, SFunction<T, ?>... sortDataFieldFunc) {
        Page<T> page = new Page<>(pageNo, pageSize);
        for (SFunction<T, ?> function : sortDataFieldFunc) {
            LambdaMeta extract = LambdaUtils.extract(function);
            String fieldName = PropertyNamer.methodToProperty(extract.getImplMethodName());
            OrderItem order = new OrderItem(SpecWrapperUtil.camelToUnderline(fieldName), asc);
            page.addOrder(order);
        }
        return page;
    }

}
