package com.cloudstar.common.util.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudstar.common.util.page.PageForm;

import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * mybatis 快速条件构造器
 *
 * <AUTHOR>
 */
public class SpecWrapperUtil {

    //需转的下划线驼峰字母
    private static final Pattern HUMP_PATTERN = Pattern.compile("[A-Z]");

    //默认时间格式
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Getter
    @AllArgsConstructor
    private enum QueryWrapperType {
        AND("并且"),
        OR("或者");

        private final String name;
    }


    /**
     * 根据请求体，自动构建wrapper参数，and拼接
     */
    public static <T> QueryWrapper<T> filter(Object obj) {
        return createWrapper(obj, QueryWrapperType.AND);
    }


    /**
     * 根据请求体，自动构建wrapper参数，and拼接
     */
    public static <T> QueryWrapper<T> filter(Object obj, PageForm pageForm) {
        // addTimes(pageForm.getTimes(), wrapper, QueryWrapperType.AND);
        return createWrapper(obj, QueryWrapperType.AND);
    }


    /**
     * 根据请求体，自动构建wrapper参数，or拼接
     */
    public static <T> QueryWrapper<T> filterOr(Object obj) {
        return createWrapper(obj, QueryWrapperType.OR);
    }

    /**
     * 根据请求体，自动构建wrapper参数，or拼接
     */
    public static <T> QueryWrapper<T> filterOr(Object obj, PageForm pageForm) {
        QueryWrapper<T> wrapper = createWrapper(obj, QueryWrapperType.OR);
        addTimes(pageForm.getTimes(), wrapper, QueryWrapperType.OR);
        return wrapper;
    }


    /**
     * 根据请求体，自动构建wrapper 适用 young，其他人勿用
     */
    public static <T> QueryWrapper<T> exist(Object obj) {
        QueryWrapper<T> wrapper = createWrapper(obj, QueryWrapperType.AND);
        wrapper.eq("presence_status", 1);
        return wrapper;
    }


    /**
     * 根据请求体，自动构建wrapper 适用 young，其他人勿用
     */
    public static <T> QueryWrapper<T> exist(Object obj, PageForm pageForm) {
        QueryWrapper<T> wrapper = createWrapper(obj, QueryWrapperType.AND);
        wrapper.eq("presence_status", 1);
        addTimes(pageForm.getTimes(), wrapper, QueryWrapperType.AND);
        return wrapper;
    }


    /**
     * 快速构建wrapper
     */
    public static <T> QueryWrapper<T> builder() {
        return new QueryWrapper<T>();
    }


    /**
     * 快速构建lambda
     */
    public static <T> LambdaQueryWrapper<T> lambda() {
        return new LambdaQueryWrapper<T>();
    }

    /**
     * 构建条件
     */
    private static <T> QueryWrapper<T> createWrapper(Object obj, QueryWrapperType type) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        Field[] declaredFields = obj.getClass().getSuperclass().getDeclaredFields();
        if (!ObjectUtils.isEmpty(declaredFields)) {
            addWrapper(declaredFields, obj, wrapper, type);
        }
        Field[] fields = obj.getClass().getDeclaredFields();
        if (!ObjectUtils.isEmpty(fields)) {
            addWrapper(fields, obj, wrapper, type);
        }
        return wrapper;
    }


    /**
     * 添加条件参数
     */
    private static <T> void addWrapper(Field[] fields, Object obj, QueryWrapper<T> wrapper, QueryWrapperType type) {
        for (Field field : fields) {
            String name = field.getName();
            //忽略列
            if (isIgnore(name)) {
                continue;
            }
            try {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (ObjectUtils.isEmpty(value)) {
                    continue;
                }
                //填充时间查询
                if ("times".equals(name)) {
                    addTimes(value, wrapper, type);
                    continue;
                }
                //填充查询条件
                addQuery(type, field, name, value, wrapper);
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 忽略的列名
     */
    private static boolean isIgnore(String name) {
        return "serialVersionUID".equals(name) || "pageNo".equals(name) || "pageSize".equals(name)
                || "sortDataField".equals(name) || "asc".equals(name);
    }

    /**
     * 填充查询条件
     */
    private static <T> void addQuery(QueryWrapperType type, Field field, String name, Object value,
                                     QueryWrapper<T> wrapper) {
        name = camelToUnderline(name);
        switch (type) {
            case AND:
                if (field.getType().equals(String.class)) {
                    wrapper.like(name, (String) value);
                } else if (field.getType().equals(List.class)) {
                    wrapper.in(name, (List) value);
                } else {
                    wrapper.eq(name, value);
                }
                break;
            case OR:
                if (field.getType().equals(String.class)) {
                    wrapper.or().like(name, (String) value);
                } else if (field.getType().equals(List.class)) {
                    wrapper.or().in(name, (List) value);
                } else {
                    wrapper.or().eq(name, value);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 填充时间条件
     */
    private static <T> void addTimes(Object value, QueryWrapper<T> wrapper, QueryWrapperType type) {
        if (ObjectUtils.isEmpty(value)) {
            return;
        }
        List<TimesItem> times = parseTimes((String) value);
        if (ObjectUtils.isEmpty(times)) {
            return;
        }
        times.forEach(v -> {
            switch (type) {
                default:
                case AND:
                    if (ObjectUtils.isEmpty(v.getEnd())) {
                        wrapper.ge(v.getName(), v.getStart());
                    } else {
                        wrapper.between(v.getName(), v.getStart(), v.getEnd());
                    }
                    break;
                case OR:
                    if (ObjectUtils.isEmpty(v.getEnd())) {
                        wrapper.or().ge(v.getName(), v.getStart());
                    } else {
                        wrapper.or().between(v.getName(), v.getStart(), v.getEnd());
                    }
                    break;
            }
        });
    }


    /**
     * 解析时间条件
     */
    public static List<TimesItem> parseTimes(String val) {
        String[] valSplit = val.split(";");
        List<TimesItem> times = new ArrayList<>();
        for (String time : valSplit) {
            String[] split = time.split(",");
            if (split.length < 2) {
                continue;
            }
            //长度2，只填充开始时间
            if (split.length == 2) {
                times.add(TimesItem.builder().name(camelToUnderline(split[0])).start(setTimeHours(split[1])).build());
                continue;
            }
            //长度3，填充开始和结束时间
            times.add(TimesItem.builder().name(camelToUnderline(split[0])).start(setTimeHours(split[1]))
                               .end(setTimeHours(split[2])).build());
        }
        return times;
    }

    /**
     * 时间戳转换
     */
    private static Date setTimeHours(String date) {
        final SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        try {
            long time = Long.parseLong(date);
            //后边的时间填充24小时
            Calendar instance = Calendar.getInstance();
            instance.setTimeInMillis(time);
            return instance.getTime();
        } catch (Exception e) {
            DateTime dateTime = DateUtil.parse(date);
            return dateTime;
        }
    }


    /**
     * 字符串转下滑驼峰("_")
     */
    public static String camelToUnderline(String str) {
        Matcher matcher = HUMP_PATTERN.matcher(str);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            matcher.appendReplacement(result, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(result);
        return result.toString();
    }


}
