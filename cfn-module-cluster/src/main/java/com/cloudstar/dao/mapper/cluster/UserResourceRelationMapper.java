package com.cloudstar.dao.mapper.cluster;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.dao.model.cluster.UserResourceRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与资源池资源关系映射器
 *
 * <AUTHOR>
 * @date 2023/04/24
 */
public interface UserResourceRelationMapper extends BaseMapper<UserResourceRelation> {
    /**
     * 更新批处理选择性
     *
     * @param list 列表
     * @return int
     */
    int updateBatchSelective(List<UserResourceRelation> list);

    /**
     * 批量插入
     *
     * @param list 列表
     * @return int
     */
    int batchInsert(@Param("list") List<UserResourceRelation> list);
}
