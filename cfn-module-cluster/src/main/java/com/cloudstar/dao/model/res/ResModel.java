package com.cloudstar.dao.model.res;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 模型信息表
 *
 * <AUTHOR>
 * @TableName res_model
 */
@Data
@TableName(value = "res_model")
public class ResModel implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -5485259044399761288L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long ownerId;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 模型名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 部署类型
     */
    private String deployType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 备注
     */
    private String remark;

    private Long clusterId;

}
