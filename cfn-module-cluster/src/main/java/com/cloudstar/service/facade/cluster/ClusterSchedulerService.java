package com.cloudstar.service.facade.cluster;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.cluster.ClusterScheduler;
import com.cloudstar.service.pojo.dto.cluster.ClusterSchedulerResourceGroupDto;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryClusterSchedulerReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterSchedulerResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterSchedulerWithResourceGroupResp;

import java.util.List;

/**
 * 集群调度器服务接口
 */
public interface ClusterSchedulerService extends IService<ClusterScheduler> {


    ClusterSchedulerWithResourceGroupResp getSchedulersWithResourceGroups(Long schedulerId);

    List<ClusterSchedulerResourceGroupDto> getResourceGroupsBySchedulerIds(List<Long> schedulerIds);


    /**
     * 分页查询调度器信息
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     *
     * @return 分页结果
     */
    PageResult<ClusterSchedulerResp> page(QueryClusterSchedulerReq req, PageForm pageForm);
}