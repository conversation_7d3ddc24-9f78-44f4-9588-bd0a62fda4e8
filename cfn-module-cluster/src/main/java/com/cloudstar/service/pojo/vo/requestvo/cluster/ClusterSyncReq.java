package com.cloudstar.service.pojo.vo.requestvo.cluster;


import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 集群同步请求类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class ClusterSyncReq {

    @NotNull
    private Long clusterId;

    /**
     * 类型 cluster:集群同步 clusterAccount:集群账号同步 clusterEngine:集群引擎同步 clusterFlavor:集群资源同步
     */
    @NotEmpty
    private String event;
}
