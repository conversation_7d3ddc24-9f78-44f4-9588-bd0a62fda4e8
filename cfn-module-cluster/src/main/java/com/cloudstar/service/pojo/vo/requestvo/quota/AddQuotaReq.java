package com.cloudstar.service.pojo.vo.requestvo.quota;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 添加配额
 *
 * <AUTHOR>
 * @date 2025/2/10 15:00
 */
@Data
public class AddQuotaReq {

    /**
     * 配额名称
     */
    @NotEmpty
    private String quotaName;

    /**
     * 配额类型 default：默认配额 exclusive：专属配额
     */
    @NotEmpty
    private String quotaType;

    /**
     * 集群id
     */
    @NotNull
    private Long clusterId;

    /**
     * 备注
     */
    private String remark;
}
