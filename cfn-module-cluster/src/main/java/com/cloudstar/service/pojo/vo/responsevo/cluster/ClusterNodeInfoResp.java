package com.cloudstar.service.pojo.vo.responsevo.cluster;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 集群引擎响应值
 */
@Data
public class ClusterNodeInfoResp {

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点架构
     */
    private String archive;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 所属资源池id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long poolId;

    /**
     * 所属资源池名称
     */
    private String poolName;

    /**
     * 计算卡型号
     */
    private String computeProductName;

    /**
     * cpu配置
     */
    private String cpu;
    /**
     * 可用cpu
     */
    private String cpuAvailable;

    /**
     * 内存配置
     */
    private String memory;

    /**
     * 可用内存
     */
    private String memoryAvailable;

    /**
     * gpu配置
     */
    private String gpu;

    /**
     * 可用gpu
     */
    private String gpuAvailable;

    /**
     * npu配置
     */
    private String npu;

    /**
     * 可用npu
     */
    private String npuAvailable;

    /**
     * 存储配置
     */
    private String disk;

    /**
     * 可用存储
     */
    private String diskAvailable;

    /**
     * 节点标签
     */
    private String labels;

    /**
     * 自动同步标识  true是 false否
     */
    private Boolean isSync;

}
