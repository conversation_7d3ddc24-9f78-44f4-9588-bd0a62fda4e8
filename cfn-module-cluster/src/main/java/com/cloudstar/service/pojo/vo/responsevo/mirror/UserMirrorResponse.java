package com.cloudstar.service.pojo.vo.responsevo.mirror;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMirrorResponse {

    /**
     * 镜像id
     */
    private Long mirrorId;

    /**
     * 镜像名称
     */
    private String mirrorName;

    /**
     * 架构
     */
    private String platformArchitecture;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 训练框架
     */
    private String trainingFramework;

    /**
     * 训练驱动
     */
    private String trainingDriver;

    /**
     * 状态
     */
    private String status;

}
