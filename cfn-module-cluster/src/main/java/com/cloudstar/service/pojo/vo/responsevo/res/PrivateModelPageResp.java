package com.cloudstar.service.pojo.vo.responsevo.res;


import java.util.Date;

import lombok.Data;

/**
 * 分页返回对象
 *
 * <AUTHOR>
 * @date 2025/4/7 14:06
 */
@Data
public class PrivateModelPageResp {

    /**
     * id
     */
    private Long id;
    /**
     * 大模型id
     */
    private String modelId;
    /**
     * 大模型名称
     */
    private String modelName;
    /**
     * 参数量
     */
    private String parameterQuantity;
    /**
     * 集群id
     */
    private Long clusterId;

    private String clusterName;
    /**
     * 资源池id
     */
    private Long poolId;
    /**
     * 资源池类型
     */
    private String poolType;
    /**
     * 规格id
     */
    private Long flavorId;
    /**
     * 规格名称
     */
    private String flavorName;
    /**
     * 模型状态
     */
    private String modelStatus;

    /**
     * k8s模型id
     */
    private String k8sModelId;

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
}
