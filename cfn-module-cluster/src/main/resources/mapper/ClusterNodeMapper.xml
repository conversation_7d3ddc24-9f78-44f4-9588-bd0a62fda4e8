<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.cluster.ClusterNodeMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.cluster.ClusterNode">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="nodeId" column="node_id" jdbcType="VARCHAR"/>
        <result property="nodeName" column="node_name" jdbcType="VARCHAR"/>
        <result property="clusterId" column="cluster_id" jdbcType="BIGINT"/>
        <result property="archive" column="archive" jdbcType="VARCHAR"/>
        <result property="os" column="os" jdbcType="VARCHAR"/>
        <result property="poolId" column="pool_id" jdbcType="BIGINT"/>
        <result property="computeProductName" column="compute_product_name" jdbcType="VARCHAR"/>
        <result property="cpu" column="cpu" jdbcType="VARCHAR"/>
        <result property="cpuAvailable" column="cpu_available" jdbcType="VARCHAR"/>
        <result property="memory" column="memory" jdbcType="VARCHAR"/>
        <result property="memoryAvailable" column="memory_available" jdbcType="VARCHAR"/>
        <result property="gpu" column="gpu" jdbcType="VARCHAR"/>
        <result property="gpuAvailable" column="gpu_available" jdbcType="VARCHAR"/>
        <result property="npu" column="npu" jdbcType="VARCHAR"/>
        <result property="npuAvailable" column="npu_available" jdbcType="VARCHAR"/>
        <result property="disk" column="disk" jdbcType="VARCHAR"/>
        <result property="diskAvailable" column="disk_available" jdbcType="VARCHAR"/>
        <result property="labels" column="labels" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,node_id,node_name,
        cluster_id,archive,os,
        pool_id,compute_product_name,cpu,
        cpu_available,memory,memory_available,
        gpu,gpu_available,npu,
        npu_available,disk,disk_available,
        labels,version,created_by,
        created_dt,updated_by,updated_dt
    </sql>
    <select id="page" resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterNodeResp">
        SELECT a.id,
               a.node_id                                                    as nodeId,
               a.node_name                                                  as nodeName,
               a.archive                                                    as archive,
               a.compute_product_name                                       as computeProductName,
               a.is_sync                                                    as isSync,
               a.status                                                     as status,
               a.node_purpose   as nodePurpose,
               a.is_virtualized as isVirtualized,
               b.pool_name                                                  as poolName,
               a.node_address   as nodeAddress,
               CASE
                   WHEN a.cpu = 0
                       THEN 0
                   ELSE (1 - round(cast(a.cpu_available as numeric) / cast(a.cpu as numeric), 4))
                   END                                                      AS cpuUsage,

               CASE
                   WHEN a.gpu = 0
                       THEN 0
                   ELSE (1 - round(cast(a.gpu_available as numeric) / cast(a.gpu as numeric), 4))
                   END                                                      AS gpuUsage,

               CASE
                   WHEN a.npu = 0
                       THEN 0
                   ELSE (1 - round(cast(a.npu_available as numeric) / cast(a.npu as numeric), 4))
                   END                                                      AS npuUsage,
               CONCAT(a.cpu, 'c ', a.memory, 'GB ', a.compute_product_name) as configure
        FROM cluster_node a
                 left join cluster_resource_pool b
                           on a.pool_id = b.id
            ${ew.customSqlSegment}
    </select>

    <select id="list" resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterNodeResp">
        SELECT a.id,
               a.node_id                                                    as nodeId,
               a.node_name                                                  as nodeName,
               a.archive                                                    as archive,
               a.compute_product_name                                       as computeProductName,
               a.is_sync                                                    as isSync,
               a.status                                                     as status,
               a.node_purpose   as nodePurpose,
               a.is_virtualized as isVirtualized,
               b.pool_name                                                  as poolName,
               CASE
                   WHEN a.cpu = 0
                       THEN 0
                   ELSE (1 - round(cast(a.cpu_available as numeric) / cast(a.cpu as numeric), 4))
                   END                                                      AS cpuUsage,

               CASE
                   WHEN a.gpu = 0
                       THEN 0
                   ELSE (1 - round(cast(a.gpu_available as numeric) / cast(a.gpu as numeric), 4))
                   END                                                      AS gpuUsage,

               CASE
                   WHEN a.npu = 0
                       THEN 0
                   ELSE (1 - round(cast(a.npu_available as numeric) / cast(a.npu as numeric), 4))
                   END                                                      AS npuUsage,
               CONCAT(a.cpu, 'c ', a.memory, 'GB ', a.compute_product_name) as configure
        FROM cluster_node a
                 left join cluster_resource_pool b
                           on a.pool_id = b.id
            ${ew.customSqlSegment}
    </select>
</mapper>
