<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.res.ResObsMapper">

    <insert id="saveBatch" parameterType="java.util.List">
        insert into res_obs (owner_id, org_sid, uuid, name, use_type, access_policy, obs_sum, total_capacity,
                             used_capacity,created_by, created_dt, updated_by, updated_dt, cluster_id)
        VALUES
        <foreach collection="insertList" item="item" separator=",">
            (#{item.ownerId}, #{item.orgSid}, #{item.uuid}, #{item.name}, #{item.useType}, #{item.accessPolicy},
             #{item.obsSum}, #{item.totalCapacity}, #{item.usedCapacity},#{item.createdBy}, #{item.createdDt},
             #{item.updatedBy}, #{item.updatedDt}, #{item.clusterId})
        </foreach>
    </insert>
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update res_obs
            <set>
                <if test="item.obsSum != null and item.obsSum != ''">
                    obs_sum = #{item.obsSum},
                </if>
                <if test="item.totalCapacity != null and item.totalCapacity != ''">
                    total_capacity = #{item.totalCapacity},
                </if>
                <if test="item.usedCapacity != null and item.usedCapacity != ''">
                    used_capacity = #{item.usedCapacity},
                </if>
                <if test="item.updatedBy != null and item.updatedBy != ''">
                    updated_by = #{item.updatedBy},
                </if>
                <if test="item.updatedDt != null">
                    updated_dt = #{item.updatedDt},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>
