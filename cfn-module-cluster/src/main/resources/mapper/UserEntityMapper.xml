<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.user.UserEntityMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.user.UserEntity">
        <id property="userSid" column="user_sid" jdbcType="OTHER"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="realName" column="real_name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="SMALLINT"/>
        <result property="parentSid" column="parent_sid" jdbcType="BIGINT"/>
        <result property="authStatus" column="auth_status" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
        <result property="lastLoginIp" column="last_login_ip" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="passwordExpiresAt" column="password_expires_at" jdbcType="TIMESTAMP"/>
        <result property="isResetPassword" column="is_reset_password" jdbcType="SMALLINT"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
        <result property="accountStatus" column="account_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_sid
        ,uuid,account,
        password,real_name,email,
        mobile,user_type,parent_sid,
        auth_status,status,last_login_time,
        last_login_ip,start_time,end_time,
        password_expires_at,is_reset_password,version,
        created_by,created_dt,updated_by,
        updated_dt
    </sql>

    <sql id="Base_Column_List_1">
        A
        .
        user_sid
        ,A.uuid,A.account,
        A.password,A.real_name,A.email,
        A.mobile,A.user_type,A.parent_sid,
        A.auth_status,A.status,A.last_login_time,
        A.last_login_ip,A.start_time,A.end_time,
        A.password_expires_at,A.is_reset_password,A.version,
        A.created_by,A.created_dt,A.updated_by,
        A.updated_dt
    </sql>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="query.userSid != null">
                and A.user_sid = #{query.userSid}
            </if>
            <if test="query.account != null">
                and A.account = #{query.account}
            </if>
            <if test="query.password != null">
                and A.password = #{query.password}
            </if>
            <if test="query.realName != null">
                and A.real_name = #{query.realName}
            </if>
            <if test="query.email != null">
                and A.email = #{query.email}
            </if>
            <if test="query.mobile != null">
                and A.mobile = #{query.mobile}
            </if>
            <if test="query.groupSid != null">
                and C.group_sid = #{query.groupSid}
            </if>
            <if test="query.orgSid != null">
                and A.org_sid = #{query.orgSid}
            </if>
        </trim>
    </sql>

    <sql id="Example_Where_Clause1">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.userSid != null">
                and A.user_sid = #{condition.userSid}
            </if>
            <if test="condition.account != null">
                and A.account = #{condition.account}
            </if>
            <if test="condition.password != null">
                and A.password = #{condition.password}
            </if>
            <if test="condition.realName != null">
                and A.real_name = #{condition.realName}
            </if>
            <if test="condition.email != null">
                and A.email = #{condition.email}
            </if>
            <if test="condition.mobile != null">
                and A.mobile = #{condition.mobile}
            </if>
            <if test="condition.groupSid != null">
                and C.group_sid = #{condition.groupSid}
            </if>
        </trim>
    </sql>
    <select id="selectByParams" resultType="com.cloudstar.dao.model.user.UserEntity">
        SELECT
        distinct
        <include refid="Base_Column_List_1"/>
        FROM user_entity A
        LEFT JOIN user_entity_group b ON A.user_sid = b.user_sid
        LEFT JOIN user_group C ON b.group_sid = C.group_sid
        <if test="query != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <select id="selectByParams1" resultType="com.cloudstar.dao.model.user.UserEntity">
        SELECT
        distinct
        <include refid="Base_Column_List_1"/>
        FROM user_entity A
        LEFT JOIN user_entity_group b ON A.user_sid = b.user_sid
        LEFT JOIN user_group C ON b.group_sid = C.group_sid
        <if test="_parameter != null">
            <include refid="Example_Where_Clause1"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectAllByAccount" resultType="com.cloudstar.dao.model.user.UserEntity">
        SELECT
        user_sid,
        uuid,
        account,
        password,
        real_name,
        email,
        mobile
        FROM user_entity A
        <where>
            <if test="account != null">
                and A.account = #{account}
            </if>
            and A.status != 'DELETED'
        </where>
    </select>

    <select id="selectListByAssertionSid" resultType="java.lang.Long">
        SELECT DISTINCT a.user_sid
        FROM user_entity A,
             sys_m_policy_user b,
             sys_m_policy_assertion C
        WHERE A.user_sid = b.user_sid
          AND b.policy_sid = C.policy_sid
          and c.assertion_sid = #{assertionSid}
    </select>

    <!--  查询新增用户数量  -->
    <select id="selectUserAddNum" resultType="com.cloudstar.service.pojo.dto.user.UserAddNum">
        SELECT
        count(*) as addUserNum,
        cast(u.created_dt as date) as time
        FROM
        user_entity u
        where u.parent_sid is null
        group by cast(u.created_dt as date)
        <if test="time != null and time != ''">
            having cast(u.created_dt as date) &gt;= cast(#{time} as date)
        </if>
    </select>

    <!--  查询用户总数  -->
    <select id="selectTotalTenantNum" resultType="java.lang.Long">
        SELECT count(*) as totalTenantNum
        FROM user_entity u
        where u.parent_sid is null
    </select>

    <select id="selectExpiredUser" resultType="com.cloudstar.dao.model.user.UserEntity">
        select *
        from user_entity ue
        where password_expires_at &lt;= now();
    </select>


    <select id="selectUserByThresholdValue" resultType="com.cloudstar.dao.model.user.UserEntity">
        select *
        from user_entity
        where (select now()) between (password_expires_at - interval '${threshold}' day) and password_expires_at
          and status != 'DELETED';
    </select>

    <select id="page" resultType="com.cloudstar.service.pojo.dto.user.UserEntityResultDto">
        select A.*, B.org_name, B.address, B.contact_name, B.contact_phone, B.application_scenario, B.personnel_size,
               B.industry_type, B.remark
        from user_entity A left join sys_m_org B on A.org_sid = B.org_sid
        <where>
            <if test="query.userSid != null">
                and A.user_sid = #{query.userSid}
            </if>
            <if test="query.account != null and query.account != ''">
                and A.account like concat('%', #{query.account}::text, '%')
            </if>
            <if test="query.realName != null and query.realName != ''">
                and A.real_name = #{query.realName}
            </if>
            <if test="query.email != null and query.email != ''">
                and A.email = #{query.email}
            </if>
            <if test="query.mobile != null">
                and A.mobile = #{query.mobile}
            </if>
            <if test="query.orgSid != null">
                and A.org_sid = #{query.orgSid}
            </if>
            <if test="query.statusNotEq != null and query.statusNotEq != ''">
                and A.status != #{query.statusNotEq}
            </if>
            <if test="query.userType != null and query.userType != ''">
                and A.user_type = #{query.userType}
            </if>
            <if test="query.orgNameLike != null and query.orgNameLike != ''">
                and B.org_name like concat('%', #{query.orgNameLike}::text, '%')
            </if>
        </where>
        <if test="query.sortDataField != null and query.sortDataField != ''">
            order by ${query.sortDataField}
            <choose>
                <when test="query.asc != null and query.asc == false">
                    desc
                </when>
                <otherwise>
                    asc
                </otherwise>
            </choose>
        </if>
    </select>

    <update id="batchUpdateFreezeStatus" parameterType="java.util.List">
        <foreach collection="userList" item="item" index="index" separator=";">
            UPDATE user_entity
            <set>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status},
                </if>
                <if test="item.accountStatus != null and item.accountStatus != ''">
                    account_status = #{item.accountStatus},
                </if>
            </set>
            where user_sid = #{item.userSid}
        </foreach>
    </update>
</mapper>
