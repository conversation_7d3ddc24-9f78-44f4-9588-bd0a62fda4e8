package com.cloudstar.dao.mapper.datastorage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.dao.model.datastorage.DataStorageKey;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageKeyResp;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface DataStorageKeyMapper extends BaseMapper<DataStorageKey> {

    /**
     * 分页查询访问密钥
     *
     * @param page 分页对象
     * @param queryWrapper 查询条件
     *
     * @return 分页结果
     */
    @Select("SELECT ak.id AS id,\n"
            + "       ak.ak AS ak,\n"
            + "       ak.description AS description,\n"
            + "       ak.created_dt AS createdDt,\n"
            + "       ak.status AS status,\n"
            + "       ce.cluster_name AS clusterName\n"
            + "FROM data_storage_key ak\n"
            + "LEFT JOIN cluster_entity ce ON ak.cluster_id = ce.id\n"
            + "  ${ew.customSqlSegment}")
    Page<DataStorageKeyResp> page(Page<DataStorageKeyResp> page,
                                  @Param("ew") LambdaQueryWrapper<DataStorageKey> queryWrapper);
}