package com.cloudstar.dao.mapper.training;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.common.base.pojo.Criteria;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.sdk.consoleserver.pojo.JobStatisticsDto;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobStatisticDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobClusterStatisticsDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobEntityDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobStatusDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobStatusSumDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobTrendDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 作业mapper
 *
 * <AUTHOR>
 * @createDate 2022-08-19 14:03:57
 * @Entity com.cloudstar.dao.model.training.TrainingJobEntity
 */
@Repository
public interface TrainingJobEntityMapper extends BaseMapper<TrainingJobEntity> {

    @Select("select j.id           as id,\n"
            + "       j.name         as name,\n"
            + "       j.description  as description,\n"
            + "       j.user_sid     as userSid,\n"
            + "       j.created_by   as createdBy,\n"
            + "       j.created_dt   as createdDt,\n"
            + "       j.updated_dt   as updatedDt,\n"
            + "       j.status       as status,\n"
            + "       j.run_duration as runDuration,\n"
            + "       j.cluster_id   as clusterId,\n"
            + "       j.job_id   as jobId,\n"
            + "       j.job_create_time   as jobCreateTime,\n"
            + "       j.job_start_time   as jobStartTime,\n"
            + "       j.is_ready   as isReady,\n"
            + "       c.cluster_name as clusterName,\n"
            + "       c.cluster_type as clusterType,\n"
            + "       g.id as groupId,\n"
            + "       g.name as groupName\n"
            + "from training_job_entity j\n"
            + "         left join cluster_entity c on j.cluster_id = c.id "
            + "left join training_job_group g on j.job_group_id = g.id"
            +
            "  ${ew.customSqlSegment} NULLS LAST")
    Page<TrainingJobEntityDto> page(Page page, @Param(Constants.WRAPPER) QueryWrapper wrapper);

    // 全量查询
    @Select("select j.id           as id,\n"
            + "       j.name         as name,\n"
            + "       j.description  as description,\n"
            + "       j.user_sid     as userSid,\n"
            + "       j.created_by   as createdBy,\n"
            + "       j.created_dt   as createdDt,\n"
            + "       j.updated_dt   as updatedDt,\n"
            + "       j.status       as status,\n"
            + "       j.run_duration as runDuration,\n"
            + "       j.cluster_id   as clusterId,\n"
            + "       j.job_id   as jobId,\n"
            + "       j.job_create_time   as jobCreateTime,\n"
            + "       j.job_start_time   as jobStartTime,\n"
            + "       j.is_ready   as isReady,\n"
            + "       j.container_id   as containerId,\n"
            + "       c.cluster_name as clusterName,\n"
            + "       c.cluster_type as clusterType,\n"
            + "       g.id as groupId,\n"
            + "       g.name as groupName\n"
            + "from training_job_entity j\n"
            + "         left join cluster_entity c on j.cluster_id = c.id "
            + "left join training_job_group g on j.job_group_id = g.id"
            +
            "  ${ew.customSqlSegment}")
    List<TrainingJobEntityDto> list(@Param(Constants.WRAPPER) QueryWrapper wrapper);

    List<JobStatisticsDto> overviewStatistics(Criteria criteria);

    @Select("select j.id           as id,\n"
            + "       j.name         as name,\n"
            + "       j.description  as description,\n"
            + "       j.user_sid     as userSid,\n"
            + "       u.account     as account,\n"
            + "       j.created_by   as createdBy,\n"
            + "       j.created_dt   as createdDt,\n"
            + "       j.status       as status,\n"
            + "       j.run_duration as runDuration,\n"
            + "       j.cluster_id   as clusterId,\n"
            + "       j.job_id   as jobId,\n"
            + "       j.job_create_time   as jobCreateTime,\n"
            + "       j.job_start_time   as jobStartTime,\n"
            + "       j.container_id   as containerId,\n"
            + "       c.cluster_type as clusterType\n,"
            + "       c.cluster_name as clusterName\n"
            + "from training_job_entity j\n"
            + "         left join cluster_entity c on j.cluster_id = c.id"
            + "         left join user_entity u on u.user_sid = j.user_sid"
            +
            "  ${ew.customSqlSegment} NULLS LAST")
    Page<TrainingJobEntityDto> pageServer(Page page, @Param(Constants.WRAPPER) QueryWrapper wrapper);


    @Select("select status, count(status) as num\n"
            + "from training_job_entity\n"
            + "where cluster_id = #{clusterId}\n"
            + "  and job_type is null \n"
            + "group by status;")
    List<TrainingJobStatusSumDto> statisticsJobStatus(@Param("clusterId") Long clusterId);


    //多租户忽略拦截器
    @InterceptorIgnore(tenantLine = "true")
    @Select("select\n"
            + "COALESCE(round(avg(extract(epoch from (j.job_start_time - j.job_create_time))) / 60, 2), '0.00') as minutes\n"
            + "            from training_job_entity j\n"
            + "            where j.cluster_id =#{clusterId}\n"
            + "              and j.job_start_time is not null\n"
            + "              and j.job_create_time is not null\n"
            + "              and j.job_create_time <= j.job_start_time\n"
            + "              and j.created_dt BETWEEN (SELECT now() - INTERVAL '7 day')\n"
            + "                AND (SELECT now());")
    Double queryQueueTime(@Param("clusterId") Long clusterId);

    @Select("select status, count(status) as num\n"
            + "from training_job_entity\n"
            + "where user_sid = #{userSid}\n"
            + "group by status;")
    List<TrainingJobStatusSumDto> getUserTrainingJobStaticStatus(Long userSid);

    @Select("SELECT\n"
            + "\tstatus,count(status) as num,\n"
            + "\tCOALESCE ( SUM ( run_duration ), 0 ) AS runDurationTotal \n"
            + "FROM\n"
            + "\ttraining_job_entity  WHERE cluster_id = #{clusterId}  \n"
            + "and  status !='DELETED'\n"
            + "GROUP BY\n"
            + "\tstatus;")
    List<TrainingJobStatusDto> selectJobStatus(@Param("clusterId") Long clusterId);

    @Select("SELECT COALESCE\n"
            + "\t( A.run_duration, 0 ) AS runDuration,\n"
            + "\tCOALESCE ( A.job_type, 'GENERAL' ) AS jobType,\n"
            + "\tCOALESCE ( b.cluster_name, '其他' ) AS clusterName \n"
            + "FROM\n"
            + "\ttraining_job_entity\n"
            + "\tA LEFT JOIN cluster_entity b ON A.cluster_id = b.ID"
            + "\t where A.status !='DELETED'")
    List<TrainingJobClusterStatisticsDto> jobStatics();

    @Select("select tje.\"name\" as job_name,\n"
            + " tje.status as job_status,\n"
            + " ce.cluster_name  \n"
            + "from training_job_entity tje \n"
            + "left join cluster_entity ce \n"
            + "on tje.cluster_id = ce.id \n"
            + "where tje.job_type is null \n"
            + "and  tje.status not in ('DELETED', 'FAILED', 'SCHEDULING_FAILED')  \n"
            + "order by tje.created_dt \n"
            + "desc limit 20;")
    List<TrainingJobStatisticDto> getTrainingJobList();


    @Select("select count(distinct(user_sid)) "
            + "from training_job_entity tje "
            + "where job_start_time > now() - interval '3 days' and cluster_id = #{clusterId} and job_type is null;")
    Integer getUserCount(@Param("clusterId") Long clusterId);

    List<TrainingJobTrendDto> getJobTrend(@Param("clusterId") Long clusterId, @Param("startTime") String startTime,
                                          @Param("endTime") String endTime);

    //@Select("select count(*)  from training_job_entity tje  where cluster_id = #{clusterId} and status = #{jobStatus} and job_type is null ;")
    @Select("select count(*)  from training_job_entity tje  where cluster_id = #{clusterId} and status = #{jobStatus};")
    Integer getJobStatisticByClusterAndStatus(@Param("clusterId") Long clusterId, @Param("jobStatus") String jobStatus);

    @Select(" select count(*) from training_job_entity tje "
            + "where cluster_id = #{clusterId} and job_type is null and status !='DELETED';")
    Integer getTotalJobNumByClusterId(Long clusterId);

    @Select(" select count(*) from training_job_entity tje "
            + "where cluster_id = #{clusterId} and job_type is null and status =#{status}")
    Integer getTotalJobNumByClusterIdAndStatus(Long clusterId, String status);

    @Select("select count(*) from training_job_entity tje where  job_type is null and status !='DELETED';")
    Integer getJobCount();

    @Select("select count(*) from training_job_entity tje where  job_type is null and status !='DELETED' "
            + "and created_dt BETWEEN (SELECT now() - INTERVAL '7 day') AND (SELECT now());")
    Integer getSevenDaysJobCount();
    
}
