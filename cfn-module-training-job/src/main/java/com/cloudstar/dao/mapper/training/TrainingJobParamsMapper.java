package com.cloudstar.dao.mapper.training;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.dao.model.training.TrainingJobParams;
import com.cloudstar.service.pojo.dto.training.TrainingJobParamDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 培训工作参数映射器
 *
 * <AUTHOR>
 * @description 针对表【training_job_params(训练作业参数)】的数据库操作Mapper
 * @createDate 2022-08-24 18:01:07
 * @Entity com.cloudstar.dao.model.training.TrainingJobParams
 * @date 2022/08/25
 */
public interface TrainingJobParamsMapper extends BaseMapper<TrainingJobParams> {
    List<TrainingJobParamDto> findAllByGroupId(Long groupId);

    TrainingJobParamDto selectLogPathsByParamsGroupId(@Param("paramsGroupId") Long paramsGroupId);
}




