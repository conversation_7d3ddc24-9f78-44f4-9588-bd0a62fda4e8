package com.cloudstar.dao.model.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.ibatis.type.ArrayTypeHandler;
import org.apache.ibatis.type.JdbcType;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 培训工作小组
 * 作业组
 *
 * <AUTHOR>
 * @TableName training_job_group
 * @date 2022/09/19
 */
@Data
@TableName(value = "training_job_group", autoResultMap = true)
public class TrainingJobGroup implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 备注
     */
    @TableField(value = "description")
    private String description;

    /**
     * 所属用户组织
     */
    @TableField(value = "org_sid")
    private Long orgSid;

    /**
     * 所属用户id
     */
    @TableField(value = "user_sid")
    private Long userSid;

    /**
     * 服务端算法文件
     */
    @TableField(value = "image_server_id")
    private Long imageServerId;
    /**
     * 客户端算法文件
     */
    @TableField(value = "image_client_id", jdbcType = JdbcType.ARRAY, typeHandler = ArrayTypeHandler.class)
    private Long[] imageClientId;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 日志输出
     */
    @TableField(value = "output_log_resource")
    private Long outputLogResource;

    /**
     * 模型输出
     */
    @TableField(value = "output_model_resource")
    private Long outputModelResource;

    /**
     * 启动人
     */
    @TableField(value = "started_by")
    private String startedBy;

    /**
     * 启动时间
     */
    @TableField(value = "started_dt")
    private Date startedDt;

    /**
     * 完成时间
     */
    @TableField(value = "end_dt")
    private Date endDt;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 最近修改人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 最近修改时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    /**
     * 运行时长
     */
    @TableField(value = "run_duration")
    private Long runDuration;

    /**
     * 版本号
     */
    @TableField(value = "group_version")
    private String groupVersion;
    /**
     * 输出日志目录地址
     */
    @TableField(value = "output_log_url")
    private String outputLogUrl;
    /**
     * 输出模型目录地址
     */
    @TableField(value = "output_model_url")
    private String outputModelUrl;
    /**
     * 客户端轮询次数
     */
    @TableField(value = "max_steps")
    private Integer maxSteps;
    /**
     * 错误信息
     */
    @TableField(value = "error_msg")
    private String errorMsg;
    /**
     * 有无子作业
     */
    @TableField(exist = false)
    private Boolean hasJob;

    /**
     * 子作业集合
     */
    @TableField(exist = false)
    private List<TrainingJobEntity> taskList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}