package com.cloudstar.service.impl.datastorage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.ServiceAccountMessage;
import com.cloudstar.common.base.util.KeyUtil;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.mapper.datastorage.DataStorageKeyMapper;
import com.cloudstar.dao.mapper.tenantmapping.ClusterSubAccountMapper;
import com.cloudstar.dao.model.datastorage.DataStorageKey;
import com.cloudstar.dao.model.tenantmapping.ClusterSubAccount;
import com.cloudstar.mq.ScheduleHelper;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.sdk.server.pojo.DataStorageKeyDto;
import com.cloudstar.service.facade.datastorage.DataStorageKeyService;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageKeyReq;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageKeyResp;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.Optional;

import javax.annotation.Resource;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class DataStorageKeyServiceImpl extends ServiceImpl<DataStorageKeyMapper, DataStorageKey> implements
        DataStorageKeyService {

    @Resource
    DataStorageKeyMapper dataStorageKeyMapper;
    @Resource
    ServerClient serverClient;
    @Resource
    ClusterSubAccountMapper clusterSubAccountMapper;

    @Override
    public PageResult<DataStorageKeyResp> page(DataStorageKeyReq req, PageForm pageForm) {
        Long clusterId = req.getClusterId();
        if (clusterId == null) {
            throw new BizException("clusterId cannot be null");
        }
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = Optional.ofNullable(authUser)
                               .map(AuthUser::getUserSid)
                               .orElseThrow(() -> new BizException(BizErrorEnum.MSG_1011_TOKEN_ERROR));
        LambdaQueryWrapper<DataStorageKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataStorageKey::getClusterId, clusterId);
        queryWrapper.eq(DataStorageKey::getUserSid, userSid);
        if (req.getDescription() != null) {
            queryWrapper.like(DataStorageKey::getDescription, req.getDescription());
        }
        Page<DataStorageKeyResp> page = new Page<>(pageForm.getPageNo(), pageForm.getPageSize());
        Page<DataStorageKeyResp> dataPage = dataStorageKeyMapper.page(page, queryWrapper);
        for (DataStorageKeyResp item : dataPage.getRecords()) {
            item.setAk(CrytoUtilSimple.decrypt(item.getAk()));
        }
        return PageResult.of(dataPage, DataStorageKeyResp.class);
    }


    @Override
    public Long createDataStorageKey(DataStorageKeyDto dataStorageKeyDto) {
        DataStorageKey dataStorageKey = new DataStorageKey();
        dataStorageKey.setOrgSid(dataStorageKeyDto.getOrgSid());
        dataStorageKey.setClusterId(dataStorageKeyDto.getClusterId());
        dataStorageKey.setUserSid(dataStorageKeyDto.getUserSid());
        dataStorageKey.setAk(CrytoUtilSimple.encrypt(dataStorageKeyDto.getAk()));
        dataStorageKey.setSk(CrytoUtilSimple.encrypt(dataStorageKeyDto.getSk()));
        dataStorageKey.setDescription(dataStorageKeyDto.getDescription());
        dataStorageKey.setCreatedDt(new Date());
        dataStorageKey.setStatus(dataStorageKeyDto.getStatus());
        this.save(dataStorageKey);
        return dataStorageKey.getId();
    }


    @Override
    public boolean deleteDataStorageKey(Long id) {

        if (ObjectUtils.isEmpty(id)) {
            throw new BizException(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        DataStorageKey dataStorageKey = getDataStorageKeyById(id);
        if (ObjectUtils.isEmpty(dataStorageKey)) {
            throw new BizException("未找到对应的访问密钥");
        }
        String accessKey = CrytoUtilSimple.decrypt(dataStorageKey.getAk());
        String userId = String.valueOf(clusterSubAccountMapper.selectOne(
                new LambdaQueryWrapper<ClusterSubAccount>()
                        .eq(ClusterSubAccount::getUserSid, dataStorageKey.getUserSid())).getAccountUuid());
        ServiceAccountMessage message = new ServiceAccountMessage();
        message.setAccessKey(accessKey);
        message.setTargetUser(userId);
        message.setOperationType("DELETE");
        message.setClusterId(dataStorageKey.getClusterId());
        ScheduleHelper.sendDataStorageKey(message);

        LambdaQueryWrapper<DataStorageKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataStorageKey::getId, id);
        int rows = dataStorageKeyMapper.delete(queryWrapper);
        return rows > 0;
    }


    @Override
    public DataStorageKey getDataStorageKeyById(Long id) {
        return dataStorageKeyMapper.selectById(id);
    }


    @Override
    public boolean editDataStorageKey(DataStorageKeyDto dataStorageKeyDto) {
        Long id = dataStorageKeyDto.getId();
        if (ObjectUtils.isEmpty(id)) {
            throw new BizException("访问密钥不存在");
        }

        String newDescription = dataStorageKeyDto.getDescription();
        if (newDescription != null && newDescription.length() > 256) {
            throw new BizException("新的描述必须最多256个字符。");
        }

        LambdaQueryWrapper<DataStorageKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataStorageKey::getId, id);
        DataStorageKey dataStorageKey = dataStorageKeyMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(dataStorageKey)) {
            throw new BizException("未找到对应的访问密钥");
        }
        dataStorageKey.setDescription(newDescription);
        //解密后
        String accessKey = CrytoUtilSimple.decrypt(dataStorageKey.getAk());
        String userId = clusterSubAccountMapper.selectOne(new LambdaQueryWrapper<ClusterSubAccount>()
                .eq(ClusterSubAccount::getUserSid, dataStorageKey.getUserSid()))
                .getAccountUuid();
        ServiceAccountMessage message = new ServiceAccountMessage();
        message.setTargetUser(userId);
        message.setAccessKey(accessKey);
        message.setDescription(newDescription);
        message.setStatus(dataStorageKeyDto.getStatus());
        message.setOperationType("EDIT");
        message.setClusterId(dataStorageKeyDto.getClusterId());
        ScheduleHelper.sendDataStorageKey(message);
        return dataStorageKeyMapper.updateById(dataStorageKey) > 0;
    }

    @Override
    public DataStorageKey findDataStorageKeyById(Long id) {
        LambdaQueryWrapper<DataStorageKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataStorageKey::getId, id);
        return dataStorageKeyMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateDataStorageKey(DataStorageKey dataStorageKey) {
        dataStorageKeyMapper.updateById(dataStorageKey);
    }

    @Override
    public Long createAccessKey(DataStorageKeyReq request) {
        log.info("创建访问密钥，参数：{}", request);
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(request.getClusterId());
        if (ObjectUtils.isEmpty(clusterSubAccount)) {
            throw new BizException("无效的集群信息。");
        }
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = Optional.ofNullable(authUser)
                               .map(AuthUser::getUserSid)
                               .orElseThrow(() -> new BizException(BizErrorEnum.MSG_1011_TOKEN_ERROR));
        Long existingKeyCountLong = dataStorageKeyMapper.selectCount(
                new LambdaQueryWrapper<DataStorageKey>()
                        .eq(DataStorageKey::getUserSid, userSid)
        );
        int existingKeyCount = existingKeyCountLong != null ? existingKeyCountLong.intValue() : 0;
        if (existingKeyCount >= 2) {
            throw new BizException("一个账号只能创建两个访问密钥。");
        }
        String description = request.getDescription();
        // 检查描述是否为空
        if (ObjectUtils.isEmpty(description)) {
            throw new BizException("描述不能为空，请重新输入！");
        }
        if (description.contains("*")) {
            throw new BizException("描述中不能包含符号*，请重新输入！");
        }
        if (description.length() > 256) {
            throw new BizException("描述必须最多256个字符。");
        }

        // 获取当前登录用户的集群子账户
        String accountUuid = clusterSubAccountMapper.selectOne(new LambdaQueryWrapper<ClusterSubAccount>()
                .eq(ClusterSubAccount::getUserSid, request.getUserSid())).getAccountUuid();
        log.info("创建访问密钥，集群子账户：{}", accountUuid);

        // 生成 AK 和 SK
        String accessKey = KeyUtil.createAccessKey();
        String secretKey = KeyUtil.createSecretKey();

        ServiceAccountMessage message = new ServiceAccountMessage();
        message.setAccessKey(accessKey);
        message.setSecretKey(secretKey);
        message.setTargetUser(accountUuid);
        message.setBucketName(clusterSubAccount.getBucketName());
        message.setDescription(description);
        message.setPrefix(request.getPrefix());
        message.setOperationType("CREATE");
        message.setClusterId(request.getClusterId());
        ScheduleHelper.sendDataStorageKey(message);

        DataStorageKeyDto dataStorageKeyDto = DataStorageKeyDto.builder()
                                                               .orgSid(request.getOrgSid())
                                                               .clusterId(request.getClusterId())
                                                               .userSid(userSid)
                                                               .ak(accessKey)
                                                               .sk(secretKey)
                                                               .description(request.getDescription())
                                                               .status("active")
                                                               .build();

        return createDataStorageKey(dataStorageKeyDto);
    }

    /**
     * 获取集群信息
     */
    private ClusterSubAccountRespDto getClusterSubAccountRespDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = getClusterSubAccountReqDto(clusterId);
        ClusterSubAccountRespDto data = serverClient.getClusterSubAccount(clusterSubAccountReqDto).getData();
        data.setAccessKey(CrytoUtilSimple.decrypt(data.getAccessKey()));
        data.setSecretKey(CrytoUtilSimple.decrypt(data.getSecretKey()));
        return data;
    }

    /**
     * 根据集群id获取当前登录用户的集群子账户
     *
     * @param clusterId clusterId
     *
     * @return {@link ClusterSubAccountReqDto}
     **/
    private ClusterSubAccountReqDto getClusterSubAccountReqDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();

        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = Optional.ofNullable(authUser)
                               .map(AuthUser::getUserSid)
                               .orElseThrow(() -> new BizException(BizErrorEnum.MSG_1011_TOKEN_ERROR));
        clusterSubAccountReqDto.setUserSid(userSid);
        clusterSubAccountReqDto.setClusterId(clusterId);
        return clusterSubAccountReqDto;
    }

}
