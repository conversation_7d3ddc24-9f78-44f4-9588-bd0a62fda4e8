package com.cloudstar.service.impl.notebook;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.base.enums.AccountTypeEnum;
import com.cloudstar.common.base.enums.BlockStorageStatusEnum;
import com.cloudstar.common.base.enums.ClusterResPoolTypeEnum;
import com.cloudstar.common.base.enums.MirrorAccessType;
import com.cloudstar.common.base.enums.MirrorUseType;
import com.cloudstar.common.base.enums.NoteBookStatusEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.NotebookAutoStopMessage;
import com.cloudstar.common.base.pojo.mq.NotebookEventMessage;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.util.UuidUtil;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.config.NotebookSshConfig;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.cluster.ClusterFlavorMapper;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.mapper.datastorage.BlockStorageMapper;
import com.cloudstar.dao.mapper.datastorage.BlockStorageUsedMapper;
import com.cloudstar.dao.mapper.notebook.NotebookEntityMapper;
import com.cloudstar.dao.mapper.notebook.NotebookOpenPortMapper;
import com.cloudstar.dao.mapper.res.ResImageMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterFlavor;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.dao.model.datastorage.BlockStorage;
import com.cloudstar.dao.model.datastorage.BlockStorageUsed;
import com.cloudstar.dao.model.notebook.NotebookEntity;
import com.cloudstar.dao.model.notebook.NotebookOpenPort;
import com.cloudstar.dao.model.res.ResImage;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.mq.ScheduleHelper;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.sdk.schedule.pojo.NotebookPortReq;
import com.cloudstar.service.facade.notebook.NotebookEntityService;
import com.cloudstar.service.pojo.dto.notebook.BlockStorageVo;
import com.cloudstar.service.pojo.vo.requestvo.notebook.BlockStorageUsedReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.ChangeNotebookFlavorReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.ChangeNotebookImageReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.CreateNotebookOpenPortReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.CreateNotebookReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.DeleteNotebookOpenPortReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.MountBlockStorageReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.QueryNotebookListReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.QueryNotebookPageReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.QueryNotebookPortPageReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.SaveImageReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.UnloadBlockStorageReq;
import com.cloudstar.service.pojo.vo.requestvo.notebook.UpdateSshInfoReq;
import com.cloudstar.service.pojo.vo.responsevo.notebook.NotebookDetailsResp;
import com.cloudstar.service.pojo.vo.responsevo.notebook.NotebookPageResp;
import com.cloudstar.service.pojo.vo.responsevo.notebook.NotebookPortPageResp;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 开发环境实体表;(notebook_entity)表服务实现类
 *
 * <AUTHOR>
 * @date 2024/6/25 15:45
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class NotebookEntityServiceImpl extends ServiceImpl<NotebookEntityMapper, NotebookEntity> implements NotebookEntityService {


    @Resource
    NotebookEntityMapper notebookEntityMapper;

    @Resource
    ClusterEntityMapper clusterEntityMapper;

    @Resource
    ClusterFlavorMapper clusterFlavorMapper;

    @Resource
    ClusterResourcePoolMapper clusterResourcePoolMapper;

    @Resource
    RedisUtil redisUtil;

    @Resource
    ResImageMapper resImageMapper;

    @Resource
    BlockStorageUsedMapper blockStorageUsedMapper;

    @Resource
    BlockStorageMapper blockStorageMapper;

    @Resource
    ConfigService configService;

    @Resource
    NotebookOpenPortMapper notebookOpenPortMapper;

    @Resource
    ScheduleClient scheduleClient;
    @Resource
    UserEntityMapper userEntityMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(CreateNotebookReq req) {
        // 默认非特权模式
        if (Objects.isNull(req.getPrivileged())) {
            req.setPrivileged(false);
        }
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        checkParam(req, authUser);
        NotebookEntity notebookEntity = BeanUtil.toBean(req, NotebookEntity.class);
        if (ClusterResPoolTypeEnum.SHARE.getType().equals(req.getPoolType())) {
            final ClusterResourcePool clusterResourcePool = clusterResourcePoolMapper.selectOne(
                    new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId, notebookEntity.getClusterId())
                                                                 .eq(ClusterResourcePool::getPoolType, ClusterResPoolTypeEnum.SHARE.getType()));
            if (ObjectUtil.isEmpty(clusterResourcePool)) {
                throw new BizException("未找到共享资源池");
            }
            notebookEntity.setPoolId(clusterResourcePool.getId());
        }
        notebookEntity.setCreatedDt(new Date());
        notebookEntity.setCreatedBy(authUser.getAccount());
        notebookEntity.setPrivileged(BooleanUtil.toStringTrueFalse(req.getPrivileged()));
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            //租户账号查询自己的数据和子账号数据
            notebookEntity.setUserSid(authUser.getUserSid());
            notebookEntity.setOrgSid(authUser.getOrgSid());
        } else if (AccountTypeEnum.MANAGER.getType().equalsIgnoreCase(authUser.getAccountType())) {
            //  管理员代开notebook资源 使用req中的userSid
            UserEntity targetUser = userEntityMapper.selectById(req.getUserSid());
            notebookEntity.setOrgSid(targetUser.getOrgSid());
            notebookEntity.setUserSid(targetUser.getUserSid());
        }
        notebookEntity.setStatus(NoteBookStatusEnum.CREATING.getType());
        if (ObjectUtil.equals(1, req.getIsOpenSsh())) {
            final NotebookSshConfig config = configService.getConfig(ConfigType.NOTEBOOK_SSH_CONFIG);
            if (ObjectUtil.isEmpty(config)) {
                throw new BizException("开发环境SSH登录名未配置");
            }
            notebookEntity.setSshAk(config.getLoginAccount().stringValue());
            if (ObjectUtil.isEmpty(req.getSshSk())) {
                throw new BizException("开发环境SSH密码为空");
            }
            notebookEntity.setSshSk(req.getSshSk());
            notebookEntity.setSshWhiteIp(req.getSshWhiteIp());
        }
        notebookEntityMapper.insert(notebookEntity);

        //判断是否挂载存储
        mountBlockStorage(notebookEntity.getId(), req.getStorageList(), authUser);
        //发送创建notebook消息
        NotebookEventMessage eventMessage = new NotebookEventMessage();
        eventMessage.setId(notebookEntity.getId());
        eventMessage.setEvent("create");
        ScheduleHelper.addNotebookEventMessage(eventMessage);
        return true;
    }

    @Override
    public Page<NotebookPageResp> page(QueryNotebookPageReq req, PageForm pageForm) {
        QueryWrapper<NotebookEntity> queryWrapper = new QueryWrapper<>();
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("登录信息获取失败");
        }
        //租户
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            //租户账号查询自己的数据和子账号数据
            queryWrapper.eq("n.org_sid", authUser.getOrgSid());
        }
        if (ObjectUtil.isNotEmpty(req.getName())) {
            queryWrapper.like("n.name", req.getName());
        }
        if (ObjectUtil.isNotEmpty(req.getImageType())) {
            queryWrapper.eq("n.image_type", req.getImageType());
        }
        if (ObjectUtil.isNotEmpty(req.getImage())) {
            queryWrapper.eq("n.image", req.getImage());
        }
        if (ObjectUtil.isNotEmpty(req.getCreateBy())) {
            queryWrapper.eq("n.created_by", req.getCreateBy());
        }
        if (ObjectUtil.isNotEmpty(req.getStatus())) {
            queryWrapper.eq("n.status", req.getStatus());
        }
        if (ObjectUtil.isNotEmpty(req.getStartTime())) {
            queryWrapper.ge("n.created_dt", req.getStartTime());
        }
        if (ObjectUtil.isNotEmpty(req.getEndTime())) {
            queryWrapper.le("n.created_dt", req.getEndTime());
        }
        //不展示已删除数据
        queryWrapper.ne("n.status", NoteBookStatusEnum.DELETED.getType());
        Page<NotebookPageResp> page = notebookEntityMapper.page(pageForm.pageRequest(), queryWrapper);
        List<NotebookPageResp> collect = page.getRecords().stream().map(resp -> {
            resp.setStatusName(NoteBookStatusEnum.getDescByType(resp.getStatus()));
            resp.setSaveImageStatusName(NoteBookStatusEnum.getDescByType(resp.getSaveImageStatus()));
            return resp;
        }).collect(Collectors.toList());
        page.setRecords(collect);
        return page;
    }

    @Override
    public List<NotebookPageResp> getNotebookList(QueryNotebookListReq req) {
        QueryWrapper<NotebookEntity> queryWrapper = new QueryWrapper<>();
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("登录信息获取失败");
        }
        //租户
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            //租户账号查询自己的数据和子账号数据
            queryWrapper.eq("n.org_sid", authUser.getOrgSid());
        }
        if (ObjectUtil.isNotEmpty(req.getName())) {
            queryWrapper.like("n.name", req.getName());
        }
        if (ObjectUtil.isNotEmpty(req.getImageType())) {
            queryWrapper.eq("n.image_type", req.getImageType());
        }
        if (ObjectUtil.isNotEmpty(req.getImage())) {
            queryWrapper.eq("n.image", req.getImage());
        }
        if (ObjectUtil.isNotEmpty(req.getCreateBy())) {
            queryWrapper.eq("n.created_by", req.getCreateBy());
        }
        if (ObjectUtil.isNotEmpty(req.getStatus())) {
            queryWrapper.eq("n.status", req.getStatus());
        }
        if (ObjectUtil.isNotEmpty(req.getStartTime())) {
            queryWrapper.ge("n.created_dt", req.getStartTime());
        }
        if (ObjectUtil.isNotEmpty(req.getEndTime())) {
            queryWrapper.le("n.created_dt", req.getEndTime());
        }
        if (ObjectUtil.isNotEmpty(req.getContainerId())) {
            queryWrapper.like("n.container_id", req.getContainerId());
        }
        if (CollectionUtil.isNotEmpty(req.getContainerIdsLike())) {
            queryWrapper.lambda()
                        .and(wrapper -> req.getContainerIdsLike().forEach(
                                pattern -> wrapper.or().like(NotebookEntity::getContainerId,
                                                             pattern)));
        }
        //不展示已删除数据
        queryWrapper.ne("n.status", NoteBookStatusEnum.DELETED.getType());
        List<NotebookPageResp> list = notebookEntityMapper.list(queryWrapper);
        List<NotebookPageResp> collect = list.stream().map(resp -> {
            resp.setStatusName(NoteBookStatusEnum.getDescByType(resp.getStatus()));
            resp.setSaveImageStatusName(NoteBookStatusEnum.getDescByType(resp.getSaveImageStatus()));
            return resp;
        }).collect(Collectors.toList());
        return collect;
    }


    @Override
    public NotebookDetailsResp queryDetails(Long id) {
        final NotebookDetailsResp notebookDetailsResp = notebookEntityMapper.queryDetails(id);
        if (ObjectUtil.isEmpty(notebookDetailsResp)) {
            throw new BizException("开发环境不存在");
        }
        notebookDetailsResp.setStatusName(NoteBookStatusEnum.getDescByType(notebookDetailsResp.getStatus()));
        notebookDetailsResp.setSaveImageStatusName(NoteBookStatusEnum.getDescByType(notebookDetailsResp.getSaveImageStatus()));
        final List<BlockStorageVo> blockStorageVos = blockStorageUsedMapper.queryStorageByNotebookId(notebookDetailsResp.getId());
        if (CollectionUtil.isNotEmpty(blockStorageVos)) {
            notebookDetailsResp.setStorageList(blockStorageVos);
        }
        //当开发环境是运行中时 返回远程连接地址
        if (NoteBookStatusEnum.RUNNING.getType().equals(notebookDetailsResp.getStatus())
                && ObjectUtil.equals(1, notebookDetailsResp.getIsOpenSsh())) {
            final NotebookSshConfig config = configService.getConfig(ConfigType.NOTEBOOK_SSH_CONFIG);
            if (ObjectUtil.isEmpty(config)) {
                throw new BizException("开发环境SSH节点IP未配置");
            }
            StringBuilder command = new StringBuilder("ssh ");
            command.append(config.getLoginAccount().stringValue()).append("@").append(config.getNodeIp().stringValue())
                   .append(" -p ").append(notebookDetailsResp.getSshPort());
            notebookDetailsResp.setCommand(command.toString());
        }
        return notebookDetailsResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long id) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(id);
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (NoteBookStatusEnum.DELETED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("开发环境已删除，不能重复删除");
        }
        //记录之前的状态 回滚到之前的状态 之前状态存redis
        String cacheKay = RedisCacheKeyEnum.NOTEBOOK_BEFORE_STATUS + ":" + notebookEntity.getId();
        redisUtil.set(cacheKay, notebookEntity.getStatus());
        notebookEntity.setStatus(NoteBookStatusEnum.DELETE.getType());
        notebookEntity.setUpdatedDt(new Date());
        notebookEntityMapper.updateById(notebookEntity);
        //发送创建notebook消息
        NotebookEventMessage eventMessage = new NotebookEventMessage();
        eventMessage.setId(notebookEntity.getId());
        eventMessage.setEvent("delete");
        ScheduleHelper.addNotebookEventMessage(eventMessage);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean stop(Long id) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(id);
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.RUNNING.getType().equals(notebookEntity.getStatus())
                && !NoteBookStatusEnum.QUEUING.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("非运行状态，不能停止");
        }
        String cacheKay = RedisCacheKeyEnum.NOTEBOOK_BEFORE_STATUS + ":" + notebookEntity.getId();
        redisUtil.set(cacheKay, notebookEntity.getStatus());
        notebookEntity.setStatus(NoteBookStatusEnum.STOPPING.getType());
        notebookEntity.setUpdatedDt(new Date());
        notebookEntityMapper.updateById(notebookEntity);
        //发送创建notebook消息
        NotebookEventMessage eventMessage = new NotebookEventMessage();
        eventMessage.setId(notebookEntity.getId());
        eventMessage.setEvent("stop");
        ScheduleHelper.addNotebookEventMessage(eventMessage);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean renewal(Long id) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(id);
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.RUNNING.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("非运行状态，不能续期");
        }
        if (notebookEntity.getIsAutoStop() != 1) {
            throw new BizException("未开启自动停止，不能续期");
        }
        DateTime endTime = DateUtil.offsetHour(notebookEntity.getEndTime(), notebookEntity.getStopUnit());
        notebookEntity.setEndTime(endTime);
        notebookEntity.setUpdatedDt(new Date());
        notebookEntityMapper.updateById(notebookEntity);
        //发送自动停止延时消息
        NotebookAutoStopMessage message = new NotebookAutoStopMessage();
        message.setId(notebookEntity.getId());
        message.setEntTime(notebookEntity.getEndTime());
        ScheduleHelper.addNotebookAutoStopMessage(message, notebookEntity.getStopUnit());
        return true;
    }

    @Override
    public Boolean changeImage(ChangeNotebookImageReq req) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(req.getId());
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.TERMINATED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("非停止状态，不能修改镜像");
        }
        //判断镜像是否存在
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        checkImage(req.getImage(), req.getImageType(), authUser);
        notebookEntity.setImage(req.getImage());
        notebookEntity.setImageType(req.getImageType());
        notebookEntity.setUpdatedDt(new Date());
        notebookEntityMapper.updateById(notebookEntity);
        return true;
    }

    @Override
    public Boolean changeFlavor(ChangeNotebookFlavorReq req) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(req.getId());
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.TERMINATED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("非停止状态，不能修改规格");
        }
        final ClusterFlavor flavor = clusterFlavorMapper.selectOne(
                new LambdaQueryWrapper<ClusterFlavor>().eq(ClusterFlavor::getClusterId, notebookEntity.getClusterId())
                                                       .eq(ClusterFlavor::getId, req.getFlavorId()));
        if (ObjectUtil.isEmpty(flavor)) {
            throw new BizException("规格不存在");
        }
        notebookEntity.setFlavorId(req.getFlavorId());
        notebookEntity.setUpdatedDt(new Date());
        notebookEntityMapper.updateById(notebookEntity);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean start(Long id) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(id);
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.TERMINATED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("状态错误");
        }
        String cacheKay = RedisCacheKeyEnum.NOTEBOOK_BEFORE_STATUS + ":" + notebookEntity.getId();
        redisUtil.set(cacheKay, notebookEntity.getStatus());
        notebookEntity.setStatus(NoteBookStatusEnum.STARTING.getType());
        notebookEntity.setUpdatedDt(new Date());
        notebookEntityMapper.updateById(notebookEntity);
        //发送创建notebook消息
        NotebookEventMessage eventMessage = new NotebookEventMessage();
        eventMessage.setId(notebookEntity.getId());
        eventMessage.setEvent("create");
        ScheduleHelper.addNotebookEventMessage(eventMessage);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean mountStorage(MountBlockStorageReq req) {
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("登录信息获取失败");
        }
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(req.getNotebookId());
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.TERMINATED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("状态错误");
        }
        mountBlockStorage(notebookEntity.getId(), req.getStorageList(), authUser);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unloadStorage(UnloadBlockStorageReq req) {
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("登录信息获取失败");
        }
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(req.getNotebookId());
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.TERMINATED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("状态错误");
        }
        //卸载存储
        req.getStorageIds().stream().forEach(storageId -> {
            final BlockStorage blockStorage = blockStorageMapper.selectById(storageId);
            if (ObjectUtil.isEmpty(blockStorage)) {
                throw new BizException("块存储不存在");
            }
            blockStorageUsedMapper.delete(new LambdaQueryWrapper<BlockStorageUsed>().eq(BlockStorageUsed::getUsedResourceId, req.getNotebookId())
                                                                                    .eq(BlockStorageUsed::getBlockStorageId, storageId));
            //修改块存储的使用状态
            blockStorage.setStatus(BlockStorageStatusEnum.UNUSED.getType());
            blockStorage.setUpdatedBy(authUser.getAccount());
            blockStorage.setUpdatedDt(new Date());
            blockStorageMapper.updateById(blockStorage);
        });
        return true;
    }

    @Override
    public String getSshName() {
        final NotebookSshConfig config = configService.getConfig(ConfigType.NOTEBOOK_SSH_CONFIG);
        if (ObjectUtil.isEmpty(config)) {
            throw new BizException("开发环境SSH登录用户名未配置");
        }
        return config.getLoginAccount().stringValue();
    }

    @Override
    public Boolean updateSshInfo(UpdateSshInfoReq req) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(req.getId());
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.TERMINATED.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("非停止状态，不能修改SSH信息");
        }
        if (ObjectUtil.isNotEmpty(req.getIsOpenSsh()) && ObjectUtil.equals(0, req.getIsOpenSsh())) {
            notebookEntity.setIsOpenSsh(req.getIsOpenSsh());
            notebookEntity.setSshAk(null);
            notebookEntity.setSshSk(null);
            notebookEntity.setSshWhiteIp(null);
        }
        if (ObjectUtil.isNotEmpty(req.getIsOpenSsh()) && ObjectUtil.equals(1, req.getIsOpenSsh())) {
            notebookEntity.setIsOpenSsh(req.getIsOpenSsh());
            if (ObjectUtil.isNotEmpty(req.getSshSk())) {
                notebookEntity.setSshSk(req.getSshSk());
            }
            if (ObjectUtil.isNotEmpty(req.getSshWhiteIp())) {
                notebookEntity.setSshWhiteIp(req.getSshWhiteIp());
            }
        }
        notebookEntityMapper.updateById(notebookEntity);
        return true;
    }

    @Override
    public Boolean createOpenPort(CreateNotebookOpenPortReq req) {
        final Long notebookId = req.getNotebookId();
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(notebookId);
        if (ObjectUtil.isEmpty(notebookEntity) || NoteBookStatusEnum.DELETED.getType().equals(notebookEntity)) {
            throw new BizException("开发环境不存");
        }
        final List<NotebookOpenPort> notebookOpenPorts = notebookOpenPortMapper.selectList(
                new LambdaQueryWrapper<NotebookOpenPort>().eq(NotebookOpenPort::getNotebookId, notebookId));
        if (CollectionUtil.isNotEmpty(notebookOpenPorts)
                && notebookOpenPorts.stream().anyMatch(notebookOpenPort -> notebookOpenPort.getInsidePort().equals(req.getInsidePort()))) {
            throw new BizException("端口已使用");
        }
        final NotebookOpenPort notebookOpenPort = BeanUtil.toBean(req, NotebookOpenPort.class);
        notebookOpenPort.setK8sSvcName(notebookEntity.getName() + "-" + UuidUtil.getShortUuid());
        notebookOpenPort.setCreatedDt(new Date());
        notebookOpenPort.setUpdatedDt(new Date());
        notebookOpenPort.setCreatedBy(ThreadAuthUserHolder.getAuthUser().getAccount());
        notebookOpenPort.setUpdatedBy(ThreadAuthUserHolder.getAuthUser().getAccount());
        notebookOpenPortMapper.insert(notebookOpenPort);
        try {
            // 下发底层
            NotebookPortReq notebookPortReq = new NotebookPortReq();
            notebookPortReq.setUserSid(notebookEntity.getUserSid());
            notebookPortReq.setClusterId(notebookEntity.getClusterId());
            notebookPortReq.setPortId(notebookOpenPort.getId());
            final Rest<Boolean> rest = scheduleClient.createPort(notebookPortReq);
            if (!rest.isSuccess() || !rest.getData()) {
                //创建失败
                notebookOpenPortMapper.deleteById(notebookOpenPort.getId());
                throw new BizException("创建端口失败");
            }
        } catch (Exception e) {
            notebookOpenPortMapper.deleteById(notebookOpenPort.getId());
            throw new BizException("创建端口失败");
        }
        return true;
    }

    @Override
    public Boolean deleteOpenPort(DeleteNotebookOpenPortReq req) {
        final Long notebookId = req.getNotebookId();
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(notebookId);
        if (ObjectUtil.isEmpty(notebookEntity) || NoteBookStatusEnum.DELETED.getType().equals(notebookEntity)) {
            throw new BizException("开发环境不存");
        }
        final NotebookOpenPort del = notebookOpenPortMapper.selectById(req.getPortId());
        if (ObjectUtil.isEmpty(del)) {
            throw new BizException("端口不存在");
        }
        try {
            // 下发底层
            NotebookPortReq notebookPortReq = new NotebookPortReq();
            notebookPortReq.setUserSid(notebookEntity.getUserSid());
            notebookPortReq.setClusterId(notebookEntity.getClusterId());
            notebookPortReq.setPortId(del.getId());
            final Rest<Boolean> rest = scheduleClient.deletePort(notebookPortReq);
            if (!rest.isSuccess() || !rest.getData()) {
                throw new BizException("删除端口失败");
            }
            notebookOpenPortMapper.deleteById(req.getPortId());
        } catch (Exception e) {
            throw new BizException("删除端口失败");
        }
        return true;
    }

    @Override
    public Page<NotebookPortPageResp> portPage(QueryNotebookPortPageReq req, PageForm pageForm) {
        if (ObjectUtil.isEmpty(req.getNotebookId())) {
            throw new BizException("开发环境ID为空");
        }
        QueryWrapper<NotebookOpenPort> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("notebook_id", req.getNotebookId());
        if (ObjectUtil.isNotEmpty(req.getPortName())) {
            queryWrapper.like("port_name", req.getPortName());
        }
        if (ObjectUtil.isNotEmpty(req.getInsidePort())) {
            queryWrapper.like("inside_port", req.getInsidePort());
        }
        if (ObjectUtil.isNotEmpty(req.getExternalPort())) {
            queryWrapper.like("external_port", req.getExternalPort());
        }
        return notebookOpenPortMapper.page(pageForm.pageRequest(), queryWrapper);
    }

    @Override
    public String getNotebookEndpoint(Long notebookId) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(notebookId);
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        final Rest<String> rest = scheduleClient.getNotebookEndpoint(notebookEntity.getClusterId());
        if (!rest.isSuccess()) {
            throw new BizException("查询端点失败");
        }
        return rest.getData();
    }

    @Override
    @Transactional
    public Boolean saveImage(SaveImageReq saveImageReq) {
        final NotebookEntity notebookEntity = notebookEntityMapper.selectById(saveImageReq.getNotebookId());
        if (ObjectUtil.isEmpty(notebookEntity)) {
            throw new BizException("开发环境不存在");
        }
        if (!NoteBookStatusEnum.RUNNING.getType().equals(notebookEntity.getStatus())) {
            throw new BizException("运行状态才能保存镜像");
        }
        notebookEntity.setSaveImageStatus(NoteBookStatusEnum.SAVING.getType());
        notebookEntityMapper.updateById(notebookEntity);
        //发送创建notebook消息
        NotebookEventMessage eventMessage = new NotebookEventMessage();
        eventMessage.setId(notebookEntity.getId());
        eventMessage.setEvent("saving");
        eventMessage.setImageName(saveImageReq.getImageName());
        eventMessage.setImageTag(saveImageReq.getImageTag());
        ScheduleHelper.addNotebookEventMessage(eventMessage);
        return true;
    }

    private void checkParam(CreateNotebookReq req, AuthUser authUser) {
        final ClusterEntity clusterEntity = clusterEntityMapper.selectById(req.getClusterId());
        if (ObjectUtil.isEmpty(clusterEntity)) {
            throw new BizException("集群不存在");
        }
        LambdaQueryWrapper<NotebookEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotebookEntity::getClusterId, req.getClusterId()).eq(NotebookEntity::getName, req.getName());
        //租户
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            //租户账号查询自己的数据和子账号数据
            queryWrapper.eq(NotebookEntity::getOrgSid, authUser.getOrgSid());
        } else if (AccountTypeEnum.MANAGER.getType().equalsIgnoreCase(authUser.getAccountType())) {
            //  管理员代开notebook资源 使用req中的userSid
            UserEntity userEntity = userEntityMapper.selectById(req.getUserSid());
            queryWrapper.eq(NotebookEntity::getOrgSid, userEntity.getOrgSid());
        }
        queryWrapper.ne(NotebookEntity::getStatus, NoteBookStatusEnum.DELETED);
        final NotebookEntity notebookEntity = this.getBaseMapper().selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(notebookEntity)) {
            throw new BizException("开发环境名称已存在");
        }
        if (req.getIsAutoStop() == 1 && ObjectUtil.isEmpty(req.getStopUnit())) {
            throw new BizException("自动停止时间为空");
        }

        if (req.getIsOpenSsh() == 1 && (ObjectUtil.isEmpty(req.getSshSk()) || ObjectUtil.isEmpty(req.getSshAk()))) {
            throw new BizException("远程连接AK、SK为空");
        }
        final ClusterFlavor flavor = clusterFlavorMapper.selectOne(
                new LambdaQueryWrapper<ClusterFlavor>().eq(ClusterFlavor::getClusterId, req.getClusterId())
                                                       .eq(ClusterFlavor::getId, req.getFlavorId()));
        if (ObjectUtil.isEmpty(flavor)) {
            throw new BizException("规格不存在");
        }
        if (ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(req.getPoolType()) && ObjectUtil.isEmpty(req.getPoolId())) {
            throw new BizException("专属资源池ID为空");
        }
        //判断镜像是否存在
        checkImage(req.getImage(), req.getImageType(), authUser);
    }

    /**
     * 检查镜像是否存在
     *
     * @param imageTag tag
     * @param imageType type
     * @param authUser user
     */
    private void checkImage(String imageTag, String imageType, AuthUser authUser) {
        //判断镜像是否存在
        LambdaQueryWrapper<ResImage> wrapper = new LambdaQueryWrapper<ResImage>();
        //判断是否查询公共镜像
        if (Objects.equals(imageType, MirrorAccessType.PUBLIC.getEnName())) {
            wrapper.eq(ResImage::getAccessType, imageType);
        } else {
            wrapper.eq(ResImage::getOwnerId, authUser.getUserSid());
        }
        wrapper.eq(ResImage::getTag, imageTag);
        wrapper.eq(ResImage::getUseType, MirrorUseType.VERSION.getEnName());
        final ResImage resImage = resImageMapper.selectOne(wrapper);
        if (ObjectUtil.isEmpty(resImage)) {
            throw new BizException("镜像不存在");
        }
    }

    /**
     * 挂载存储
     *
     * @param notebookId id
     * @param storageList list
     * @param authUser user
     */
    private void mountBlockStorage(Long notebookId, List<BlockStorageUsedReq> storageList, AuthUser authUser) {
        //判断是否挂载存储
        if (CollectionUtil.isNotEmpty(storageList)) {
            storageList.stream().forEach(storageUsedReq -> {
                final BlockStorage blockStorage = blockStorageMapper.selectById(storageUsedReq.getBlockStorageId());
                if (ObjectUtil.isEmpty(blockStorage)) {
                    throw new BizException("块存储不存在");
                }
                //新建块存储挂载信息
                BlockStorageUsed used = BlockStorageUsed.builder()
                                                        .blockStorageId(storageUsedReq.getBlockStorageId())
                                                        .usedResourceId(notebookId)
                                                        .mountPath(storageUsedReq.getMountPath())
                                                        .createdDt(new Date())
                                                        .createdBy(authUser.getAccount())
                                                        .build();
                blockStorageUsedMapper.insert(used);
                //修改块存储的使用状态
                blockStorage.setStatus(BlockStorageStatusEnum.USED.getType());
                blockStorageMapper.updateById(blockStorage);
            });
        }
    }
}
