package com.cloudstar.service.impl.training;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.annotation.DistributedLock;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.enums.ObsResourceTypeEnum;
import com.cloudstar.common.base.enums.ScheduleEventType;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.enums.VisualizationTaskStatus;
import com.cloudstar.common.base.enums.VisualizationTaskType;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.VisualizedEventMessage;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.mapper.training.VisualizationJobTaskMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.dao.model.training.TrainingJobParams;
import com.cloudstar.dao.model.training.VisualizationJobTask;
import com.cloudstar.mq.ScheduleHelper;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.cluster.ClusterEntityService;
import com.cloudstar.service.facade.datastorage.SlurmAossService;
import com.cloudstar.service.facade.training.VisualizationJobTaskService;
import com.cloudstar.service.pojo.dto.obs.ObsFileDto;
import com.cloudstar.service.pojo.vo.requestvo.obs.ObsEntityPageReq;
import com.cloudstar.service.pojo.vo.requestvo.training.VisualizationCreateRequest;
import com.cloudstar.service.pojo.vo.requestvo.training.VisualizationJobPageReq;
import com.cloudstar.service.pojo.vo.requestvo.training.VisualizationLogPathReq;
import com.cloudstar.service.pojo.vo.requestvo.training.VisualizationTaskPageReq;
import com.cloudstar.service.pojo.vo.responsevo.obs.ObsEntityRep;
import com.cloudstar.service.pojo.vo.responsevo.training.JobListResponse;
import com.cloudstar.service.pojo.vo.responsevo.training.LogDirectoryListResponse;
import com.cloudstar.service.pojo.vo.responsevo.training.VisualizationTask;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 可视化作业任务服务impl
 *
 * <AUTHOR>
 * @date 2024/07/31
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class VisualizationJobTaskServiceImpl
        extends ServiceImpl<VisualizationJobTaskMapper, VisualizationJobTask>
        implements VisualizationJobTaskService {

    private static final String JOB_TASK = "VisualizationJobTask";
    VisualizationJobTaskMapper visualizationJobTaskMapper;

    TrainingJobEntityMapper trainingJobEntityMapper;

    TrainingJobParamsServiceImpl trainingJobParamsService;

    SlurmAossService slurmAossService;

    ServerClient serverClient;

    ClusterEntityService clusterEntityService;

    ClusterEntityMapper clusterEntityMapper;

    /**
     * 底层实施
     *
     * @param task      任务
     * @param eventType 事件类型
     */
    private static void bottomLevelImplementation(VisualizationJobTask task, ScheduleEventType eventType) {
        VisualizedEventMessage message = new VisualizedEventMessage();
        message.setUserSid(ThreadAuthUserHolder.getAuthUser().getUserSid());
        message.setTaskName(task.getType().toLowerCase() + "-" + task.getName());
        message.setClusterId(task.getClusterId());
        message.setEvent(eventType);
        message.setTaskType(task.getType());
        message.setTaskLogPath(task.getLogDirectory());
        ScheduleHelper.addVisualizationJobMessage(message);
    }

    /**
     * 获取可用的作业
     *
     * @param jobPageReq 作业页面要求
     * @return {@link PageResult }<{@link JobListResponse }>
     */
    @Override
    public PageResult<JobListResponse> getJobPage(VisualizationJobPageReq jobPageReq) {
        //只查询有log_path参数组的作业
        Map<Long, String> logUrl = Optional.ofNullable(trainingJobParamsService.getBaseMapper()
                        .selectList(new LambdaQueryWrapper<TrainingJobParams>()
                                .select(TrainingJobParams::getGroupId, TrainingJobParams::getValue)
                                .eq(TrainingJobParams::getName, "log_url"))).orElse(List.of())
                .stream().collect(Collectors.toMap(TrainingJobParams::getGroupId, TrainingJobParams::getValue, (k, v) -> v));
        if (CollUtil.isEmpty(logUrl)) {
            return PageResult.of(new Page<>(), new ArrayList<>());
        }
        //查询可用的作业
        LambdaQueryWrapper<TrainingJobEntity> wrapper = new LambdaQueryWrapper<TrainingJobEntity>()
                .eq(TrainingJobEntity::getStatus, TrainingJobStatusEnum.COMPLETED.getType())
                .in(TrainingJobEntity::getParamsGroupId, logUrl.keySet())
                .eq(StrUtil.isNotBlank(jobPageReq.getClusterId()), TrainingJobEntity::getClusterId, jobPageReq.getClusterId())
                .like(StrUtil.isNotBlank(jobPageReq.getJobName()), TrainingJobEntity::getName, jobPageReq.getJobName())
                .orderByDesc(TrainingJobEntity::getCreatedDt);
        Page<TrainingJobEntity> page = new Page<>(jobPageReq.getPageNo(), jobPageReq.getPageSize());
        page = trainingJobEntityMapper.selectPage(page, wrapper);
        List<JobListResponse> records = Optional.ofNullable(page.getRecords()).orElse(new ArrayList<>())
                .stream().map(job -> {
                    JobListResponse response = new JobListResponse();
                    response.setJobId(String.valueOf(job.getId()));
                    response.setJobName(job.getName());
                    response.setPrefix(logUrl.getOrDefault(job.getParamsGroupId(), ""));
                    return response;
                }).collect(Collectors.toList());
        return PageResult.of(page, records);
    }

    /**
     * 获取日志目录
     *
     * @param logPathReq 作业id
     * @return {@link LogDirectoryListResponse }
     */
    @Override
    public ObsEntityRep getLogDirectories(VisualizationLogPathReq logPathReq) {
        TrainingJobEntity job = getJobEntity(logPathReq.getJobId());

        final ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(job.getClusterId());
        final ClusterEntity clusterEntity = clusterEntityService.getById(job.getClusterId());
        String logPaths = trainingJobParamsService.selectLogPathsByParamsGroupId(job.getParamsGroupId());
        if (StrUtil.isBlank(logPaths)) {
            return new ObsEntityRep();
        }
        ObsEntityPageReq req = new ObsEntityPageReq();
        req.setClusterId(job.getClusterId());
        req.setPrefix(StrUtil.isBlank(logPathReq.getPrefix()) ? logPaths : logPathReq.getPrefix());
        req.setMarker(StrUtil.isBlank(logPathReq.getMarker()) ? logPaths : logPathReq.getMarker());
        req.setMaxKeys(logPathReq.getMaxKeys());

        ObsEntityRep obsEntityRep = slurmAossService.listObjects(req, clusterSubAccount, clusterEntity.getClusterType());
        List<ObsFileDto> collect = Optional.ofNullable(obsEntityRep.getFileList())
                .orElse(new ArrayList<>()).stream()
                .filter(obj -> Objects.equals(ObsResourceTypeEnum.FOLDER.getType(), obj.getResourceType()))
                .collect(Collectors.toList());
        obsEntityRep.setFileList(collect);
        return obsEntityRep;
    }

    /**
     * 创建可视化实例
     *
     * @param request 请求
     */
    @Override
    @DistributedLock(key = "#request.jobId", prefix = JOB_TASK)
    public void create(VisualizationCreateRequest request) {
        //获取当前用户
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        //创建有效性验证
        VisualizationJobTask task = createValidityVerification(request);
        //创建可视化实例
        task.setOwnerId(authUser.getUserSid());
        task.setOrgSid(authUser.getOrgSid());
        task.setName(request.getName());
        task.setStatus(VisualizationTaskStatus.STARTING.getCode());
        task.setType(request.getType());
        task.setJobId(Long.valueOf(request.getJobId()));
        task.setDescription(request.getDescription());
        Date createdAt = new Date();
        task.setCreatedAt(createdAt);
        task.setUpdatedAt(createdAt);
        WebUtil.prepareInsertParams(task, authUser);
        visualizationJobTaskMapper.insert(task);
        // 启动可视化实例
        bottomLevelImplementation(task, ScheduleEventType.CREATE);
    }

    /**
     * 获取可视化任务列表
     *
     * @param pageReq 页面要求
     * @return {@link PageResult }<{@link VisualizationTask }>
     */
    @Override
    public PageResult<VisualizationTask> getVisualizationTaskPage(VisualizationTaskPageReq pageReq) {
        Map<Long, String> clusterMap = new HashMap<>();
        if (StrUtil.isNotBlank(pageReq.getClusterName())) {
            clusterMap = Optional.ofNullable(clusterEntityService.getClustersByName(pageReq.getClusterName()))
                    .orElse(List.of())
                    .stream().collect(Collectors.toMap(ClusterEntity::getId, ClusterEntity::getClusterName, (k, v) -> v));
            if (CollUtil.isEmpty(clusterMap)) {
                return PageResult.of(new Page<>(), new ArrayList<>());
            }
        }

        //查询未删除的可视化实例
        LambdaQueryWrapper<VisualizationJobTask> wrapper = new LambdaQueryWrapper<VisualizationJobTask>()
                .ne(VisualizationJobTask::getStatus, VisualizationTaskStatus.DELETED.getCode())
                .eq(VisualizationJobTask::getOwnerId, ThreadAuthUserHolder.getAuthUser().getUserSid())
                .in(CollUtil.isNotEmpty(clusterMap.keySet()), VisualizationJobTask::getClusterId, clusterMap.keySet())
                .like(StrUtil.isNotBlank(pageReq.getName()), VisualizationJobTask::getName, pageReq.getName())
                .orderByDesc(VisualizationJobTask::getCreatedAt);
        Page<VisualizationJobTask> page = new Page<>(pageReq.getPageNo(), pageReq.getPageSize());
        page = visualizationJobTaskMapper.selectPage(page, wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return PageResult.of(page, new ArrayList<>());
        }
        if (CollUtil.isEmpty(clusterMap)) {
            clusterMap = Optional.ofNullable(clusterEntityService.getAllClusters())
                    .orElse(List.of())
                    .stream().collect(Collectors.toMap(ClusterEntity::getId, ClusterEntity::getClusterName, (k, v) -> v));
        }
        Map<Long, String> finalClusterMap = clusterMap;
        List<VisualizationTask> collect = Optional.ofNullable(page.getRecords()).orElse(new ArrayList<>())
                .stream().map(task -> {
                    VisualizationTask response = new VisualizationTask();
                    response.setId(String.valueOf(task.getId()));
                    response.setName(task.getName());
                    VisualizationTaskStatus taskStatus = VisualizationTaskStatus.getByCode(task.getStatus());
                    response.setStatus(taskStatus.getCode());
                    response.setStatusName(taskStatus.getName());
                    response.setType(Optional.ofNullable(VisualizationTaskType.getByCode(task.getType()))
                            .orElse(VisualizationTaskType.TENSOR_BOARD).getName());
                    response.setJobName(task.getJobName());
                    response.setClusterId(String.valueOf(task.getClusterId()));
                    response.setClusterName(finalClusterMap.get(task.getClusterId()));
                    response.setCreatedAt(DateUtil.format(task.getCreatedAt(), DatePattern.NORM_DATETIME_PATTERN));
                    response.setCreatedBy(task.getCreatedBy());
                    response.setDescription(task.getDescription());
                    response.setAccessPath(taskStatus.equals(VisualizationTaskStatus.RUNNING)
                            ? task.getAccessPath() : "");
                    return response;
                }).collect(Collectors.toList());
        return PageResult.of(page, collect);
    }

    /**
     * 更新可视化任务状态
     *
     * @param id     任务id
     * @param status 状态
     */
    @Override
    @DistributedLock(key = "#id", prefix = JOB_TASK)
    public void updateStatus(Long id, String status) {
        //校验状态是否合法
        VisualizationTaskStatus taskStatus = Optional.ofNullable(VisualizationTaskStatus.getByCode(status))
                .orElseThrow(() -> new BizException("状态不合法"));
        //更新状态
        switch (taskStatus) {
            case STARTING:
                start(id);
                break;
            case STOPPED:
                stop(id);
                break;
            case DELETED:
                delete(id);
                break;
            default:
                log.warn("{}状态无操作", taskStatus.getName());
                break;
        }

    }

    /**
     * 启动可视化任务
     *
     * @param id 任务id
     */
    @Override
    @DistributedLock(key = "#id", prefix = JOB_TASK)
    public void start(Long id) {
        VisualizationJobTask task = getTask(id);
        if (task.getStatus().equals(VisualizationTaskStatus.RUNNING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.STOPPING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.DELETING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.STARTING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.DELETED.getCode())) {
            throw new BizException("任务状态不合法");
        }
        task.setStatus(VisualizationTaskStatus.STARTING.getCode());
        visualizationJobTaskMapper.updateById(task);
        // 启动可视化实例
        bottomLevelImplementation(task, ScheduleEventType.START);
    }

    /**
     * 停止可视化任务
     *
     * @param id 任务id
     */
    @Override
    @DistributedLock(key = "#id", prefix = JOB_TASK)
    public void stop(Long id) {
        VisualizationJobTask task = getTask(id);
        if (task.getStatus().equals(VisualizationTaskStatus.STOPPED.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.STOPPING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.STARTING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.DELETING.getCode())
                || task.getStatus().equals(VisualizationTaskStatus.DELETED.getCode())) {
            throw new BizException("任务状态不合法");
        }
        task.setStatus(VisualizationTaskStatus.STOPPING.getCode());
        visualizationJobTaskMapper.updateById(task);
        // 启动可视化实例
        bottomLevelImplementation(task, ScheduleEventType.STOP);
    }

    /**
     * 删除可视化任务
     *
     * @param id 任务id
     */
    @Override
    @DistributedLock(key = "#id", prefix = JOB_TASK)
    public void delete(Long id) {
        VisualizationJobTask task = getTask(id);
        if (task.getStatus().equals(VisualizationTaskStatus.DELETED.getCode())) {
            throw new BizException("任务状态不合法");
        }
        task.setStatus(VisualizationTaskStatus.DELETING.getCode());
        visualizationJobTaskMapper.updateById(task);
        // 启动可视化实例
        bottomLevelImplementation(task, ScheduleEventType.DELETE);
    }

    /**
     * 名称重复检查
     *
     * @param name 名称
     * @return boolean 是否重复
     */
    @Override
    public boolean nameDuplicationCheck(String name) {
        return Optional.ofNullable(visualizationJobTaskMapper.selectOne(new LambdaQueryWrapper<VisualizationJobTask>()
                .eq(VisualizationJobTask::getOwnerId, ThreadAuthUserHolder.getAuthUser().getUserSid())
                .ne(VisualizationJobTask::getStatus, VisualizationTaskStatus.DELETED.getCode())
                .eq(VisualizationJobTask::getName, name))).isPresent();
    }

    /**
     * 获取任务
     *
     * @param id 身份证件
     * @return {@link VisualizationJobTask }
     */
    private VisualizationJobTask getTask(Long id) {
        return Optional.ofNullable(visualizationJobTaskMapper.selectById(id))
                .orElseThrow(() -> new BizException("任务不存在"));
    }

    /**
     * 创建有效性验证
     *
     * @param request 请求
     */
    private VisualizationJobTask createValidityVerification(VisualizationCreateRequest request) {
        final VisualizationJobTask task = new VisualizationJobTask();
        //校验名称是否重复
        if (nameDuplicationCheck(request.getName())) {
            throw new BizException("名称重复");
        }
        //校验作业是否已创建可视化实例
        List<VisualizationJobTask> taskList = visualizationJobTaskMapper.selectList(new LambdaQueryWrapper<VisualizationJobTask>()
                .eq(VisualizationJobTask::getType, VisualizationTaskStatus.STARTING.getCode())
                .eq(VisualizationJobTask::getType, VisualizationTaskStatus.RUNNING.getCode())
                .eq(VisualizationJobTask::getType, VisualizationTaskStatus.STOPPING.getCode())
                .eq(VisualizationJobTask::getType, VisualizationTaskStatus.DELETING.getCode())
                .eq(VisualizationJobTask::getType, VisualizationTaskStatus.STOPPED.getCode())
                .eq(VisualizationJobTask::getJobId, request.getJobId()));
        if (CollUtil.isNotEmpty(taskList)) {
            throw new BizException("作业已有可用的可视化实例");
        }
        //校验作业是否存在
        TrainingJobEntity job = getJobEntity(request.getJobId());
        //校验日志目录是否存在
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(job.getClusterId());
        ClusterEntity clusterEntity = clusterEntityService.getById(job.getClusterId());
        String paths = trainingJobParamsService.selectLogPathsByParamsGroupId(job.getParamsGroupId());
        String logPaths = request.getLogPath();
        if (!slurmAossService.doesFolderExist(logPaths, clusterSubAccount, clusterEntity.getClusterType())
                || StrUtil.isBlank(paths)) {
            throw new BizException("日志目录不存在");
        }
        //校验可视化类型是否支持
        if (Objects.isNull(VisualizationTaskType.getByCode(request.getType()))) {
            throw new BizException("暂不支持该可视化类型");
        }
        task.setJobName(job.getName());
        task.setClusterId(job.getClusterId());
        task.setLogDirectory(request.getLogPath());
        task.setInstanceDirectory(logPaths);
        return task;
    }

    /**
     * 获取作业实体
     *
     * @param jobId 作业id
     * @return {@link TrainingJobEntity }
     */
    private TrainingJobEntity getJobEntity(String jobId) {
        return Optional.ofNullable(trainingJobEntityMapper.selectById(Long.parseLong(jobId))).orElseThrow(() -> new BizException("作业不存在"));
    }

    /**
     * 获取集群信息
     */
    private ClusterSubAccountRespDto getClusterSubAccountRespDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = Optional.ofNullable(authUser).map(AuthUser::getUserSid).orElseThrow(() -> new BizException(BizErrorEnum.MSG_1011_TOKEN_ERROR));
        clusterSubAccountReqDto.setUserSid(userSid);
        clusterSubAccountReqDto.setClusterId(clusterId);
        ClusterSubAccountRespDto data = serverClient.getClusterSubAccount(clusterSubAccountReqDto).getData();
        data.setAccessKey(CrytoUtilSimple.decrypt(data.getAccessKey()));
        data.setSecretKey(CrytoUtilSimple.decrypt(data.getSecretKey()));
        return data;
    }
}
