package com.cloudstar.service.pojo.vo.requestvo.datastorage;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 添加数据资源
 *
 * <AUTHOR>
 * @date 2022-08-22 15:36
 */
@Data
public class DataStorageResourcesUpdateReq {

    /**
     * id
     */
    @NotNull(message = "数据资源ID不能为空")
    private Long id;

    /**
     * 名字
     */
    @Length(max = 64, message = "长度不能超过64")
    private String name;


    /**
     * 备注
     */
    @Length(max = 500, message = "长度不能超过500")
    private String remark;

    /**
     * 文件源路径
     */
    private String fileSourcePath;
}
