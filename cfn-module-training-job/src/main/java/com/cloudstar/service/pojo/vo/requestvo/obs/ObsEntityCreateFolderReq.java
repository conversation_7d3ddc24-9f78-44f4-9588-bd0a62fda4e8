package com.cloudstar.service.pojo.vo.requestvo.obs;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class ObsEntityCreateFolderReq {
    /**
     * 集群id
     */
    @NotNull(message = "cannot be null")
    private Long clusterId;

    /**
     * 对象key
     */
    @NotNull(message = "cannot be null")
    @Pattern(regexp = "^[^?!\\\\|/:*<>\\'^&]+$",
            message = "名称不能包含特殊字符")
    private String objectKey;
}

