package com.cloudstar.service.pojo.vo.requestvo.training;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 可视化创建请求
 *
 * <AUTHOR>
 * @date 2024/07/30
 */
@Data
public class VisualizationCreateRequest {
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(min = 2, max = 100, message = "名称长度必须在2-64个字符之间")
    @Pattern(regexp = "^[a-z0-9]([-a-z0-9]*[a-z0-9])?", message = "名称不合法，只允许小写字母、数字、中划线")
    private String name;
    /**
     * 关联作业id
     */
    @NotBlank(message = "关联作业id不能为空")
    private String jobId;
    /**
     * 日志目录
     */
    @NotBlank(message = "日志目录不能为空")
    private String logPath;
    /**
     * 实例类型
     */
    @NotBlank(message = "实例类型不能为空")
    private String type;

    /**
     * 详情
     */
    private String description;
}
