package com.cloudstar.rightcloud.monitor.common.constant;

import cn.hutool.core.util.StrUtil;

public class MorFillPointConstant {

    //exporter接口超时时间 - 1 小时
    public static final int HTTP_TIMEOUT_S = 600;
    public static final int HTTP_TIMEOUT_MS = HTTP_TIMEOUT_S * 1000;

    //任务锁过期时间, 再任务执行过程中进行延长任务锁剩余时间，避免任务还未执行完就过期了
    public static final int TASK_LOCK_KEY_EXPIRE_S = 600;

    //补点粒度，为1表示按照采集频率一比一进行补点，3 表示经过三次采集频率的时间才补一次点
    public static final int GRANULARITY = 3;

    //监控指标补点任务锁，参数为云环境ID
    private static final String TASK_LOCK_KEY_TEMPLATE = "MONITOR:FILL_POINT:TASK:{}";

    public static String getTaskLockKey(Long cloudEnvId) {
        return StrUtil.format(TASK_LOCK_KEY_TEMPLATE, cloudEnvId);
    }

}
