package com.cloudstar.rightcloud.monitor.common.constant;

/**
 * The type MonitorConst.
 * <p>
 * Created on 2019/5/20
 *
 * <AUTHOR>
 */
public interface OpsMonitorConstant {

    /**
     * 监控云环境字段名称
     */
    String MONITOR_CLOUD_ENV_FIELD_NAME = "instanceId";

    /**
     * 监控云环境字段名称
     */
    String MONITOR_CLOUD_ENV_ID_NAME = "云环境Id";

    /**
     * 云平台
     */
    String MONITOR_ENV_FIELD_NAME = "cloudEnv";

    /**
     * 云环境
     */
    String MONITOR_ENV_NAME_FIELD_NAME = "cloudEnvName";

    /**
     * 云平台
     */
    String MONITOR_ENV_TYPE_NAME = "云平台";

    /**
     * 云平台
     */
    String MONITOR_ENV_TYPE_EN_NAME = "cloud env type";

    /**
     * 云环境
     */
    String MONITOR_ENV_NAME = "云环境";

    /**
     * 云环境
     */
    String MONITOR_ENV_EN_NAME = "cloud env";

    /**
     * 云环境Id
     */
    String MONITOR_ENV_ID_FIELD_NAME = "cloudEnvId";

    /**
     * 云环境Id
     */
    String MONITOR_ID_FIELD_NAME = "id";


    /**
     * 告警
     */
    String MONITOR_ALARM_FIELD_NAME = "alarm";

    /**
     * 云环境
     */
    String MONITOR_ALARM_NAME = "告警";

    /**
     * 云环境
     */
    String MONITOR_ALARM_EN_NAME = "alarm";

    /**
     * 任务名称
     */
    String JON_NAME = "jobName";

    /**
     * 任务id
     */
    String TASK_ID = "taskId";

    /**
     * 云环境id
     */
    String ENV_ID = "instanceId";

    /**
     * influxdb 默认策略
     */
    String INFLUXDB_DEFAULT_RETENTION_POLICY = "default";

    /**
     * influxdb 小时策略
     */
    String HOURLY_RETENTION_POLICY = "hourly";
    /**
     * influxdb 天策略
     */
    String INFLUXDB_DAILY_RETENTION_POLICY = "daily";


    /**
     * 性能统计资源编码前缀
     */
    String STATISTICS_EXAMINE_RESOURCE_CODE_PREFIX = "EXAMINE_RESOURCE";


    /**
     * 项目id
     */
    String PROJECT_ID_NAME = "projectId";

    /**
     * 项目id
     */
    String ORG_ID_NAME = "orgId";

    /**
     * 项目id
     */
    String INSTANCE_NAME = "name";

    /**
     * influxdb时间字段
     */
    String INFLUXDB_TIME_NAM = "time";

    /**
     * 监控key
     */
    String MONITOR_REDIS_KEY = "MONITOR:OPS-ALARM-DATA";


    /**
     * 指标查询模版
     */
    String MERGE_QUERY_SIMPLE =
            "select * into {dbName}.\"{retentionPolicy}\".{tableNames} " + "from ("
                    + "select count(value) as avgCount, "
                    + "mean(value) as value "
                    + "from \"{oldRetentionPolicy}\".{tableNames} "
                    + "where time < {endDate}ms and time >= {startDate}ms "
                    + " group by time({convergenceMode}), * tz('Asia/Shanghai')) group by *;"
                    +
                    "select * into {dbName}.\"{retentionPolicy}\".{tableNames}_max " + "from ("
                    + "select count(value) as maxCount, "
                    + "PERCENTILE(value, 95) as percentValue, max(value) as maxValue, "
                    + "mean(value) as avgMaxValue "
                    + "from \"{oldRetentionPolicy}\".{tableNames}_max "
                    + "where time < {endDate}ms and time >= {startDate}ms "
                    + " group by time({convergenceMode}), * tz('Asia/Shanghai')) group by *;"
                    +
                    "select * into {dbName}.\"{retentionPolicy}\".{tableNames}_min " + "from ("
                    + "select "
                    + "min(value) as minValue, mean(value) as avgMinValue "
                    + "from \"{oldRetentionPolicy}\".{tableNames}_min "
                    + "where time < {endDate}ms and time >= {startDate}ms "
                    + " group by time({convergenceMode}), * tz('Asia/Shanghai')) group by *;";

    /**
     * monitor web hook 地址
     */
    String WEB_HOOK_API = "api/v1/monitor/access/web/hook";

    /**
     * prometheus类型
     */
    String PROMETHEUS_TYPE = "prometheus";

    /**
     * 指标类型
     */
    String METRIC_TYPE = "metric";

    /**
     * 集群名称
     */
    String CLUSTER = "default";
}
