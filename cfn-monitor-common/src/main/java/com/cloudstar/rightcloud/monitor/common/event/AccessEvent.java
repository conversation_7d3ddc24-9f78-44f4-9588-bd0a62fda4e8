package com.cloudstar.rightcloud.monitor.common.event;

import com.cloudstar.rightcloud.monitor.common.em.AccessType;
import com.cloudstar.rightcloud.monitor.common.em.PrometheusType;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * prometheus事件
 *
 * @author: wanglang
 * @date: 2023/8/11 18:07
 */
public class AccessEvent extends ApplicationEvent {

    /**
     * 类型
     */
    private AccessType type;

    /**
     * 实例id
     */
    private String instanceId;

    public AccessEvent(String instanceId) {
        super(instanceId);
    }

    public AccessType getType() {
        return type;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public AccessEvent(AccessType type, String instanceId) {
        super(instanceId);
        this.instanceId = instanceId;
        this.type = type;
    }

    public static Builder type(AccessType type) {
        return new DefaultBuilder(type);
    }


    @Data
    public static class DefaultBuilder implements Builder {


        private AccessType type;

        private String instanceId;

        public DefaultBuilder(AccessType type) {
            this.type = type;
        }

        @Override
        public Builder type(AccessType type) {
            this.type = type;
            return this;
        }

        @Override
        public Builder instanceId(String instanceId) {
            this.instanceId = instanceId;
            return this;
        }

        @Override
        public AccessEvent build() {
            return new AccessEvent(this.type, this.instanceId);
        }
    }


    public interface Builder {

        /**
         * 类型
         *
         * @param type 类型
         * @return Builder 接口
         */
        Builder type(AccessType type);

        /**
         * 实例id
         *
         * @param instanceId 实例id
         * @return Builder 接口
         */
        Builder instanceId(String instanceId);

        /**
         * 构建事件
         *
         * @return PrometheusEvent 事件
         */
        AccessEvent build();
    }

}
