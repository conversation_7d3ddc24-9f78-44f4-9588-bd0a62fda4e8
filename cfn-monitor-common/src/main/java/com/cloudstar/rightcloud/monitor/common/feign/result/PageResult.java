package com.cloudstar.rightcloud.monitor.common.feign.result;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import lombok.Data;

/**
 * 分页返回值
 *
 * @param <T> 泛型
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> {

    /**
     * 数据
     */
    private List<T> list;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页数
     */
    private Integer pageSize;

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 总页码
     */
    private Integer totalPages;

    /**
     * 分页
     *
     * @return {@link PageResult }<{@link T }>
     */
    public static <T> PageResult<T> of() {
        return new PageResult<>();
    }
    /**
     * 分页
     *
     * @param list list
     * @param <T>  泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(List<T> list) {
        Page<T> page = (Page<T>) list;
        return pageHelperOf(page);
    }

    /**
     * 分页
     *
     * @param page list
     * @param <T>  泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(PageInfo<T> page, Class<T> clazz) {
        PageResult<T> tPage = new PageResult<>();
        tPage.setList(BeanUtil.copyToList(page.getList(), clazz));
        tPage.setPageNo(page.getPages());
        tPage.setPageSize(page.getPageSize());
        tPage.setTotal(Long.bitCount(page.getTotal()));
        tPage.setTotalPages(page.getPages());
        return tPage;
    }


    /**
     * 分页
     *
     * @param list list
     * @param <T>  泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(com.baomidou.mybatisplus.extension.plugins.pagination.Page<?> page, List<T> list) {
        PageResult<T> result = new PageResult<>();
        result.setList(list);
        result.setPageNo(Math.toIntExact(page.getCurrent()));
        result.setPageSize(Math.toIntExact(page.getSize()));
        result.setTotal(Math.toIntExact(page.getTotal()));
        result.setTotalPages(Math.toIntExact(page.getPages()));
        return result;
    }

    /**
     * 分页
     *
     * @param page  page
     * @param clazz class
     * @param <T>   泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(PageResult<?> page, Class<T> clazz) {
        PageResult<T> tPage = new PageResult<>();
        tPage.setList(BeanUtil.copyToList(page.list, clazz));
        tPage.setPageNo(page.pageNo);
        tPage.setPageSize(page.pageSize);
        tPage.setTotal(page.total);
        tPage.setTotalPages(page.totalPages);
        return tPage;
    }

    /**
     * 分页
     *
     * @param page page
     * @param <T>  泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        PageResult<T> tPage = new PageResult<>();
        tPage.setPageNo(Math.toIntExact(page.getCurrent()));
        tPage.setPageSize(Math.toIntExact(page.getSize()));
        tPage.setList(page.getRecords());
        tPage.setTotal(Math.toIntExact(page.getTotal()));
        tPage.setTotalPages(Math.toIntExact(page.getPages()));
        return tPage;
    }

    /**
     * 分页
     *
     * @param page  page
     * @param clazz class
     * @param <T>   泛型
     * @return 响应值
     */
    public static <T> PageResult<T> of(com.baomidou.mybatisplus.extension.plugins.pagination.Page<?> page,
            Class<T> clazz) {
        PageResult<T> tPage = new PageResult<>();
        tPage.setPageNo(Math.toIntExact(page.getCurrent()));
        tPage.setPageSize(Math.toIntExact(page.getSize()));
        tPage.setList(BeanUtil.copyToList(page.getRecords(), clazz));
        tPage.setTotal(Math.toIntExact(page.getTotal()));
        tPage.setTotalPages(Math.toIntExact(page.getPages()));
        return tPage;
    }

    /**
     * 分页
     *
     * @param page page
     * @param <T>  泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(Page<T> page) {
        return pageHelperOf(page);
    }

    /**
     * 分页
     *
     * @param page  page
     * @param clazz class
     * @param <T>   泛型
     * @return 返回值
     */
    public static <T> PageResult<T> of(Page<?> page, Class<T> clazz) {
        PageResult<T> tPage = new PageResult<>();
        tPage.setPageNo(page.getPageNum());
        tPage.setPageSize(page.getPageSize());
        tPage.setList(BeanUtil.copyToList(page.getResult(), clazz));
        tPage.setTotal(Math.toIntExact(page.getTotal()));
        tPage.setTotalPages(page.getPages());
        return tPage;
    }

    /**
     * 分页
     *
     * @param page page
     * @param <T>  泛型
     */
    private static <T> PageResult<T> pageHelperOf(Page<T> page) {
        PageResult<T> tPage = new PageResult<>();
        tPage.setPageNo(page.getPageNum());
        tPage.setPageSize(page.getPageSize());
        tPage.setList(page.getResult());
        tPage.setTotal(Math.toIntExact(page.getTotal()));
        tPage.setTotalPages(page.getPages());
        return tPage;
    }


}
