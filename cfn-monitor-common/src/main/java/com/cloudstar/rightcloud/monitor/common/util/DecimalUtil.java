package com.cloudstar.rightcloud.monitor.common.util;

import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DecimalUtil {

    /**
     * double 保留两位小数
     */
    public static String format2(double number) {
        DecimalFormat df2 = new DecimalFormat("0.00");
        return df2.format(number);
    }

    /**
     * float 保留两位小数
     */
    public static String format2(float number) {
        DecimalFormat df2 = new DecimalFormat("0.00");
        return df2.format(number);
    }

    /**
     * double保留两位小数
     *
     * @param d
     */
    public static double keepTwoDecimals(double d) {
        return (double) Math.round(d * 100) / 100;
    }

    /**
     * double保留三位小数
     *
     * @param d
     */
    public static double keepThreeDecimals(double d) {
        return (double) Math.round(d * 1000) / 1000;
    }

    /**
     * double保留四位小数
     *
     * @param d
     * @return
     */
    public static double keepFourDecimals(double d) {
        return (double) Math.round(d * 10000) / 10000;
    }

    public static Double keepTwo(Double d) {
        if (Objects.isNull(d)) {
            return d;
        }
        return keepTwoDecimals(d.doubleValue());
    }
}

