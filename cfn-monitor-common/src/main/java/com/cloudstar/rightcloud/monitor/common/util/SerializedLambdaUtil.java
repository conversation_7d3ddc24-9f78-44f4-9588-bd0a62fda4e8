package com.cloudstar.rightcloud.monitor.common.util;

import com.cloudstar.rightcloud.data.util.lambda.LambdaUtil;
import com.cloudstar.rightcloud.data.util.lambda.SFunction;
import com.cloudstar.rightcloud.data.util.lambda.SerializedLambda;
import org.apache.ibatis.reflection.property.PropertyNamer;

/**
 * 序列号工具类
 *
 * @author: wanglang
 * @date: 2023/11/6 20:50
 */
public class SerializedLambdaUtil {


    /**
     * MapperAutoObject::getField 获取string字段名
     *
     * @param column 属性
     * @return String 属性名
     */
    public static <T> String columnToString(SFunction<T, ?> column) {
        return getColumn(LambdaUtil.resolve(column));
    }

    /**
     * 获取序列号的对象的属性名
     *
     * @param lambda lambda对象
     * @return String 属性名
     */
    public static String getColumn(SerializedLambda lambda) {
        Class<?> aClass = lambda.getInstantiatedType();
        return PropertyNamer.methodToProperty(lambda.getImplMethodName());
    }
}
