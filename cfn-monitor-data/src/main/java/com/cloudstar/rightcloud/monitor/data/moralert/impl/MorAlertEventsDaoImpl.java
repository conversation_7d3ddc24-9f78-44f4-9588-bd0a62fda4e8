package com.cloudstar.rightcloud.monitor.data.moralert.impl;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.monitor.data.moralert.dao.MorAlertEventsDao;
import com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsProcessDto;
import com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsTableResultDto;
import com.cloudstar.rightcloud.monitor.data.moralert.dto.query.MorAlertEventsTablePageDto;
import com.cloudstar.rightcloud.monitor.data.moralert.mapper.MorAlertEventsMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@AllArgsConstructor
@Repository
public class MorAlertEventsDaoImpl implements MorAlertEventsDao {

    private MorAlertEventsMapper mapper;

    @Override
    public PageResult<MorAlertEventsTableResultDto> page(MorAlertEventsTablePageDto dto) {
        return PageHelperUtil.doSelectPageResult(dto, MorAlertEventsTableResultDto.class, () -> {
            mapper.list(dto);
        });
    }

    @Override
    public List<MorAlertEventsTableResultDto> list(MorAlertEventsTablePageDto dto) {
        return mapper.list(dto);
    }

    @Override
    public MorAlertEventsTableResultDto info(String hash) {
        MorAlertEventsTableResultDto infoCur = mapper.infoCur(hash);
        if (infoCur != null) {
            return infoCur;
        }
        MorAlertEventsTableResultDto infoHis = mapper.infoHis(hash);
        return infoHis;
    }

    @Override
    public Boolean processConfirm(MorAlertEventsProcessDto dto) {
        int i = mapper.processConfirmCur(dto);
        int j = mapper.processConfirmHis(dto);
        return i + j > 0;
    }

    @Override
    public Boolean processResolve(MorAlertEventsProcessDto dto) {
        int i = mapper.processResolveCur(dto);
        int j = mapper.processResolveHis(dto);
        return i + j > 0;
    }

    @Override
    public Boolean clear(String hashList) {
        int i = mapper.clearCur(hashList);
        int j = mapper.clearHis(hashList);
        return i + j > 0;
    }

}
