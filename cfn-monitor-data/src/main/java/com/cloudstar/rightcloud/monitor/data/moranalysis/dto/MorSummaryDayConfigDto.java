package com.cloudstar.rightcloud.monitor.data.moranalysis.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class MorSummaryDayConfigDto implements Serializable {

    /**
     * 执行时间
     */
    private String executeTime;
    /**
     * 统计策略日期
     */
    private String statisticStrategyDate;
    /**
     * 开始的小时时间点
     */
    private String startHour;
    /**
     * 结束的小时时间点
     */
    private String endHour;
    /**
     * 生成历史数据
     */
    private boolean generateHistoricalData;
    /**
     * 生成时间范围
     */
    private int generateTimeRange;
}
