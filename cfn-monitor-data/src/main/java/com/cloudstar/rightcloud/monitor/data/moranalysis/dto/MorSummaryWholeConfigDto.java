package com.cloudstar.rightcloud.monitor.data.moranalysis.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class MorSummaryWholeConfigDto implements Serializable {

    /**
     * 统计数据峰值
     */
    private String statisticDataPeak;
    /**
     * 峰值来源类型:  max 最大值 average 平均值
     */
    private String statisticPeakSourceType;
    /**
     * 时间段峰值数据统计方式 单位:  tantile 分位值 average 平均值 max 最大值
     */
    private String statisticPeakTargetType;
    /**
     * 分位值
     */
    private Integer statisticTantile;
    /**
     * 结束的小时时间点
     */
    private Boolean dataRangeBaselineEnable;
    /**
     * 小时数据汇总数据点基线时间点
     */
    private Double usageStartThreshold;
    /**
     * 小时数据汇总数据点基线开关
     */
    private Boolean hourBaselineEnable;
    /**
     * 小时数据汇总数据点基线时间
     */
    private Integer hourThreshold;
    /**
     * 天数据汇总数据点基线开关
     */
    private Boolean dayBaselineEnable;
    /**
     * 天数据汇总数据点基线时间
     */
    private Integer dataRangeThreshold;
    /**
     * 时间段统计数据点基时间
     */
    private Double usageEndThreshold;
    /**
     * 时间段统计数据点基
     */
    private Boolean usageBaselineEnable;
    /**
     * 结束的小时时间点
     */
    private Integer dayThreshold;

}
