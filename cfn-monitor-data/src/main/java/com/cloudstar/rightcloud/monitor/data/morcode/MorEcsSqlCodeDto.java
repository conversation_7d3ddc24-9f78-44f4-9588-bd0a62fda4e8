package com.cloudstar.rightcloud.monitor.data.morcode;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 云主机动态sql参数
 *
 * @author: hjy
 * @date: 2023/12/18 17:24
 */

@Data
public class MorEcsSqlCodeDto implements Serializable {

    /**
     * 资源类型编码
     */
    private String resTypeCode;
    /**
     * 资源类型编码英文名称
     */
    private String resTypeName;
    /**
     * 资源类型编码
     */
    private String resTypeEnName;
    /**
     * 资源类型分类code
     */
    private String resTypeCodeCategory;

    /**
     * 组织/项目关键字配置
     */
    private List<MorKeywordConfigDto> orgKeywordConfig;
    /**
     * 资源关键字配置
     */
    private List<MorKeywordConfigDto> resourceKeywordConfig;
    /**
     * 资源闲置率关键字配置
     */
    private List<MorKeywordConfigDto> resPoolKeywordConfig;
    /**
     * 汇总指标项配置
     */
    private List<MorMetricConfigDto> metricConfig;
    /**
     * 数据库配置
     */
    private MorDatabaseConfigDto databaseConfig;
    /**
     * 组织维度列表需要展示的列
     */
    private List<MorHeaderDto> orgHeader;
    /**
     * 项目维度列表需要展示的列
     */
    private List<MorHeaderDto> projectHeader;
    /**
     * 资源维度列表需要展示的列
     */
    private List<MorHeaderDto> resourceHeader;
    /**
     * 资源池维度列表需要展示的列
     */
    private List<MorHeaderDto> resPoolHeader;
    /**
     * 统计推荐规格项规则
     */
    private List<MorStatSuggestSpecRuleDto> optimizationSuggestionsSpecRule;
    /**
     * 维度
     */
    private List<MorDimensionDto> dimension;
    /**
     * 规格项配置
     */
    private List<MorSpecConfigDto> specConfig;
    /**
     * 考核评分分项配置
     */
    private List<MorScoreDto> score;
}