package com.cloudstar.rightcloud.monitor.data.moroptimization.dto;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import lombok.Data;

import java.util.List;

/**
 * 优化建议数据
 *
 * <AUTHOR> Lesao
 * @date : 2023/12/12
 */
@Data
public class OptimizationDataListDto extends PageForm {


    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 资源UUID
     */
    private String resUuid;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    /**
     * 优化类型
     */
    private String optimizationType;

    /**
     * 资源类型
     */
    private String resType;

    /**
     * 云环境ID
     */
    private List<Long> cloudEnvIdIn;

    /**
     * 组织id
     */
    private List<Long> orgIdIn;

    /**
     * 策略ID
     */
    private Long strategyId;
}
