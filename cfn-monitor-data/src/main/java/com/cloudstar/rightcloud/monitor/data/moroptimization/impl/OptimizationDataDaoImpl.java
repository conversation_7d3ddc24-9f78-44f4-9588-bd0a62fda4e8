package com.cloudstar.rightcloud.monitor.data.moroptimization.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.data.datascope.annotation.DataFilter;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dao.OptimizationDataDao;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationData;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationDataListDto;
import com.cloudstar.rightcloud.monitor.data.moroptimization.mapper.OptimizationDataMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 优化建议数据
 *
 * <AUTHOR> Lesao
 * @date : 2023/11/29
 */
@Repository
public class OptimizationDataDaoImpl implements OptimizationDataDao {

    @Resource
    private OptimizationDataMapper optimizationDataMapper;

    @Override
    public void insert(OptimizationData optimizationData) {
        optimizationDataMapper.insert(optimizationData);
    }

    @Override
    public List<OptimizationData> getByStrategyId(Long strategyId) {
        return optimizationDataMapper.selectList(Wrappers.lambdaQuery(OptimizationData.class)
                .eq(OptimizationData::getStrategyId, strategyId)
        );
    }

    @Override
    public void deleteByStrategyId(Long strategyId) {
        optimizationDataMapper.delete(Wrappers.lambdaQuery(OptimizationData.class)
                .eq(OptimizationData::getStrategyId, strategyId)
        );
    }

    @Override
    public void batchDelete(List<Long> ids) {
        optimizationDataMapper.deleteBatchIds(ids);
    }

    @Override
    @DataFilter(ignoreProjectFilter = false, ignoreProjectEnvTypeFilter = true)
    public PageResult<OptimizationData> page(OptimizationDataListDto listDto) {
        return PageHelperUtil.doSelectPageResult(listDto, "created_dt desc ", OptimizationData.class,
                () -> optimizationDataMapper.selectList(Wrappers.lambdaQuery(OptimizationData.class)
                        .eq(StrUtil.isNotBlank(listDto.getResType()), OptimizationData::getResType, listDto.getResType())
                        .eq(Objects.nonNull(listDto.getOrgId()), OptimizationData::getOrgId, listDto.getOrgId())
                        .eq(Objects.nonNull(listDto.getProjectId()), OptimizationData::getProjectId, listDto.getProjectId())
                        .eq(Objects.nonNull(listDto.getResUuid()), OptimizationData::getResUuid, listDto.getResUuid())
                        .eq(Objects.nonNull(listDto.getCloudEnvId()), OptimizationData::getCloudEnvId, listDto.getCloudEnvId())
                        .eq(Objects.nonNull(listDto.getOptimizationType()), OptimizationData::getOptimizationType, listDto.getOptimizationType())
                        .like(Objects.nonNull(listDto.getResName()), OptimizationData::getResName, listDto.getResName())
                )
        );
    }

    @Override
    public List<OptimizationData> list(OptimizationDataListDto listDto) {
        return optimizationDataMapper.selectList(Wrappers.lambdaQuery(OptimizationData.class)
                .eq(StrUtil.isNotBlank(listDto.getResType()), OptimizationData::getResType, listDto.getResType())
                .eq(Objects.nonNull(listDto.getOrgId()), OptimizationData::getOrgId, listDto.getOrgId())
                .eq(Objects.nonNull(listDto.getProjectId()), OptimizationData::getProjectId, listDto.getProjectId())
                .eq(Objects.nonNull(listDto.getResUuid()), OptimizationData::getResUuid, listDto.getResUuid())
                .eq(Objects.nonNull(listDto.getCloudEnvId()), OptimizationData::getCloudEnvId, listDto.getCloudEnvId())
                .eq(Objects.nonNull(listDto.getOptimizationType()), OptimizationData::getOptimizationType, listDto.getOptimizationType())
                .like(Objects.nonNull(listDto.getResName()), OptimizationData::getResName, listDto.getResName())
                .eq(Objects.nonNull(listDto.getStrategyId()), OptimizationData::getStrategyId, listDto.getStrategyId())
                .in(CollectionUtil.isNotEmpty(listDto.getOrgIdIn()), OptimizationData::getOrgId, listDto.getOrgIdIn())
                .in(CollectionUtil.isNotEmpty(listDto.getCloudEnvIdIn()), OptimizationData::getCloudEnvId, listDto.getCloudEnvIdIn())
        );
    }

    @Override
    @DataFilter(ignoreProjectFilter = false, ignoreProjectEnvTypeFilter = true)
    public List<OptimizationData> dataFilterList(OptimizationDataListDto listDto) {
        return optimizationDataMapper.selectList(Wrappers.lambdaQuery(OptimizationData.class)
                .eq(StrUtil.isNotBlank(listDto.getResType()), OptimizationData::getResType, listDto.getResType())
                .eq(Objects.nonNull(listDto.getOrgId()), OptimizationData::getOrgId, listDto.getOrgId())
                .eq(Objects.nonNull(listDto.getProjectId()), OptimizationData::getProjectId, listDto.getProjectId())
                .eq(Objects.nonNull(listDto.getResUuid()), OptimizationData::getResUuid, listDto.getResUuid())
                .eq(Objects.nonNull(listDto.getCloudEnvId()), OptimizationData::getCloudEnvId, listDto.getCloudEnvId())
                .eq(Objects.nonNull(listDto.getOptimizationType()), OptimizationData::getOptimizationType, listDto.getOptimizationType())
                .like(Objects.nonNull(listDto.getResName()), OptimizationData::getResName, listDto.getResName())
                .eq(Objects.nonNull(listDto.getStrategyId()), OptimizationData::getStrategyId, listDto.getStrategyId())
                .in(CollectionUtil.isNotEmpty(listDto.getOrgIdIn()), OptimizationData::getOrgId, listDto.getOrgIdIn())
                .in(CollectionUtil.isNotEmpty(listDto.getCloudEnvIdIn()), OptimizationData::getCloudEnvId, listDto.getCloudEnvIdIn())
        );
    }

    @Override
    public OptimizationData getById(Long id) {
        return optimizationDataMapper.selectById(id);
    }
}
