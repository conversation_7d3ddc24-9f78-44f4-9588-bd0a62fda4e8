package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 评分规则
 *
 * @author: hjy
 * @date: 2023/10/31 20:26
 */
@Data
public class MorExamineRuleDto implements Serializable {

    /**
     * 开始条件(大于:gt,小于:lt,等于:eq,大于等于:ge,小于等于:le)
     */
    private String startOperate;

    /**
     * 开始值
     */
    private Double startValue;

    /**
     * 结束条件(大于:gt,小于:lt,等于:eq,大于等于:ge,小于等于:le)
     */
    private String endOperate;

    /**
     * 结束值
     */
    private Double endValue;

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 建议
     */
    private String advice;

}
