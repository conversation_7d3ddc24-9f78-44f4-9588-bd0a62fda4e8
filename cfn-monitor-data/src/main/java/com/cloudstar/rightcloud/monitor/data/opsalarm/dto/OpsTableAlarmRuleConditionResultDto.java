package com.cloudstar.rightcloud.monitor.data.opsalarm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>描述: [OpsAlarmRuleCondition 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_alarm_rule_condition")
public class OpsTableAlarmRuleConditionResultDto extends MapperAutoObject implements Serializable {
    private static final long serialVersionUID = -89379610293842209L;
    /**
    * id;id
    */
    private Long id;


    /**
    * 告警规则id
    */
    private Long opsAlarmRuleId;
    /**
    * 监控采集指标id
    */
    private Long opsMonitorMetricId;

    /**
     * 函数运算符 sum 求和 max 最大值 min 最小值 avg 平均值
     */
    private String functionOperator;

    /**
    * 运算符 gt > ,ge >= ,lt< ,le <=, equals =, not_equals!=
    */
    private String operator;
    /**
    * 阔值
    */
    private String broadValue;
    /**
    * 数据范围时间（分钟）
    */
    private Integer dataRangeTime;

}
