package com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query;

import com.cloudstar.rightcloud.monitor.common.annotation.WrapperField;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 通知对象联系人组与联系人关联
 *
 * @author: wanglang
 * @date: 2023/2/14 4:23 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
public class OpsNotifyTargetGroupContactsRelQueryDto implements Serializable {
    /**
     * id;id
     */
    private Long id;
    /**
     * 通知对象联系人id
     */
    private Long opsNotifyTargetContactsId;

    /**
     * 通知对象联系人id
     */
    @WrapperField(type = WrapperType.IN, value = "opsNotifyTargetContactsId")
    private Long opsNotifyTargetContactsIds;

    /**
     * 通知对象联系人组id
     */
    private Long opsNotifyTargetGroupId;

    /**
     * 通知对象联系人组id
     */
    @WrapperField(type = WrapperType.IN, value = "opsNotifyTargetGroupId")
    private List<Long> opsNotifyTargetGroupIds;
    /**
     * 版本号
     */
    private Long version;

}
