package com.cloudstar.rightcloud.monitor.data.opsalarm.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmRuleTargetQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmRuleTargetResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsAlarmRuleTargetMapper;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmRuleTargetDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>描述: [OpsAlarmRuleTarget 服务实现层] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@AllArgsConstructor
@Repository
public class OpsAlarmRuleTargetDaoImpl implements OpsAlarmRuleTargetDao {

    private OpsAlarmRuleTargetMapper opsAlarmRuleTargetMapper;


    @Override
    public Boolean updateList(List<OpsTableAlarmRuleTargetResultDto> entityList) {
        Boolean resutl = true;
        final List<OpsTableAlarmRuleTargetResultDto> updateEntityList = entityList.stream()
                .filter(Objects::nonNull)
                .filter(opsTableAlarmRuleTargetResultDto -> {
                    return Objects.nonNull(opsTableAlarmRuleTargetResultDto.getId());
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateEntityList)) {
            resutl = opsAlarmRuleTargetMapper.updateBatch(updateEntityList);
        }
        final List<OpsTableAlarmRuleTargetResultDto> insertEntityList = entityList.stream()
                .filter(Objects::nonNull)
                .filter(opsTableAlarmRuleTargetResultDto -> {
                    return Objects.isNull(opsTableAlarmRuleTargetResultDto.getId());
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(insertEntityList)) {
            resutl = opsAlarmRuleTargetMapper.insertBatch(insertEntityList);
        }
        return resutl;
    }

    @Override
    public Boolean delete(OpsAlarmRuleTargetQueryDto query) {
        return opsAlarmRuleTargetMapper.delete(query) > 0;
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        return opsAlarmRuleTargetMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertList(List<OpsTableAlarmRuleTargetResultDto> entityList) {
        return opsAlarmRuleTargetMapper.insertBatch(entityList);
    }

    @Override
    public List<OpsTableAlarmRuleTargetResultDto> selectList(OpsAlarmRuleTargetQueryDto query) {
        return opsAlarmRuleTargetMapper.selectList(query);
    }

    @Override
    public OpsTableAlarmRuleTargetResultDto selectOne(OpsAlarmRuleTargetQueryDto query) {
        return opsAlarmRuleTargetMapper.selectOne(query);
    }
}
