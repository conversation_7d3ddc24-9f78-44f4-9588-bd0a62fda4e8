package com.cloudstar.rightcloud.monitor.data.opscollectrule.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>描述: [OpsMonitorMetric 实体类] </p>
 * <p>创建时间: 2023/04/19 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_collect_metric")
public class OpsTableCollectMetricResultDto extends MapperAutoObject implements Serializable {
    private static final long serialVersionUID = 910202086580857277L;
    /**
     * id
     */
    private Long id;
    /**
     * 采集指标名称
     */
    private String name;
    /**
     * 采集指标名称
     */
    private String nameEn;

    /**
     * 状态  enable 启用 disable 禁用
     */
    private String status;

    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 采集规则id
     */
    private Long opsCollectRuleId;
    /**
     * 采集规则维度id
     */
    private Long opsCollectRuleDimensionsId;

    /**
     * 采集指标统一编码id
     */
    private Long monitorCommonMetricId;

    /**
     * 采集指标统一编码名称
     */
    @TableField(exist = false)
    private String monitorCommonMetricName;

    /**
     * 采集指标统一编码名称
     */
    @TableField(exist = false)
    private String monitorCommonMetricNameEn;
    /**
     * 采集指标统一编码
     */
    @TableField(exist = false)
    private String unifiedCoding;
    /**
     * 采集指标原始编码
     */
    private String originalCoding;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;

    /**
     * 指标单位
     */
    private String unitEn;
    /**
     * 排序编码
     */
    private Integer sortRank;

    /**
     * 描述
     */
    private String description;

    /**
     * 描述
     */
    private String descriptionEn;
    /**
     * 单位转换因子
     */
    private Double unitConvFactor;
    /**
     * 指标命名空间
     */
    private String namespace;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * uuid
     */
    private String uuid;


    /**
     * 采集组件id
     */
    private Long opsExporterId;

}
