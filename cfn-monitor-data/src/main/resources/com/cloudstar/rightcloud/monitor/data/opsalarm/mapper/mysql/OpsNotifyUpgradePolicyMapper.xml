<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsNotifyUpgradePolicyMapper">

    <resultMap type="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyUpgradePolicyResultDto" id="OpsNotifyUpgradePolicyMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="VARCHAR"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR" />
    </resultMap>
    
</mapper>
