package com.cloudstar.rightcloud.monitor.client.feign.server;


import com.cloudstar.rightcloud.monitor.client.feign.server.request.ComputeCardEntityListReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.ComputeCardEntityPageReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.ManagerListSelectReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.NotifyRecordRequest;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryClusterEntityListReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryClusterEntityReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryClusterNodeListReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryClusterNodeReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryNotebookListReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryTrainingJobEntityListReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.QueryTrainingJobEntityReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.SendNotifyRequest;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ClusterEntityListResp;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ClusterEntityResp;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ClusterNodeResp;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ComputeCardEntityListResp;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ComputeCardEntityPageResp;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ManagerDetailResponse;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.NotebookPageResp;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.NotifyRecordResponse;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.TrainingJobEntityResp;
import com.cloudstar.rightcloud.monitor.common.feign.result.PageForm;
import com.cloudstar.rightcloud.monitor.common.feign.result.PageResult;
import com.cloudstar.rightcloud.monitor.common.feign.result.Rest;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 运营服务
 *
 * <AUTHOR>
 * @date 2025/06/0414:41
 */
@FeignClient(name = "cfn-server", url = "${feign.url.cfn-server:127.0.0.1:9001}", path = "/api/v1/server",
        fallbackFactory = ServerClient.ServerClientFallbackFactory.class)
public interface ServerClient {

    /**
     * 查询集群节点列表信息
     *
     * @return 响应值
     */
    @GetMapping("/cluster_node/list")
    Rest<List<ClusterNodeResp>> list(@SpringQueryMap QueryClusterNodeListReq req);

    /**
     * 分页查询集群节点列表信息
     *
     * @return 响应值
     */
    @GetMapping("/cluster_node")
    Rest<PageResult<ClusterNodeResp>> page(@SpringQueryMap QueryClusterNodeReq req, @SpringQueryMap PageForm pageForm);

    /**
     * 分页查询管理员列表
     */
    @GetMapping("/manager")
    Rest<PageResult<ManagerDetailResponse>> getManagerList(@SpringQueryMap ManagerListSelectReq req);

    /**
     * 查询管理员列表
     */
    @GetMapping("/manager/list")
    Rest<List<ManagerDetailResponse>> getManagers(@SpringQueryMap ManagerListSelectReq req);

    /**
     * 发送告警消息
     */
    @PostMapping("/notify/send_alarm_notify")
    Rest sendAlarmNotify(@RequestBody SendNotifyRequest request);

    /**
     * 查询训练作业
     */
    @GetMapping("/training")
    Rest<PageResult<TrainingJobEntityResp>> getTrainingPage(@SpringQueryMap QueryTrainingJobEntityReq req,
                                                 @SpringQueryMap PageForm pageForm);

    /**
     * 查询训练作业列表信息
     *
     * @return 响应值
     */
    @GetMapping("/training/list")
    Rest<List<TrainingJobEntityResp>> getTrainingJobList(@SpringQueryMap QueryTrainingJobEntityListReq req);

    /**
     * 查询开发环境列表信息
     *
     * @return 响应值
     */
    @GetMapping("/notebook/list")
    Rest<List<NotebookPageResp>> getNotebookList(@SpringQueryMap QueryNotebookListReq req);

    /**
     * 分页查询集群
     */
    @GetMapping("/cluster_entity")
    Rest<PageResult<ClusterEntityResp>> getClusterPage(@SpringQueryMap QueryClusterEntityReq req,
                                                       @SpringQueryMap PageForm pageForm);

    /**
     * 查询集群列表
     */
    @GetMapping("/cluster_entity/list")
    Rest<List<ClusterEntityListResp>> getClusterList(@SpringQueryMap QueryClusterEntityListReq req);

    /**
     * 查询计算卡列表
     */
    @GetMapping("/compute/card/entity/list")
    Rest<List<ComputeCardEntityListResp>> getComputeCardList(@SpringQueryMap ComputeCardEntityListReq req);

    /**
     * 分页查询计算卡
     */
    @GetMapping("/compute/card/entity")
    Rest<PageResult<ComputeCardEntityPageResp>> getComputeCardPage(@SpringQueryMap ComputeCardEntityPageReq req,
                                                                          @SpringQueryMap PageForm pageForm);

    /**
     * 获取通知记录列表
     */
    @GetMapping("/notify/record/list")
    Rest<PageResult<NotifyRecordResponse>> getNotifyRecordPage(@SpringQueryMap NotifyRecordRequest notifyRecordRequest,
                                                               @SpringQueryMap PageForm pageForm);


    @Slf4j
    @Component
    class ServerClientFallbackFactory implements FallbackFactory<ServerClient> {

        @Override
        public ServerClient create(Throwable cause) {
            log.error("调用ServerClient失败", cause);
            return new ServerClient() {

                @Override
                public Rest<List<ClusterNodeResp>> list(QueryClusterNodeListReq req) {
                    return null;
                }

                @Override
                public Rest<PageResult<ClusterNodeResp>> page(QueryClusterNodeReq req, PageForm pageForm) {
                    return null;
                }

                @Override
                public Rest<PageResult<ManagerDetailResponse>> getManagerList(ManagerListSelectReq req) {
                    return null;
                }

                @Override
                public Rest sendAlarmNotify(@RequestBody SendNotifyRequest request) {
                    log.error("发送告警通知错误");
                    return null;
                }

                @Override
                public Rest<PageResult<TrainingJobEntityResp>> getTrainingPage(@SpringQueryMap QueryTrainingJobEntityReq req,
                                                                    @SpringQueryMap PageForm pageForm) {
                    return null;
                }

                @Override
                public Rest<List<TrainingJobEntityResp>> getTrainingJobList(@SpringQueryMap QueryTrainingJobEntityListReq req) {
                    return null;
                }

                @Override
                public Rest<List<NotebookPageResp>> getNotebookList(@SpringQueryMap QueryNotebookListReq req) {
                    return null;
                }

                @Override
                public Rest<List<ManagerDetailResponse>> getManagers(ManagerListSelectReq req) {
                    return null;
                }

                @Override
                public Rest<PageResult<ClusterEntityResp>> getClusterPage(@SpringQueryMap QueryClusterEntityReq req,
                                                                          @SpringQueryMap PageForm pageForm) {
                    return null;
                }

                @Override
                public Rest<List<ClusterEntityListResp>> getClusterList(@SpringQueryMap QueryClusterEntityListReq req) {
                    return null;
                }

                @Override
                public Rest<List<ComputeCardEntityListResp>> getComputeCardList(
                        @SpringQueryMap ComputeCardEntityListReq req) {
                    return null;
                }

                public Rest<PageResult<ComputeCardEntityPageResp>> getComputeCardPage(
                        @SpringQueryMap ComputeCardEntityPageReq req, @SpringQueryMap PageForm pageForm) {
                    return null;
                }

                public Rest<PageResult<NotifyRecordResponse>> getNotifyRecordPage(
                        @SpringQueryMap NotifyRecordRequest notifyRecordRequest, @SpringQueryMap PageForm pageForm) {
                    return null;
                }
            };
        }
    }
}
