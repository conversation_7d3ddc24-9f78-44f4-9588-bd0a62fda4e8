package com.cloudstar.rightcloud.monitor.client.moralert.result;

import com.cloudstar.rightcloud.common.utils.excel.ExcelField;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 自监控告警导出实体类
 * <AUTHOR>
 * @date 2025/1/17 11:32
 */
@Data
public class MorAlertEventsExcelResult implements Serializable {
    /**
     * 告警级别
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_LEVEL)
    private String severityStr;

    /**
     * 告警名称
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_NAME)
    private String ruleName;

    /**
     * 告警对象
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_TARGET)
    private String target;

    /**
     * 告警状态
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_STATUS)
    private String statusStr;

    /**
     * 开始时间
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_START_TIME)
    private Date firstTriggerTimeDate;

    /**
     * 持续时长
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_DURATION)
    private String durationStr;

    /**
     * 最新发生时间
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_OCCUR_TIME)
    private Date triggerTimeDate;

    /**
     * 告警恢复时间
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_RECOVER_DATE)
    private Date recoverTimeDate;

    /**
     * 告警处理状态
     */
    @ExcelField(propertyKey = MonitorFieldKeyConstant.ALARM_DATA_PROCESSING_STATUS)
    private String processingStatus;

}
