package com.cloudstar.rightcloud.monitor.client.mordatasource.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据源
 *
 * @author: wanglang
 * @date: 2024/4/15 14:34
 */
@Data
public class MorDatasourceGetDefaultResult implements Serializable {
    /**
     * id;id
     */
    private Long id;
    /**
     * 名称;名称
     */
    private String name;
    /**
     * 状态;状态
     */
    private String status;
    /**
     * 类型;类型
     */
    private String type;
    /**
     * http配置
     */
    private String httpConfig;
    /**
     * 认证配置
     */
    private String authConfig;
    /**
     * 是否默认;是否默认
     */
    private Boolean isDefault;
    /**
     * 描述;描述
     */
    private String description;

    /**
     * 创建用户ID
     */
    private String createdBy;
    /**
     * 记录创建时间
     */
    private Date createdDt;
    /**
     * 最后修改用户ID
     */
    private String updatedBy;
    /**
     * 记录修改时间
     */
    private Date updatedDt;
    /**
     * 版本号
     */
    private Long version;
}
