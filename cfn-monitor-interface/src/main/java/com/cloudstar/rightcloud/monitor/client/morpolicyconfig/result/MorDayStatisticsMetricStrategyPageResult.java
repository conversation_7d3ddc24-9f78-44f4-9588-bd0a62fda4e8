package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result;

import com.cloudstar.rightcloud.monitor.client.morcomonmetric.result.MorCommonMetricGetListResult;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 天性能汇总指标策略返回结果
 *
 * @author: hjy
 * @date: 2023/11/9 11:05
 */
@Data
public class MorDayStatisticsMetricStrategyPageResult implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;
    /**
     * 云平台类型
     */
    private String envType;
    /**
     * 云平台类型名称
     */
    private String envTypeName;
    /**
     * 云平台类型英文名称
     */
    private String envTypeNameEn;
    /**
     * 资源类型
     */
    private String resTypeCode;
    /**
     * 统计指标项
     */
    private String performanceMetricIndicatorCode;
    /**
     * 状态
     */
    private String status;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建用户ID
     */
    private String createdBy;
    /**
     * 记录创建时间
     */
    private Date createdDt;
    /**
     * 最后修改用户ID
     */
    private String updatedBy;
    /**
     * 记录修改时间
     */
    private Date updatedDt;
    /**
     * 版本号
     */
    private String version;

    /**
     * 监控指标列表
     */
    private List<MorCommonMetricGetListResult> collectMetrics;

}
