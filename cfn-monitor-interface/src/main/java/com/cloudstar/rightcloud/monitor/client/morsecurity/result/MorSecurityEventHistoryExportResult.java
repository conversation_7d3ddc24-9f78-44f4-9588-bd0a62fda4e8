package com.cloudstar.rightcloud.monitor.client.morsecurity.result;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.utils.excel.ExcelField;
import com.cloudstar.rightcloud.monitor.common.msg.field.ResFieldKeyConstant;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MorSecurityEventHistoryExportResult implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 原生ID
     */
    private String originId;

    /**
     * 安全事件名称
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.EVENT_NAME)
    @SafeHtml
    private String eventName;

    /**
     * 安全事件类型;remote_code_execution：远程代码命令执行、directory_traversal_attack：目录遍历攻击、general_cyber_attack：通用网络攻击、
     * unserialize_attack：反序列化攻击、web_vulnerability_attack：web漏洞攻击、account_brute_force_cracking：账号暴力破解、
     * Injection_attack：注入型攻击、account_weak_password：账号弱口令、scanning_detection：扫描探测、telnet：远程登录、 malicious_backdoor：恶意后门
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.EVENT_TYPE)
    private String eventType;

    /**
     * 安全事件级别;urgency:紧急、significance:严重、 ordinary一般
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.EVENT_LEVEL)
    private String eventLevel;

    /**
     * 安全事件可信等级;high：高，middle：中、low:低
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.RELIABILITY_LEVEL)
    private String reliabilityLevel;

    /**
     * 安全事件发生时间
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.EVENT_TIME)
    private Date eventTime;

    /**
     * 安全事件目标IP
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.DES_IP)
    private String desIp;

    /**
     * 安全事件目标端口
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.DES_PORT)
    private String desPort;

    /**
     * 目标云主机资源id
     */
    private String resourceId;

    /**
     * 安全事件源IP
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.SRC_IP)
    private String srcIp;

    /**
     * 安全事件源端口
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.SRC_PORT)
    private String srcPort;

    /**
     * 关联组织id
     */
    private Long orgId;

    private Long projectId;

    /**
     * 关联业务系统id
     */
    private String bizSystemId;

    /**
     * 确认人
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.CONFIRMUSER)
    private String confirmUser;

    /**
     * 确认人id
     */
    private String confirmUserId;

    /**
     * 确认时间
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.CONFIRM_TIME)
    private Date confirmTime;

    /**
     * 确认详情
     */
    private String confirmContent;

    /**
     * 解决人
     */
    private String resolveUser;

    /**
     * 解决id
     */
    private String resolveUserId;

    /**
     * 解决时间
     */
    private Date resolveTime;

    /**
     * 恢复时间
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.RECOVERED_TIME)
    private Date recoveredTime;

    /**
     * 解决详情
     */
    private String resolveContent;

    /**
     * 安全事件状态 1:待确认、2:处理中、3:已解决、4:已清除
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.STATUS)
    private String status;

    /**
     * 资源名称
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.RESOURCE_NAME)
    @SafeHtml
    private String resourceName;

    /**
     * 所属组织名称
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.ORG_NAME)
    @SafeHtml
    private String orgName;

    /**
     * 所属项目名称
     */
    @ExcelField(propertyKey = ResFieldKeyConstant.PROJECT_NAME)
    @SafeHtml
    private String projectName;

    /**
     * 记录创建时间
     */
    private Date createdDt;

    /**
     * 创建用户
     */
    private String createdBy;

    /**
     * 最后修改用户
     */
    private String updatedBy;

    /**
     * 记录修改时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;
}
