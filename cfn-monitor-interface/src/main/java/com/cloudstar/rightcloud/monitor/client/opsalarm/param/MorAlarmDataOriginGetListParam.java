package com.cloudstar.rightcloud.monitor.client.opsalarm.param;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 原生告警列表查询数据
 *
 * @author: wanglang
 * @date: 2024/4/7 15:16
 */
@Data
public class MorAlarmDataOriginGetListParam extends PageForm {

    /**
     * 开始发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startOccurTime;
    /**
     * 结束发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endOccurTime;


    /**
     * 持续时长
     */
    private String duration;

    /**
     * 告警名称
     */
    private String name;
}
