package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import java.io.Serializable;
import java.util.Date;

import com.cloudstar.rightcloud.common.annotation.BeanHelperField;

import lombok.Data;

/**
 * 告警规则分页返回体
 *
 * @author: wanglang
 * @date: 2023/6/7 8:20 PM
 */
@Data
public class OpsAlarmRulePageResult implements Serializable {


    /**
     * id
     */
    private Long id;

    /**
     * 告警名称
     */
    private String name;

    /**
     * 对象类型
     */
    @BeanHelperField(columnName = "alarmTargetType")
    private String targetType;

    /**
     * 对象范围
     */
    @BeanHelperField(columnName = "alarmTargetScope")
    private String targetScope;

    /**
     * 告警级别
     */
    @BeanHelperField(columnName = "alarmLevelStatus")
    private String alarmLevel;

    /**
     * 通知策略
     */
    private String opsNotifyPolicy;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 执行频率 单位 s
     */
    private Integer promEvalInterval;

    /**
     * 留观时长 单位 s
     */
    private Integer recoverDuration;
}
