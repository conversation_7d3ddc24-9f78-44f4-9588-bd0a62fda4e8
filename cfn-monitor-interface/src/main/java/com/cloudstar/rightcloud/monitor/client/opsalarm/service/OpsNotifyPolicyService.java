package com.cloudstar.rightcloud.monitor.client.opsalarm.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsNotifyPolicyDetailResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsNotifyPolicyCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsNotifyPolicyGetNameParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsNotifyPolicyQueryParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsNotifyPolicyUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsNotifyPolicyGetByNameResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsNotifyPolicyPageResult;

import java.util.List;

/**
 * 告警通知策略
 *
 * @author: 卢泳舟
 * @date: 2023/6/5 9:56
 */
public interface OpsNotifyPolicyService {

    /**
     * 查询通知策略（分页）
     *
     * @param queryNotifyPolicyParam 通知策略查询条件
     * @return OpsNotifyPolicyPageResult 通知策略信息
     */
    RightCloudResult<PageResult<OpsNotifyPolicyPageResult>> getNotifyPolicyPage(OpsNotifyPolicyQueryParam queryNotifyPolicyParam);

    /**
     * 查询告警通知策略详情
     *
     * @param id 通知策略id
     * @return OpsNotifyPolicyDetailResult 告警通知策略详情数据
     */
    RightCloudResult<OpsNotifyPolicyDetailResult> getNotifyPolicyDetails(Long id);

    /**
     * 创建通知策略
     *
     * @param opsNotifyPolicyCreateParam 通知策略信息
     * @return String id 创建成功数据id
     */
    RightCloudResult<Long> createNotifyPolicy(OpsNotifyPolicyCreateParam opsNotifyPolicyCreateParam);

    /**
     * 编辑通知策略
     *
     * @param opsNotifyPolicyUpdateParam 通知策略信息
     * @return Boolean true 成功 false 失败
     */
    RightCloudResult<Boolean> updateNotifyPolicy(OpsNotifyPolicyUpdateParam opsNotifyPolicyUpdateParam);

    /**
     * 禁用/启用通知策略
     *
     * @param id 通知策略id
     * @return Boolean true 成功 false 失败
     */
    RightCloudResult<Boolean> updateNotifyPolicyStatus(Long id);

    /**
     * 删除通知策略
     *
     * @param ids 通知策略id
     * @return Boolean true 成功 false 失败
     */
    RightCloudResult<Boolean> deleteNotifyPolicy(List<Long> ids);


    /**
     * 获取告警通知策略数据
     *
     * @param param 告警通知策略筛选条件
     * @return OpsNotifyPolicyGetResult 告警通知策略数据
     */
    RightCloudResult<List<OpsNotifyPolicyGetByNameResult>> getNotifyPolicyList(OpsNotifyPolicyGetNameParam param);


}
