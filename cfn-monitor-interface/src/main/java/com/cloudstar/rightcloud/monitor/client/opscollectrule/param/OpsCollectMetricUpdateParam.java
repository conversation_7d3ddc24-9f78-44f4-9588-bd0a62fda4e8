package com.cloudstar.rightcloud.monitor.client.opscollectrule.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 监控指标创建
 *
 * @author: wanglang
 * @date: 2023/6/16 15:57
 */
@Data
public class OpsCollectMetricUpdateParam implements Serializable {


    private Long id;

    /**
     * 采集指标名称
     */
    private String name;

    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 采集规则id
     */
    private Long opsCollectRuleId;
    /**
     * 维度id
     */
    private Long opsCollectRuleDimensionsId;

    /**
     * 状态
     */
    private String status;
    /**
     * 采集指标统一编码id
     */
    private Long monitorCommonMetricId;

    /**
     * 采集指标原始编码
     */
    private String originalCoding;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 排序编码
     */
    private Integer sortRank;
    /**
     * 描述
     */
    private String description;
    /**
     * 单位转换因子
     */
    private Double unitConvFactor;
    /**
     * 指标命名空间
     */
    private String namespace;

    /**
     * 是否默认展示
     */
    private Boolean defaultDisplay;

    /**
     * uuid
     */
    private String uuid;

}
