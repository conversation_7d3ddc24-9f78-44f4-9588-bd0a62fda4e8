package com.cloudstar.rightcloud.monitor.client.opscollecttask.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * prometheus tls config
 *
 * @author: wanglang
 * @date: 2025/1/13 14:29
 */
@Data
public class MorPromTlsConfigParam implements Serializable {
    /**
     * 是否忽略https校验
     */
    @JSONField(name = "insecure_skip_verify", ordinal = 1)
    private Boolean insecureSkipVerify;
}
