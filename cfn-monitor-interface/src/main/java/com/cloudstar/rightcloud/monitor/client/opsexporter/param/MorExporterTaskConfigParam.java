package com.cloudstar.rightcloud.monitor.client.opsexporter.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 任务 redis缓存数据实体类, 需要和exporter里面的 TaskInfo 类结构一致
 * <AUTHOR>
 * @date 2024/5/31 10:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MorExporterTaskConfigParam implements Serializable {

    private Long id;

    //资源类型编码
    private List<String> resTypeCode;

}
