package com.cloudstar.rightcloud.monitor.client.opsexporter.param;

import com.cloudstar.rightcloud.common.annotation.BeanHelperField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 采集器修改参数
 *
 * @author: wanglang
 * @date: 2023/6/19 15:22
 */
@Data
public class OpsExporterUpdateParam implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 组件名称
     */
    private String name;
    /**
     * exporter名称
     */
    private String exporterName;
    /**
     * 采集频率（秒）
     */
    private String collectInterval;
    /**
     * 超时时间
     */
    private String timeout;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * 描述
     */
    private String description;
    /**
     * 采集器名称
     */
    private String collectorName;

    /**
     * 采集器id
     */
    private Long collectorId;

    /**
     * 云资源类型
     */
    private List<String> resTypeCodes;

    /**
     * 采集路径
     */
    private String collectorPath;

    /**
     * 类型
     */
    private String type;

    /**
     * exporter修改参数
     */
    @BeanHelperField(columnName = "exporterUpdateParamsForms", columnClass = OpsExporterUpdateParamsParam.class)
    private List<OpsExporterUpdateParamsParam> exporterUpdateParamsParams;


}
