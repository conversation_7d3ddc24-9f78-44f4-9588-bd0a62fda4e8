package com.cloudstar.rightcloud.monitor.client.opsviews.param;


import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import com.cloudstar.rightcloud.monitor.common.em.MorTimeStatistic;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警报表资源告警列表查询参数
 *
 * @author: wanglang
 * @date: 2023/8/21 15:24
 */
@Data
public class MorViewsResAlarmStatisticDataParam extends PageForm implements Serializable {

    /**
     * 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    private String time;

    /**
     * 告警来源
     */
    private String source;

    /**
     * 告警状态
     */
    private String status;

    /**
     * 所属云环境
     */
    private Long envId;

    /**
     * 对象类型
     */
    private String targetType;

    /**
     * 告警级别
     */
    private String level;

    /**
     * 所属组织
     */
    private Long orgId;

    /**
     * 处理状态
     */
    private String processingStatus;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 告警对象
     */
    private String target;

    /**
     * 告警持续时长
     */
    private String duration;


}
