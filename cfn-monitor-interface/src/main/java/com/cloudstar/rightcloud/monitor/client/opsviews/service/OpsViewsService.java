package com.cloudstar.rightcloud.monitor.client.opsviews.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.param.OpsCategoryInstanceGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryInstanceGetPropertiesPageResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryPropertiesGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsAlarmDataGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsAlarmRuleGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsCategoryInstanceGetDetailsParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsCategoryGetParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsViewsMetricDataGetParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsMonitoringViewsAlarmCountGetResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsMonitoringViewsAlarmDataGetPageResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsMonitoringViewsAlarmRuleGetPageResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsMetricDataGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsMetricGetListResult;

import java.util.List;
import java.util.Map;

/**
 * 视图接口
 *
 * @author: wanglang
 * @date: 2023/8/18 14:18
 */
public interface OpsViewsService {


    /**
     * 获取视图指标数据
     *
     * @param param 参数
     * @return OpsMonitoringViewsMetricGetResult视图指标数据
     */
    RightCloudResult<PageResult<OpsViewsMetricGetListResult>> getViewsMetricListResult(OpsMonitoringViewsCategoryGetParam param);


    /**
     * 获取告警统计数量
     *
     * @param param 参数
     * @return Map 告警统计数量
     */
    RightCloudResult<Map<String, String>> getAlarmCount(OpsMonitoringViewsCategoryGetParam param);


    /**
     * 获取告警统计数量
     *
     * @param categoryCode 分类编码
     * @return Map 告警统计数量
     */
    RightCloudResult<Map<String, Object>> getAlarmCount(String categoryCode);

    /**
     * 获取告警统计数量
     *
     * @param params 参数
     * @return Map告警统计数量
     */
    RightCloudResult<List<OpsMonitoringViewsAlarmCountGetResult>> getAlarmCount(List<OpsMonitoringViewsCategoryGetParam> params);

    /**
     * 查询分类扩展属性
     *
     * @param categoryCode 分类编码
     * @return OpsResTypePropertiesGetListResult 资源类型扩展属性
     */
    RightCloudResult<List<OpsCategoryPropertiesGetListResult>> getCategoryMonitorViewsPropertiesList(String categoryCode);


    /**
     * 查询监测视图实例数据
     *
     * @param param 条件参数
     * @return OpsResInstanceGetPageResult 资源实例数据
     */
    RightCloudResult<PageResult<OpsCategoryInstanceGetPropertiesPageResult>> getCategoryMonitorViewsInstancePageList(
            OpsCategoryInstanceGetPageParam param);

    /**
     * 查询监测视图实例数据
     *
     * @param param 条件参数
     * @return OpsResInstanceGetPageResult 资源实例数据
     */
    RightCloudResult<OpsCategoryInstanceGetPropertiesPageResult> getCategoryMonitorViewsInstanceDetails(
            OpsMonitoringViewsCategoryInstanceGetDetailsParam param);

    /**
     * 获取分类监测视图告警数据
     *
     * @param param 查询条件
     * @return
     */
    RightCloudResult<PageResult<OpsMonitoringViewsAlarmDataGetPageResult>> getCategoryMonitorViewsAlarmData(
            OpsMonitoringViewsAlarmDataGetPageParam param
    );


    /**
     * 获取监测视图告警规则数据
     *
     * @param param 查询条件
     * @return OpsMonitoringViewsAlarmRuleGetPageResult 告警规则数据
     */
    RightCloudResult<PageResult<OpsMonitoringViewsAlarmRuleGetPageResult>> getCategoryMonitorViewsAlarRule(
            OpsMonitoringViewsAlarmRuleGetPageParam param
    );


    /**
     * 获取分类监控视图指标数据
     *
     * @param param 查询条件
     * @return OpsViewsMetricDataGetListResult 视图指标数据
     */
    RightCloudResult<List<OpsViewsMetricDataGetListResult>> getCategoryMonitorViewsMetricData(OpsViewsMetricDataGetParam param);

}
