package com.cloudstar.rightcloud.monitor.service.moroptimization;

import cn.hutool.core.bean.BeanUtil;
import com.cloudstar.rightcloud.api.system.org.SysOrgClient;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil;
import com.cloudstar.rightcloud.monitor.client.moroptimization.param.OptimizationStrategyConfigUpdateParam;
import com.cloudstar.rightcloud.monitor.client.moroptimization.result.OptimizationStrategyConfigResult;
import com.cloudstar.rightcloud.monitor.client.moroptimization.service.OptimizationStrategyConfigService;
import com.cloudstar.rightcloud.monitor.common.constant.msg.OpsMonitorMsgConstant;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dao.OptimizationStrategyConfigDao;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationStrategyConfig;
import com.cloudstar.rightcloud.utils.AuthUserInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 优化策略配置
 *
 * <AUTHOR> Lesao
 * @date : 2023/11/29
 */
@Service
@Slf4j
public class OptimizationStrategyConfigServiceImpl implements OptimizationStrategyConfigService {


    @Resource
    private OptimizationStrategyConfigDao optimizationStrategyConfigDao;

    @Resource
    private SysOrgClient sysOrgClient;


    @Override
    public RightCloudResult<OptimizationStrategyConfigResult> getStrategyConfig() {
        // 获取当前项目根组织
        Long rootOrgId = sysOrgClient.getOrgRoot(AuthUserInfoUtil.getCurrentOrgId()).getData();
        if (rootOrgId == null) {
            BizAssertUtils.fail(OpsMonitorMsgConstant.MONITOR_ERROR_CURRENT_ORG_NULL);
        }
        // 如果当前用户的根组织id查询数据为空，则默认初始化数据
        OptimizationStrategyConfig config = optimizationStrategyConfigDao.getByOrgId(rootOrgId);
        OptimizationStrategyConfigResult configResult;
        if (config != null) {
            configResult = BeanUtil.copyProperties(config, OptimizationStrategyConfigResult.class);
        } else {
            configResult = initConfig(rootOrgId);
        }
        return RightCloudResult.success(configResult);
    }


    @Override
    public RightCloudResult<Long> update(OptimizationStrategyConfigUpdateParam updateParam) {
        OptimizationStrategyConfig config = BeanUtil.copyProperties(updateParam, OptimizationStrategyConfig.class);
        CrudHelpUtil.prepareUpdateParams(config);
        optimizationStrategyConfigDao.update(config);
        return RightCloudResult.success(config.getId());
    }


    /**
     * 初始化配置
     *
     * @param rootOrgId 根组织id
     * @return 配置
     */
    private OptimizationStrategyConfigResult initConfig(Long rootOrgId) {
        OptimizationStrategyConfig config = new OptimizationStrategyConfig();
        config.setOrgId(rootOrgId);
        config.setHisDays(10);
        // todo 枚举
        config.setDateRange("nature_day");
        config.setTimeRangeStart("00:00:00");
        config.setTimeRangeEnd("23:59:59");
        config.setShowPrice(true);
        config.setInquiryPriceType("spec");
        config.setChargeType("postPaid");
        config.setPayType("month");
        config.setInquiryDuration(1);
        config.setInquiryUnit("月");
        CrudHelpUtil.prepareInsertParams(config);
        optimizationStrategyConfigDao.insert(config);
        return BeanUtil.copyProperties(config, OptimizationStrategyConfigResult.class);
    }
}
