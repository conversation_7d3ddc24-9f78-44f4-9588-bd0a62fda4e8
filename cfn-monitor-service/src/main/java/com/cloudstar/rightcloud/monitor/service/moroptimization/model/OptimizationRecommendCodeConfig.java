package com.cloudstar.rightcloud.monitor.service.moroptimization.model;

import lombok.Data;

/**
 * 推荐配置code表中配置项
 * 对应OPTIMIZATION_RECOMMEND_前缀的code里面attr2属性
 *
 * <AUTHOR> Lesao
 */
@Data
public class OptimizationRecommendCodeConfig {

    /**
     * 指标
     */
    private String metric;
    /**
     * 指标值是否为使用率
     */
    private Boolean isUsage;

    /**
     * 推荐区间步长
     */
    private int step;

    /**
     * 根据规格询价的字段名
     */
    private String specPriceField;

    /**
     * 单位
     */
    private String unit;

}
