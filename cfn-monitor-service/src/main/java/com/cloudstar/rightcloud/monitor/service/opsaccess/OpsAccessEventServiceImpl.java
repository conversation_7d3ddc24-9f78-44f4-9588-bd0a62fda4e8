package com.cloudstar.rightcloud.monitor.service.opsaccess;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.opsaccess.service.OpsAccessEventService;
import com.cloudstar.rightcloud.monitor.common.event.AccessEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * promethue
 *
 * @author: wanglang
 * @date: 2023/8/14 11:00
 */

@AllArgsConstructor
@Service
@Slf4j
public class OpsAccessEventServiceImpl implements OpsAccessEventService {

    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    public RightCloudResult<Boolean> publishEvent(AccessEvent event) {
        Boolean result = false;
        if (Objects.nonNull(event)) {
            switch (event.getType()) {
                // 云环境
                case ENV:
                    log.info(
                            "Publisher CMDB Cloud Environment deletes push and updates the data associated with the cloud environment"
                    );
                    applicationEventPublisher.publishEvent(event);
                    result = true;
                    break;
                // 资源
                case RESOURCE:
                    log.info(
                            "Publisher The cmdb resource deletes the push and updates the data associated with the cloud environmente"
                    );
                    applicationEventPublisher.publishEvent(event);
                    result = true;
                    break;
                default:
                    log.error(
                            "The corresponding access event type could not be found,{}",
                            event
                    );
            }
        }
        return RightCloudResult.success(result);
    }
}
