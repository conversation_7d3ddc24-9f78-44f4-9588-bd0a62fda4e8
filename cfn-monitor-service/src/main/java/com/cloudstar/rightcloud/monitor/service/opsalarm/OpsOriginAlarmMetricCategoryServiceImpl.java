package com.cloudstar.rightcloud.monitor.service.opsalarm;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;
import com.cloudstar.rightcloud.log.common.utils.ActionLogUtil;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsOriginAlarmMetricCategoryCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsOriginAlarmMetricCategoryTreeParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsOriginAlarmMetricCategoryUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsOriginAlarmMetricCategoryDetailsResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsOriginAlarmMetricCategoryTreeListResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.service.OpsOriginAlarmMetricCategoryService;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.OpsMonitorMsgConstant;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsOriginAlarmMetricCategoryDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsOriginAlarmMetricDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableOriginAlarmMetricCategoryResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableOriginAlarmMetricResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsOriginAlarmMetricCategoryQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsOriginAlarmMetricQueryDto;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 原生告警指标接口
 *
 * @author: wanglang
 * @date: 2023/7/31 11:01
 */
@Service
@AllArgsConstructor
public class OpsOriginAlarmMetricCategoryServiceImpl implements OpsOriginAlarmMetricCategoryService {

    private final OpsOriginAlarmMetricCategoryDao opsOriginAlarmMetricCategoryDao;

    private final OpsOriginAlarmMetricDao opsOriginAlarmMetricDao;


    @Override
    public RightCloudResult<Long> createOriginAlarmMetricCategory(OpsOriginAlarmMetricCategoryCreateParam param) {
        BizAssertUtils.notNull(param, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY);
        // 校验名称是否重复
        final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDtoByNameResult =
                opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                        OpsOriginAlarmMetricCategoryQueryDto.builder()
                                .name(param.getName())
                                .build()
                );
        BizAssertUtils.isNull(opsTableOriginAlarmMetricCategoryResultDtoByNameResult,
                OpsMonitorMsgConstant.MONITOR_ERROR_VALIDATION_CONSTRAINT_NAME,
                MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY_NAME);
        // 校验编码是否重复
        final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDtoByCodeResult =
                opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                        OpsOriginAlarmMetricCategoryQueryDto.builder()
                                .code(param.getCode())
                                .build()
                );
        BizAssertUtils.isNull(opsTableOriginAlarmMetricCategoryResultDtoByCodeResult,
                OpsMonitorMsgConstant.MONITOR_ERROR_VALIDATION_CONSTRAINT_CODE,
                MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY_CODE);
        // 校验告警来源
        if (StringUtils.isEmpty(param.getParentCode())) {
            BizAssertUtils.notBlank(param.getSource(),
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                    MonitorFieldKeyConstant.ALARM_DATA_SOURCE);
        }
        if (StringUtils.isNotEmpty(param.getParentCode())) {
            final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDto =
                    opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                            OpsOriginAlarmMetricCategoryQueryDto.builder()
                                    .code(param.getParentCode())
                                    .build()
                    );
            BizAssertUtils.notNull(opsTableOriginAlarmMetricCategoryResultDto,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY_PARENT);
            param.setSource(opsTableOriginAlarmMetricCategoryResultDto.getSource());
        }
        final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDto =
                BeanHelperUtil.copyForBean(OpsTableOriginAlarmMetricCategoryResultDto::new, param);
        final Boolean result = opsOriginAlarmMetricCategoryDao.create(opsTableOriginAlarmMetricCategoryResultDto);
        BizAssertUtils.isTrue(result, CommonMsgConstant.COMMON_ERROR_HANDLE_FAIL);
        return RightCloudResult.success(opsTableOriginAlarmMetricCategoryResultDto.getId());
    }

    @Override
    public RightCloudResult<Boolean> updateOriginAlarmMetricCategory(OpsOriginAlarmMetricCategoryUpdateParam param) {
        BizAssertUtils.notNull(param, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY);
        // 校验名称是否重复
        if (StringUtils.isNotEmpty(param.getName())) {
            final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDtoByNameResult =
                    opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                            OpsOriginAlarmMetricCategoryQueryDto.builder()
                                    .name(param.getName())
                                    .notInId(List.of(param.getId()))
                                    .build()
                    );
            BizAssertUtils.isNull(opsTableOriginAlarmMetricCategoryResultDtoByNameResult,
                    OpsMonitorMsgConstant.MONITOR_ERROR_VALIDATION_CONSTRAINT_NAME,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY_NAME);
        }
        // 校验名称是否重复
        if (StringUtils.isNotEmpty(param.getCode())) {
            // 校验编码是否重复
            final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDtoByCodeResult =
                    opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                            OpsOriginAlarmMetricCategoryQueryDto.builder()
                                    .code(param.getCode())
                                    .notInId(List.of(param.getId()))
                                    .build()
                    );
            BizAssertUtils.isNull(opsTableOriginAlarmMetricCategoryResultDtoByCodeResult,
                    OpsMonitorMsgConstant.MONITOR_ERROR_VALIDATION_CONSTRAINT_CODE,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY_CODE);
        }
        // 校验告警来源
        if (StringUtils.isEmpty(param.getParentCode())) {
            BizAssertUtils.notBlank(param.getSource(),
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                    MonitorFieldKeyConstant.ALARM_DATA_SOURCE);
        }
        if (StringUtils.isNotEmpty(param.getParentCode())) {
            final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDto =
                    opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                            OpsOriginAlarmMetricCategoryQueryDto.builder()
                                    .code(param.getParentCode())
                                    .build()
                    );
            BizAssertUtils.notNull(opsTableOriginAlarmMetricCategoryResultDto,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY_PARENT);
            param.setSource(opsTableOriginAlarmMetricCategoryResultDto.getSource());
        }
        final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDto =
                BeanHelperUtil.copyForBean(OpsTableOriginAlarmMetricCategoryResultDto::new, param);
        final Boolean result = opsOriginAlarmMetricCategoryDao.update(opsTableOriginAlarmMetricCategoryResultDto);
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<Boolean> deleteOriginAlarmMetricCategory(Long id) {
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ID);
        // 查询是否有子级
        final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDto =
                opsOriginAlarmMetricCategoryDao.selectOneNoDataFilter(
                        OpsOriginAlarmMetricCategoryQueryDto.builder()
                                .id(id)
                                .build()
                );

        if (opsTableOriginAlarmMetricCategoryResultDto == null) {
            throw new BizException(MessageUtil.getMessage(CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER));
        }

        ActionLogUtil.logParam(opsTableOriginAlarmMetricCategoryResultDto.getName(), null);

        if (Objects.nonNull(opsTableOriginAlarmMetricCategoryResultDto)) {
            final List<OpsTableOriginAlarmMetricCategoryResultDto> opsTableOriginAlarmMetricCategories =
                    opsOriginAlarmMetricCategoryDao.selectListNoDataFilter(
                            OpsOriginAlarmMetricCategoryQueryDto.builder()
                                    .parentCode(opsTableOriginAlarmMetricCategoryResultDto.getCode())
                                    .build()
                    );
            BizAssertUtils.isEmpty(opsTableOriginAlarmMetricCategories,
                    OpsMonitorMsgConstant.MONITOR_ERROR_VALIDATION_CHILDREN,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY);
            final List<OpsTableOriginAlarmMetricResultDto> opsTableOriginAlarmMetricResultDtoList =
                    opsOriginAlarmMetricDao.selectListNoDataFilter(
                            OpsOriginAlarmMetricQueryDto.builder()
                                    .productComponent(opsTableOriginAlarmMetricCategoryResultDto.getCode())
                                    .build()
                    );
            BizAssertUtils.isEmpty(opsTableOriginAlarmMetricResultDtoList,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETE,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_CATEGORY,
                    MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC);
        }

        final Boolean result = opsOriginAlarmMetricCategoryDao.delete(
                Wrappers.<OpsTableOriginAlarmMetricCategoryResultDto>lambdaQuery()
                        .eq(OpsTableOriginAlarmMetricCategoryResultDto::getId, id)
        );
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<OpsOriginAlarmMetricCategoryDetailsResult> getOriginAlarmMetricCategoryDetails(Long id) {
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY, MonitorFieldKeyConstant.ID);
        final OpsTableOriginAlarmMetricCategoryResultDto opsTableOriginAlarmMetricCategoryResultDto = opsOriginAlarmMetricCategoryDao.selectOne(
                OpsOriginAlarmMetricCategoryQueryDto.builder()
                        .id(id)
                        .build()
        );
        BizAssertUtils.notNull(opsTableOriginAlarmMetricCategoryResultDto,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT, id);
        final OpsOriginAlarmMetricCategoryDetailsResult opsOriginAlarmMetricCategoryDetailsResult =
                BeanHelperUtil.copyForBean(OpsOriginAlarmMetricCategoryDetailsResult::new,
                        opsTableOriginAlarmMetricCategoryResultDto);
        return RightCloudResult.success(opsOriginAlarmMetricCategoryDetailsResult);
    }

    @Override
    public RightCloudResult<List<OpsOriginAlarmMetricCategoryTreeListResult>> getOriginAlarmMetricCategoryList(OpsOriginAlarmMetricCategoryTreeParam
                                                                                                                       param) {
        // 查询条件
        final OpsOriginAlarmMetricCategoryQueryDto query = OpsOriginAlarmMetricCategoryQueryDto.builder().build();
        // 是否父级
        if (Objects.nonNull(param.getParent()) && param.getParent()) {
            query.setParentCodeByNull(true);
        }
        // 告警来源
        if (StringUtils.isNotEmpty(param.getAlarmSource())) {
            query.setSource(param.getAlarmSource());
        }

        final List<OpsTableOriginAlarmMetricCategoryResultDto> opsTableOriginAlarmMetricCategories =
                opsOriginAlarmMetricCategoryDao.selectList(query);
        List<OpsOriginAlarmMetricCategoryTreeListResult> opsOriginAlarmMetricCategoryTreeListTreeResults = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(opsTableOriginAlarmMetricCategories)) {
            final List<OpsOriginAlarmMetricCategoryTreeListResult> opsOriginAlarmMetricCategoryTreeListResults =
                    BeanHelperUtil.copyForList(OpsOriginAlarmMetricCategoryTreeListResult::new, opsTableOriginAlarmMetricCategories);
            opsOriginAlarmMetricCategoryTreeListTreeResults =
                    this.buildTree(opsOriginAlarmMetricCategoryTreeListResults);
        }
        return RightCloudResult.success(opsOriginAlarmMetricCategoryTreeListTreeResults);
    }

    /**
     * 构建树
     *
     * @param list 数据集合
     * @return 树菜单
     */
    private List<OpsOriginAlarmMetricCategoryTreeListResult> buildTree(List<OpsOriginAlarmMetricCategoryTreeListResult> list) {
        List<OpsOriginAlarmMetricCategoryTreeListResult> result = new ArrayList<>();
        Map<String, OpsOriginAlarmMetricCategoryTreeListResult> map =
                list.stream().collect(Collectors.toMap(OpsOriginAlarmMetricCategoryTreeListResult::getCode, x -> x));
        for (OpsOriginAlarmMetricCategoryTreeListResult t : list) {
            OpsOriginAlarmMetricCategoryTreeListResult category = map.get(t.getCode());
            String parentId = category.getParentCode();
            if (StringUtils.isEmpty(parentId)) {
                result.add(category);
            } else {
                OpsOriginAlarmMetricCategoryTreeListResult parentCategory = map.get(parentId);
                if (Objects.nonNull(parentCategory)) {
                    if (CollectionUtil.isEmpty(parentCategory.getChildren())) {
                        parentCategory.setChildren(new ArrayList<>());
                    }
                    parentCategory.getChildren().add(category);
                    parentCategory.setChildren(
                            parentCategory.getChildren().stream()
                                    .sorted(Comparator.comparing(OpsOriginAlarmMetricCategoryTreeListResult::getSortRank))
                                    .collect(Collectors.toList())
                    );
                } else {
                    result.add(category);
                }
            }
        }
        return result.stream()
                .sorted(Comparator.comparing(OpsOriginAlarmMetricCategoryTreeListResult::getSortRank))
                .collect(Collectors.toList());
    }
}
