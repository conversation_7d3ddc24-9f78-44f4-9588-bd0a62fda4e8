package com.cloudstar.rightcloud.monitor.service.opscmdb;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.cloudstar.rightcloud.api.cmdb.category.CmdbResCategoryV2Client;
import com.cloudstar.rightcloud.api.cmdb.category.form.ResCategoryFeignForm;
import com.cloudstar.rightcloud.api.cmdb.category.form.ResCategoryTreeQueryForm;
import com.cloudstar.rightcloud.api.cmdb.category.result.CmdbResCategoryGeDetailsFeignV2Result;
import com.cloudstar.rightcloud.api.cmdb.category.result.CmdbResCategoryGetListFeignV2Result;
import com.cloudstar.rightcloud.api.cmdb.category.result.CmdbResCategoryPropertiesGetV2Result;
import com.cloudstar.rightcloud.api.cmdb.category.result.CmdbResCategoryTagFeignV2Result;
import com.cloudstar.rightcloud.api.cmdb.category.result.AbstractTreeV2Result;
import com.cloudstar.rightcloud.api.cmdb.categoryralation.CmdbResCategoryRelationV2Client;
import com.cloudstar.rightcloud.api.cmdb.categoryralation.form.ResRelationV2Form;
import com.cloudstar.rightcloud.api.cmdb.categoryralation.result.ResRelDefiV2Result;
import com.cloudstar.rightcloud.api.cmdb.resinst.CmdbResInstClient;
import com.cloudstar.rightcloud.api.cmdb.resinst.CmdbResInstV1Client;
import com.cloudstar.rightcloud.api.cmdb.resinst.form.CmdbResInstPageListForm;
import com.cloudstar.rightcloud.api.cmdb.resinst.form.ResInstPageV1FeignForm;
import com.cloudstar.rightcloud.api.cmdb.resinst.result.CmdbResInstCountFeignResult;
import com.cloudstar.rightcloud.api.cmdb.resinst.result.CmdbResInstDetailsFeignResult;
import com.cloudstar.rightcloud.api.cmdb.resinst.result.CmdbResInstPropValFeignResult;
import com.cloudstar.rightcloud.api.cmdb.resinst.util.CmdbResInstClientUtil;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.monitor.client.opscmdb.param.OpsCategoryInstanceGetListParam;
import com.cloudstar.rightcloud.monitor.client.opscmdb.param.OpsCategoryInstanceGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryDataGetEnvResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryDataResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryInstanceGetCountResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryInstanceGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryInstanceGetPageResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryInstanceGetPropertiesPageResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryPropertiesGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCloudEnvGetInstanceResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsEnvGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsResInstancePropertiesResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.service.OpsCmdbService;
import com.cloudstar.rightcloud.monitor.common.constant.OpsMonitorConstant;
import com.cloudstar.rightcloud.monitor.common.constant.StringPool;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.em.CmdbCategoryPropertiesType;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.mybatisplus.dto.MorResInstListDto;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cloudstar.rightcloud.monitor.common.constant.OpsResourceTypeConstant.ORG_ID;
import static com.cloudstar.rightcloud.monitor.common.constant.OpsResourceTypeConstant.UU_ID;

/**
 * 资源类型接口
 *
 * @author: wanglang
 * @date: 2023/7/26 17:47
 */
@Service
@AllArgsConstructor
@Slf4j
public class OpsCmdbServiceImpl implements OpsCmdbService {

    private final CmdbResCategoryV2Client cmdbResCategoryClient;

    private final CmdbResCategoryRelationV2Client cmdbResCategoryRelationV2Client;

    private final CmdbResInstV1Client cmdbResInstV1Client;

    private final CmdbResInstClient cmdbResInstClient;

    /**
     * 监控分类标签
     */
    private final String categoryCode = "MONITOR_CATEGORY_CODE";

    /**
     * 监控树级分类标签
     */
    private final String categoryTreeCode = "MONITOR_TREE_CATEGORY_CODE";

    private final String categoryType = "category";
    private final String moldType = "mold";


    @Override
    public RightCloudResult<List<OpsCategoryDataResult>> getCategoryTypeDataResult() {
        // 获取资源分类
        // 获取资源类别
        ResCategoryTreeQueryForm resCategoryTreeQueryForm = new ResCategoryTreeQueryForm();
        resCategoryTreeQueryForm.setTagCodes(List.of(categoryTreeCode));
        // 查询资源分类
        final RightCloudResult<List<CmdbResCategoryTagFeignV2Result>> resCategoryDataResult = cmdbResCategoryClient.data(resCategoryTreeQueryForm);
        List<OpsCategoryDataResult> resultList = new ArrayList<>();
        if (Objects.nonNull(resCategoryDataResult) && CollectionUtil.isNotEmpty(resCategoryDataResult.getData())) {
            List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResult = new ArrayList<>();
            final List<List<CmdbResCategoryTagFeignV2Result>> categoryDataListResult =
                    this.formListCmdbResType(resCategoryDataResult.getData(), new ArrayList<>());
            if (CollectionUtil.isNotEmpty(categoryDataListResult)) {
                for (List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResults : categoryDataListResult) {
                    cmdbResCategoryDataQueryFeignResult.addAll(cmdbResCategoryDataQueryFeignResults);
                }
            }
            final List<OpsCategoryDataResult> opsCategoryDataResults = cmdbResCategoryDataQueryFeignResult.stream()
                    .distinct()
                    .map(cmdbResCategoryDataQueryFeign -> {
                        final OpsCategoryDataResult opsCategoryDataResult =
                                BeanHelperUtil.copyForBean(OpsCategoryDataResult::new, cmdbResCategoryDataQueryFeign);
                        opsCategoryDataResult.setType(categoryType);
                        return opsCategoryDataResult;
                    }).collect(Collectors.toList());
            resultList.addAll(opsCategoryDataResults);
        }
        // 查询资源类型数据
        resCategoryTreeQueryForm.setTagCodes(List.of(categoryCode));
        final RightCloudResult<List<CmdbResCategoryTagFeignV2Result>> resTypeCategoryDataResult =
                cmdbResCategoryClient.data(resCategoryTreeQueryForm);
        if (Objects.nonNull(resTypeCategoryDataResult) && CollectionUtil.isNotEmpty(resTypeCategoryDataResult.getData())) {
            List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResult = new ArrayList<>();
            final List<List<CmdbResCategoryTagFeignV2Result>> categoryDataListResult =
                    this.formListCmdbResType(resTypeCategoryDataResult.getData(), new ArrayList<>());
            if (CollectionUtil.isNotEmpty(categoryDataListResult)) {
                for (List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResults :
                        categoryDataListResult) {
                    cmdbResCategoryDataQueryFeignResult.addAll(cmdbResCategoryDataQueryFeignResults);
                }
            }
            final List<OpsCategoryDataResult> opsCategoryDataResults = cmdbResCategoryDataQueryFeignResult.stream()
                    .distinct()
                    .map(cmdbResCategoryDataQueryFeign -> {
                        final OpsCategoryDataResult opsCategoryDataResult =
                                BeanHelperUtil.copyForBean(OpsCategoryDataResult::new, cmdbResCategoryDataQueryFeign);
                        opsCategoryDataResult.setType(moldType);
                        return opsCategoryDataResult;
                    }).collect(Collectors.toList());
            opsCategoryDataResults.stream()
                    .filter(Objects::nonNull);
            resultList.addAll(opsCategoryDataResults);
        }
        final List<OpsCategoryDataResult> collectRuleResTypeDataResults = this.buildTree(resultList);
        return RightCloudResult.success(collectRuleResTypeDataResults.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public List<OpsCategoryDataResult> getCategoryTypeDataResult(String tagCode) {
        // 获取资源类别
        ResCategoryTreeQueryForm resCategoryTreeQueryForm = new ResCategoryTreeQueryForm();
        resCategoryTreeQueryForm.setTagCodes(List.of(tagCode));
        // 查询资源分类
        final RightCloudResult<List<CmdbResCategoryTagFeignV2Result>> resCategoryDataResult = cmdbResCategoryClient.data(resCategoryTreeQueryForm);
        List<OpsCategoryDataResult> resultList = new ArrayList<>();
        if (Objects.nonNull(resCategoryDataResult) && CollectionUtil.isNotEmpty(resCategoryDataResult.getData())) {
            List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResult = new ArrayList<>();
            final List<List<CmdbResCategoryTagFeignV2Result>> categoryDataListResult =
                    this.formListCmdbResType(resCategoryDataResult.getData(), new ArrayList<>());
            if (CollectionUtil.isNotEmpty(categoryDataListResult)) {
                for (List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResults : categoryDataListResult) {
                    cmdbResCategoryDataQueryFeignResult.addAll(cmdbResCategoryDataQueryFeignResults);
                }
            }
            final List<OpsCategoryDataResult> opsCategoryDataResults = cmdbResCategoryDataQueryFeignResult.stream()
                    .distinct()
                    .map(cmdbResCategoryDataQueryFeign -> {
                        final OpsCategoryDataResult opsCategoryDataResult =
                                BeanHelperUtil.copyForBean(OpsCategoryDataResult::new, cmdbResCategoryDataQueryFeign);
                        opsCategoryDataResult.setType(categoryType);
                        return opsCategoryDataResult;
                    }).collect(Collectors.toList());
            resultList.addAll(opsCategoryDataResults);
        }
        return resultList;
    }

    @Override
    public RightCloudResult<List<OpsCategoryDataResult>> getCategoryTypeDataListResult() {
        // 查询资源类型数据
        List<OpsCategoryDataResult> resultList = new ArrayList<>();
        ResCategoryTreeQueryForm resCategoryTreeQueryForm = new ResCategoryTreeQueryForm();
        resCategoryTreeQueryForm.setTagCodes(List.of(categoryCode));
        final RightCloudResult<List<CmdbResCategoryTagFeignV2Result>> resTypeCategoryDataResult =
                cmdbResCategoryClient.data(resCategoryTreeQueryForm);
        if (Objects.nonNull(resTypeCategoryDataResult) && CollectionUtil.isNotEmpty(resTypeCategoryDataResult.getData())) {
            List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResult = new ArrayList<>();
            final List<List<CmdbResCategoryTagFeignV2Result>> categoryDataListResult =
                    this.formListCmdbResType(resTypeCategoryDataResult.getData(), new ArrayList<>());
            if (CollectionUtil.isNotEmpty(categoryDataListResult)) {
                for (List<CmdbResCategoryTagFeignV2Result> cmdbResCategoryDataQueryFeignResults :
                        categoryDataListResult) {
                    cmdbResCategoryDataQueryFeignResult.addAll(cmdbResCategoryDataQueryFeignResults);
                }
            }
            final List<OpsCategoryDataResult> opsCategoryDataResults = cmdbResCategoryDataQueryFeignResult.stream()
                    .distinct()
                    .map(cmdbResCategoryDataQueryFeign -> {
                        final OpsCategoryDataResult opsCategoryDataResult =
                                BeanHelperUtil.copyForBean(OpsCategoryDataResult::new, cmdbResCategoryDataQueryFeign);
                        opsCategoryDataResult.setType(moldType);
                        return opsCategoryDataResult;
                    }).collect(Collectors.toList());
            opsCategoryDataResults.stream()
                    .filter(Objects::nonNull);
            resultList.addAll(opsCategoryDataResults);
        }
        return RightCloudResult.success(resultList);

    }

    @Override
    public RightCloudResult<PageResult<OpsCategoryInstanceGetPropertiesPageResult>> getCategoryInstancePropertiesPageList(
            OpsCategoryInstanceGetPageParam param) {
        // 查询资源实例数据
        final OpsCategoryInstanceGetPageParam opsCategoryInstanceGetPageParam = BeanHelperUtil.copyForBean(
                OpsCategoryInstanceGetPageParam::new,
                param
        );
        // 返回结果
        PageResult<OpsCategoryInstanceGetPropertiesPageResult> result = new PageResult<>();
        result.setPageNo(param.getPageNo());
        result.setTotal(0L);
        result.setPageSize(param.getPageSize());
        result.setTotalPages(0);
        final RightCloudResult<PageResult<OpsCategoryInstanceGetPageResult>> categoryInstanceGetPageResult =
                this.getCategoryInstanceGetPageResult(opsCategoryInstanceGetPageParam);
        if (Objects.nonNull(categoryInstanceGetPageResult) && Objects.nonNull(categoryInstanceGetPageResult.getData())) {
            final PageResult<OpsCategoryInstanceGetPageResult> data = categoryInstanceGetPageResult.getData();
            result.setPageNo(data.getPageNo());
            result.setTotal(data.getTotal());
            result.setPageSize(data.getPageSize());
            result.setTotalPages(data.getTotalPages());
            List<OpsCategoryInstanceGetPropertiesPageResult> opsCategoryInstanceGetPropertiesPageResults = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(data.getList())) {
                opsCategoryInstanceGetPropertiesPageResults = data.getList().stream()
                        .filter(Objects::nonNull)
                        .map(opsResInstanceGetPageResult -> {
                            Map<String, Object> opsResInstancePropertiesResultMap = new HashMap<>();
                            final OpsCategoryInstanceGetPropertiesPageResult opsResInstanceGetPage = BeanHelperUtil.copyForBean(
                                    OpsCategoryInstanceGetPropertiesPageResult::new,
                                    opsResInstanceGetPageResult
                            );
                            final List<CmdbResInstPropValFeignResult> resInstPropValList = opsResInstanceGetPageResult.getResInstPropValList();
                            if (CollectionUtil.isNotEmpty(resInstPropValList)) {
                                for (CmdbResInstPropValFeignResult cmdbResInstPropValFeignResult : resInstPropValList) {
                                    opsResInstancePropertiesResultMap.put(cmdbResInstPropValFeignResult.getPropCode(),
                                            cmdbResInstPropValFeignResult.getPropValue());
                                }

                            }
                            if (Objects.nonNull(opsResInstanceGetPage)) {
                                opsResInstanceGetPage.setOpsResInstancePropertiesResults(opsResInstancePropertiesResultMap);
                            }
                            return opsResInstanceGetPage;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                result.setPageNo(param.getPageNo());
                result.setTotal(0L);
                result.setPageSize(param.getPageSize());
                result.setTotalPages(0);
            }
            result.setList(opsCategoryInstanceGetPropertiesPageResults);
        }
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<OpsCategoryInstanceGetPropertiesPageResult> getCategoryInstancePropertiesDetails(String instanceId) {
        BizAssertUtils.notBlank(instanceId, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.ID);
        OpsCategoryInstanceGetPropertiesPageResult opsCategoryInstanceGetPropertiesPageResult =
                new OpsCategoryInstanceGetPropertiesPageResult();
        ResInstPageV1FeignForm pageV1FeignForm = new ResInstPageV1FeignForm();
        pageV1FeignForm.setInstanceIds(List.of(instanceId));
        final CmdbResInstDetailsFeignResult detailData = this.getResInstByPropertiesDetail(pageV1FeignForm);
        if (Objects.nonNull(detailData)) {
            opsCategoryInstanceGetPropertiesPageResult = BeanHelperUtil.copyForBean(
                    OpsCategoryInstanceGetPropertiesPageResult::new,
                    detailData
            );
            Map<String, Object> opsResInstancePropertiesResultMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(detailData.getProperties())) {
                opsResInstancePropertiesResultMap = detailData.getProperties();
            }
            opsCategoryInstanceGetPropertiesPageResult.setOpsResInstancePropertiesResults(opsResInstancePropertiesResultMap);
        }
        return RightCloudResult.success(opsCategoryInstanceGetPropertiesPageResult);
    }

    @Override
    public RightCloudResult<List<OpsCategoryDataGetEnvResult>> getCategoryDataResult(String envCode, String envVersion) {
        List<OpsCategoryDataGetEnvResult> opsCategoryDataGetEnvResults = new ArrayList<>();
        // 获取资源类别
        ResRelationV2Form resRelationV2Form = new ResRelationV2Form();
        String code = envCode + StringPool.HASH + envVersion;
        resRelationV2Form.setSrcCategoryCode(code);
        final RightCloudResult<List<ResRelDefiV2Result>> categoryRelation = cmdbResCategoryRelationV2Client.getCategoryRelation(resRelationV2Form);
        if (Objects.nonNull(categoryRelation) && CollectionUtil.isNotEmpty(categoryRelation.getData())) {
            final List<ResRelDefiV2Result> categoryRelationData = categoryRelation.getData();
            opsCategoryDataGetEnvResults = BeanHelperUtil.copyForList(OpsCategoryDataGetEnvResult::new, categoryRelationData);
        }
        return RightCloudResult.success(opsCategoryDataGetEnvResults.stream().distinct().collect(Collectors.toList()));
    }


    /**
     * 将tree结构转换为list路径结构
     *
     * @param list    原始数据
     * @param results 返回数据
     * @return
     */
    @Override
    public List<List<OpsCategoryDataResult>> formList(List<OpsCategoryDataResult> list,
                                                      List<OpsCategoryDataResult> results) {
        List<List<OpsCategoryDataResult>> listList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (OpsCategoryDataResult cmdbResCategoryDataQueryFeignResult : list) {
                List<OpsCategoryDataResult> treePath = new ArrayList<>();
                treePath.addAll(results);
                OpsCategoryDataResult treeVo = new OpsCategoryDataResult();
                //将tree复制给treeVo
                BeanUtil.copyProperties(cmdbResCategoryDataQueryFeignResult, treeVo);
                treeVo.setChildren(null);
                //将child为零的对象放入路径中
                treePath.add(treeVo);
                //检查当前节点对象的child是否为空
                if (CollectionUtil.isEmpty(cmdbResCategoryDataQueryFeignResult.getChildren())) {
                    listList.add(treePath);
                } else {
                    List<List<OpsCategoryDataResult>> resultList =
                            formList(cmdbResCategoryDataQueryFeignResult.getChildren(), treePath);
                    //迭代结束
                    listList.addAll(resultList);
                }
            }
        }
        return listList;
    }

    @Override
    public RightCloudResult<List<OpsCategoryGetListResult>> getCategoryDataList(List<String> resTypeCodeList) {
        ResCategoryFeignForm resCategoryFeignForm = new ResCategoryFeignForm();
        resCategoryFeignForm.setCodes(String.join(",", resTypeCodeList));
        final RightCloudResult<List<CmdbResCategoryGetListFeignV2Result>> categoryDataList =
                cmdbResCategoryClient.getResCategory(resCategoryFeignForm);
        List<OpsCategoryGetListResult> categoryGetListResultList = new ArrayList<>();
        if (Objects.nonNull(categoryDataList) && CollectionUtil.isNotEmpty(categoryDataList.getData())) {
            final List<CmdbResCategoryGetListFeignV2Result> categoryDataListData = categoryDataList.getData();
            categoryGetListResultList = categoryDataListData.stream()
                    .filter(Objects::nonNull)
                    .map(categoryData -> {
                        OpsCategoryGetListResult categoryGetListResult =
                                new OpsCategoryGetListResult();
                        categoryGetListResult.setCode(categoryData.getCategoryCode());
                        Locale locale = LocaleContextHolder.getLocale();
                        if ("en".equals(locale.getLanguage())) {
                            categoryGetListResult.setName(categoryData.getCategoryNameEn());
                        } else {
                            categoryGetListResult.setName(categoryData.getCategoryName());
                        }
                        return categoryGetListResult;
                    }).collect(Collectors.toList());
        }
        return RightCloudResult.success(categoryGetListResultList.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public RightCloudResult<List<OpsCategoryInstanceGetListResult>> getCategoryInstanceList(ResInstPageV1FeignForm param) {
        // 查询资源实例数据
        List<OpsCategoryInstanceGetListResult> opsResInstanceGetPageResults = new ArrayList<>();
        final List<MorResInstListDto> resultRightCloudResultData = this.buildResInstListFeignResult(param);
        if (CollectionUtil.isNotEmpty(resultRightCloudResultData)) {
            if (CollectionUtil.isNotEmpty(resultRightCloudResultData)) {
                opsResInstanceGetPageResults = BeanHelperUtil.copyForList(
                        OpsCategoryInstanceGetListResult::new,
                        resultRightCloudResultData
                );
                final Map<String, MorResInstListDto> resInstListFeignResultMap = resultRightCloudResultData.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(
                                MorResInstListDto::getInstanceId,
                                resInstListFeignResult -> resInstListFeignResult));
                if (CollectionUtil.isNotEmpty(resInstListFeignResultMap)) {
                    for (OpsCategoryInstanceGetListResult opsResInstanceGetPageResult : opsResInstanceGetPageResults) {
                        final MorResInstListDto resInstListFeignResult =
                                resInstListFeignResultMap.get(opsResInstanceGetPageResult.getInstanceId());
                        final Map<String, Object> properties = resInstListFeignResult.getProperties();
                        final Set<String> propCodes = properties.keySet();
                        List<OpsResInstancePropertiesResult> opsResInstancePropertiesResults = new ArrayList<>();
                        for (String propCode : propCodes) {
                            OpsResInstancePropertiesResult opsResInstancePropertiesResult = new OpsResInstancePropertiesResult();
                            opsResInstancePropertiesResult.setPropCode(propCode);
                            final Object value = properties.get(propCode);
                            if (Objects.nonNull(value)) {
                                opsResInstancePropertiesResult.setPropValue(value.toString());
                            }
                            opsResInstancePropertiesResults.add(opsResInstancePropertiesResult);
                        }
                        opsResInstanceGetPageResult.setOpsResInstancePropertiesResults(opsResInstancePropertiesResults);
                    }
                }
            }

        }
        return RightCloudResult.success(opsResInstanceGetPageResults);
    }

    @SneakyThrows
    private List<MorResInstListDto> buildResInstListFeignResult(ResInstPageV1FeignForm param) {
        final String idName = "id";
        final String instanceIdName = "instanceId";
        final String codeName = "code";
        final String instanceNameName = "instanceName";
        final String categoryCodeName = "categoryCode";
        final String categoryNameName = "categoryName";
        final String cloudEnvIdName = "cloudEnvId";
        List<String> returnProperties = new ArrayList<>();
        List<MorResInstListDto> resInstPageFeignResults = new ArrayList<>();
        // 查找资源的组织id
        if (Objects.nonNull(param)) {
            CmdbResInstPageListForm listForm = BeanUtil.copyProperties(param, CmdbResInstPageListForm.class);
            if (Objects.nonNull(listForm) && CollectionUtil.isNotEmpty(listForm.getReturnProperties())) {
                returnProperties.add(instanceIdName);
                returnProperties.add(idName);
                returnProperties.add(codeName);
                returnProperties.add(instanceNameName);
                returnProperties.add(categoryCodeName);
                returnProperties.add(categoryNameName);
                returnProperties.add(cloudEnvIdName);
                returnProperties.addAll(listForm.getReturnProperties());
                returnProperties = returnProperties.stream().distinct().collect(Collectors.toList());
                listForm.setReturnProperties(returnProperties);
            }
            List<Map<String, Object>> data = CmdbResInstClientUtil.getResInstByProperties(listForm).getData();
            resInstPageFeignResults = data.stream().distinct()
                    .filter(Objects::nonNull)
                    .map(OpsCmdbServiceImpl::build)
                    .filter(Objects::nonNull)
                    .filter(a -> StrUtil.isNotBlank(a.getInstanceId()))
                    .distinct()
                    .collect(Collectors.toList());
        }
        return resInstPageFeignResults;
    }

    private static MorResInstListDto build(Map<String, Object> propertiesMap) {
        final String idName = "id";
        final String instanceIdName = "id";
        final String codeName = "code";
        final String instanceNameName = "instanceName";
        MorResInstListDto resInstListFeignResult = null;
        if (CollectionUtil.isNotEmpty(propertiesMap)) {
            resInstListFeignResult = BeanUtil.copyProperties(propertiesMap, MorResInstListDto.class);
            final Object instanceId = propertiesMap.get(instanceIdName);
            if (Objects.nonNull(instanceId)) {
                resInstListFeignResult.setInstanceId(instanceId.toString());
            }
            final Object code = propertiesMap.get(codeName);
            if (Objects.nonNull(code)) {
                resInstListFeignResult.setCode(code.toString());
            }
            final Object instanceName = propertiesMap.get(instanceNameName);
            if (Objects.nonNull(instanceName)) {
                resInstListFeignResult.setInstanceName(instanceName.toString());
            } else {
                resInstListFeignResult.setInstanceName(MapUtil.getStr(propertiesMap, "name"));
            }
            resInstListFeignResult.setCategoryCode(MapUtil.getStr(propertiesMap, "categoryCode"));
            resInstListFeignResult.setCategoryName(MapUtil.getStr(propertiesMap, "categoryName"));
            resInstListFeignResult.setProperties(propertiesMap);
        }
        return resInstListFeignResult;

    }

    /**
     * 获取总页数
     *
     * @param pageV1FeignForm 资源实例数据
     * @param pageSize        分页条数
     * @return totalPages 总页数
     */
    private Integer getResTotalPages(ResInstPageV1FeignForm pageV1FeignForm, Integer pageSize) {
        Integer totalPages = 0;
        // 获取总页数
        // 查找资源的组织id
        if (Objects.nonNull(pageV1FeignForm)) {
            pageV1FeignForm.setPageSize(pageSize);
            final RightCloudResult<PageResult<Map<String, Object>>> pageResultRightCloudResult = cmdbResInstV1Client.postPage(pageV1FeignForm);
            if (Objects.nonNull(pageResultRightCloudResult) && Objects.nonNull(pageResultRightCloudResult.getData())) {
                totalPages = pageResultRightCloudResult.getData().getTotalPages();
            }
        }
        return totalPages;
    }

    @Override
    public RightCloudResult<List<OpsCategoryPropertiesGetListResult>> getResTypePropertiesList(String categoryCode) {
        List<OpsCategoryPropertiesGetListResult> opsCategoryPropertiesGetListResults = new ArrayList<>();
        final RightCloudResult<CmdbResCategoryPropertiesGetV2Result> categoryProperty = cmdbResCategoryClient.getCategoryProperty(categoryCode);
        if (Objects.nonNull(categoryProperty) && Objects.nonNull(categoryProperty.getData())) {
            final CmdbResCategoryPropertiesGetV2Result cmdbResCategoryPropertiesGetV2Result = categoryProperty.getData();
            if (CollectionUtil.isNotEmpty(cmdbResCategoryPropertiesGetV2Result.getProperties())) {
                opsCategoryPropertiesGetListResults = cmdbResCategoryPropertiesGetV2Result.getProperties()
                        .stream()
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(cmdbResPropertyVOResult -> {
                            int sort = 1;
                            if (Objects.nonNull(cmdbResPropertyVOResult.getSortRank())) {
                                sort = cmdbResPropertyVOResult.getSortRank();
                            }
                            return sort;
                        }))
                        .map(cmdbResPropertyVOResult -> {
                            final OpsCategoryPropertiesGetListResult opsCategoryPropertiesGetListResult = BeanHelperUtil.copyForBean(
                                    OpsCategoryPropertiesGetListResult::new,
                                    cmdbResPropertyVOResult
                            );
                            List<String> propTypeList = new ArrayList<>();
                            if (StringUtils.isNotEmpty(cmdbResPropertyVOResult.getPropType())) {

                                final String[] propTypeArray = cmdbResPropertyVOResult.getPropType().split(StringPool.COMMA);
                                if (ArrayUtil.isNotEmpty(propTypeArray)) {
                                    for (String propType : propTypeArray) {
                                        if (!StringUtils.equals(StringPool.COMMA, propType)) {
                                            propTypeList.add(propType);
                                        }
                                    }
                                }
                            }
                            opsCategoryPropertiesGetListResult.setPropType(propTypeList);
                            return opsCategoryPropertiesGetListResult;
                        })
                        .collect(Collectors.toList());
            }
        }
        return RightCloudResult.success(opsCategoryPropertiesGetListResults);
    }

    @Override
    public RightCloudResult<Map<String, List<OpsCategoryPropertiesGetListResult>>> getResTypePropertiesList(List<String> categoryCodes) {
        return this.getResTypePropertiesList(categoryCodes, null, null);
    }

    @Override
    public RightCloudResult<Map<String, List<OpsCategoryPropertiesGetListResult>>> getResTypePropertiesList(List<String> categoryCodes,
                                                                                                            String... ignoreProperties) {
        return this.getResTypePropertiesList(categoryCodes, null, ignoreProperties);
    }

    @Override
    public RightCloudResult<Map<String, List<OpsCategoryPropertiesGetListResult>>> getResTypePropertiesList(List<String> categoryCodes,
                                                                                                            CmdbCategoryPropertiesType propertiesType,
                                                                                                            String... ignoreProperties) {
        Map<String, List<OpsCategoryPropertiesGetListResult>> resultMap = new HashMap<>();
        ResCategoryFeignForm feignForm = new ResCategoryFeignForm();
        if (CollUtil.isNotEmpty(categoryCodes)) {
            String codes = StrUtil.join(StrUtil.COMMA, categoryCodes);
            feignForm.setCodes(codes);
        }
        feignForm.setHasProperty(Boolean.TRUE);
        // 忽略属性
        List<String> ignorePropertiesList;
        if (ArrayUtil.isNotEmpty(ignoreProperties)) {
            ignorePropertiesList = List.of(ignoreProperties);
        } else {
            ignorePropertiesList = new ArrayList<>();
        }
        final RightCloudResult<List<CmdbResCategoryGetListFeignV2Result>> categoryProperty = cmdbResCategoryClient.getResCategory(feignForm);
        if (Objects.nonNull(categoryProperty) && CollectionUtil.isNotEmpty(categoryProperty.getData())) {
            final List<CmdbResCategoryGetListFeignV2Result> cmdbResCategoryPropertiesGetV2ResultList = categoryProperty.getData();
            for (CmdbResCategoryGetListFeignV2Result cmdbResCategoryPropertiesGetV2Result : cmdbResCategoryPropertiesGetV2ResultList) {
                if (Objects.nonNull(cmdbResCategoryPropertiesGetV2Result.getProperties())) {
                    List<OpsCategoryPropertiesGetListResult> opsCategoryPropertiesGetListResults
                            = cmdbResCategoryPropertiesGetV2Result.getProperties()
                            .stream()
                            .filter(Objects::nonNull)
                            .sorted(Comparator.comparing(cmdbResPropertyVOResult -> {
                                int sort = 1;
                                if (Objects.nonNull(cmdbResPropertyVOResult.getSortRank())) {
                                    sort = cmdbResPropertyVOResult.getSortRank();
                                }
                                return sort;
                            }))
                            .map(cmdbResPropertyVOResult -> {
                                final OpsCategoryPropertiesGetListResult opsCategoryPropertiesGetListResult = BeanHelperUtil.copyForBean(
                                        OpsCategoryPropertiesGetListResult::new,
                                        cmdbResPropertyVOResult
                                );
                                List<String> propTypeList = new ArrayList<>();
                                if (StringUtils.isNotEmpty(cmdbResPropertyVOResult.getPropType())) {

                                    final String[] propTypeArray = cmdbResPropertyVOResult.getPropType().split(StringPool.COMMA);
                                    if (ArrayUtil.isNotEmpty(propTypeArray)) {
                                        for (String propType : propTypeArray) {
                                            if (!StringUtils.equals(StringPool.COMMA, propType)) {
                                                propTypeList.add(propType);
                                            }
                                        }
                                    }
                                }
                                opsCategoryPropertiesGetListResult.setPropType(propTypeList);
                                return opsCategoryPropertiesGetListResult;
                            })
                            .filter(properties -> {
                                if (StrUtil.isBlank(properties.getCode())) {
                                    return false;
                                }
                                if (ignorePropertiesList.contains(properties.getCode())) {
                                    return false;
                                }
                                // 类型
                                return Objects.isNull(propertiesType) || properties.getPropType().contains(propertiesType.getCode());
                            })
                            .collect(Collectors.toList());
                    resultMap.put(cmdbResCategoryPropertiesGetV2Result.getCategoryCode(), opsCategoryPropertiesGetListResults);
                }
            }

        }
        return RightCloudResult.success(resultMap);
    }


    /**
     * 将tree结构转换为list路径结构
     *
     * @param list    原始数据
     * @param results 返回数据
     * @return CmdbResCategoryTagFeignV2Result
     */
    private List<List<CmdbResCategoryTagFeignV2Result>> formListCmdbResType(List<CmdbResCategoryTagFeignV2Result> list,
                                                                            List<CmdbResCategoryTagFeignV2Result> results) {
        List<List<CmdbResCategoryTagFeignV2Result>> listList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (CmdbResCategoryTagFeignV2Result cmdbResCategoryDataQueryFeignResult : list) {
                List<CmdbResCategoryTagFeignV2Result> treePath = new ArrayList<>();
                treePath.addAll(results);
                CmdbResCategoryTagFeignV2Result treeVo = new CmdbResCategoryTagFeignV2Result();
                //将tree复制给treeVo
                BeanUtil.copyProperties(cmdbResCategoryDataQueryFeignResult, treeVo);
                treeVo.setChildren(null);
                //将child为零的对象放入路径中
                treePath.add(treeVo);
                //检查当前节点对象的child是否为空
                if (CollectionUtil.isEmpty(cmdbResCategoryDataQueryFeignResult.getChildren())) {
                    listList.add(treePath);
                } else {
                    List<List<CmdbResCategoryTagFeignV2Result>> resultList =
                            formListCmdbResType(cmdbResCategoryDataQueryFeignResult.getChildren(), treePath);
                    //迭代结束
                    listList.addAll(resultList);
                }
            }
        }
        return listList;
    }

    @Override
    public RightCloudResult<OpsCategoryPropertiesGetListResult> getCategoryDetails(String code) {
        BizAssertUtils.notBlank(code, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY, MonitorFieldKeyConstant.ALARM_OBJECT_CODE);
        final RightCloudResult<CmdbResCategoryGeDetailsFeignV2Result> resultRightCloudResult =
                cmdbResCategoryClient.get(code);
        OpsCategoryPropertiesGetListResult opsCategoryPropertiesGetListResult = new OpsCategoryPropertiesGetListResult();
        if (Objects.nonNull(resultRightCloudResult) && Objects.nonNull(resultRightCloudResult.getData())) {
            final CmdbResCategoryGeDetailsFeignV2Result categoryGeDetailsResult = resultRightCloudResult.getData();
            opsCategoryPropertiesGetListResult =
                    BeanHelperUtil.copyForBean(OpsCategoryPropertiesGetListResult::new, categoryGeDetailsResult);
        }
        return RightCloudResult.success(opsCategoryPropertiesGetListResult);
    }

    @Override
    public RightCloudResult<List<OpsEnvGetListResult>> getEnvList() {
        final String tagEnvCategoryType = "CLOUD_ENV_TYPE";
        List<OpsEnvGetListResult> envGetListResults = new ArrayList<>();
        ResCategoryTreeQueryForm resCategoryTreeQueryForm = new ResCategoryTreeQueryForm();
        resCategoryTreeQueryForm.setTagCodes(List.of(tagEnvCategoryType));
        // 查询云平台数据
        final RightCloudResult<List<CmdbResCategoryTagFeignV2Result>> envCategoryDataResult = cmdbResCategoryClient.data(resCategoryTreeQueryForm);
        if (Objects.nonNull(envCategoryDataResult) && CollectionUtil.isNotEmpty(envCategoryDataResult.getData())) {
            final List<CmdbResCategoryTagFeignV2Result> data = envCategoryDataResult.getData();
            envGetListResults = data.stream()
                    .filter(Objects::nonNull)
                    .map(resCategoryTree -> {
                        OpsEnvGetListResult opsEnvGetListResult = null;
                        if (StringUtils.isNotEmpty(resCategoryTree.getCode())) {
                            final String[] envs = resCategoryTree.getCode().split(StringPool.HASH);
                            if (ArrayUtil.isNotEmpty(envs) && envs.length > 1) {
                                opsEnvGetListResult = new OpsEnvGetListResult();
                                final String envCode = envs[0];
                                final String envVersion = envs[1];
                                final CmdbResCategoryTagFeignV2Result categoryTagFeignV2Result = resCategoryTree.getData();
                                if (Objects.nonNull(categoryTagFeignV2Result)) {
                                    opsEnvGetListResult.setEnvCode(envCode);
                                    opsEnvGetListResult.setVersionNumber(envVersion);
                                    opsEnvGetListResult.setName(resCategoryTree.getName());
                                    opsEnvGetListResult.setNameEn(categoryTagFeignV2Result.getNameEn());
                                    opsEnvGetListResult.setIcon(categoryTagFeignV2Result.getIcon());
                                }
                            }
                        }
                        return opsEnvGetListResult;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return RightCloudResult.success(envGetListResults.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public RightCloudResult<OpsCloudEnvGetInstanceResult> getCloudEnvInstance(String instanceId) {
        BizAssertUtils.notBlank(instanceId, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.CLOUD_ENV_ID);
        ResInstPageV1FeignForm pageV1FeignForm = new ResInstPageV1FeignForm();
        pageV1FeignForm.setInstanceIds(List.of(instanceId));
        pageV1FeignForm.setReturnProperties(getReturnProperties(OpsCloudEnvGetInstanceResult.class));
        final CmdbResInstDetailsFeignResult resInstDetails = this.getResInstByPropertiesDetail(pageV1FeignForm);
        BizAssertUtils.isTrue(Objects.nonNull(resInstDetails),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT, MonitorFieldKeyConstant.CLOUD_ENV);
        OpsCloudEnvGetInstanceResult opsCloudEnvGetInstanceResult = new OpsCloudEnvGetInstanceResult();
        if (CollectionUtil.isNotEmpty(resInstDetails.getProperties())) {
            final String propertiesJson = JSONObject.toJSONString(resInstDetails.getProperties());
            opsCloudEnvGetInstanceResult =
                    JSONObject.parseObject(propertiesJson, OpsCloudEnvGetInstanceResult.class);
            handleData(opsCloudEnvGetInstanceResult);
        }
        return RightCloudResult.success(opsCloudEnvGetInstanceResult);
    }

    @Override
    public RightCloudResult<List<OpsCloudEnvGetInstanceResult>> getCloudEnvInstance(List<String> instanceIds) {
        BizAssertUtils.notEmpty(instanceIds, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.CLOUD_ENV_ID);
        ResInstPageV1FeignForm cmdbResInstPageListForm = new ResInstPageV1FeignForm();
        cmdbResInstPageListForm.setInstanceIds(instanceIds);
        cmdbResInstPageListForm.setReturnProperties(getReturnProperties(OpsCloudEnvGetInstanceResult.class));
        final List<MorResInstListDto> morResInstListDtoList = this.buildResInstListFeignResult(cmdbResInstPageListForm);
        BizAssertUtils.isTrue(CollectionUtil.isNotEmpty(morResInstListDtoList),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT, MonitorFieldKeyConstant.CLOUD_ENV);
        List<OpsCloudEnvGetInstanceResult> opsCloudEnvGetInstanceResults = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(morResInstListDtoList)) {
            for (MorResInstListDto resInstListFeignResult : morResInstListDtoList) {
                final String propertiesJson = JSONObject.toJSONString(resInstListFeignResult.getProperties());
                OpsCloudEnvGetInstanceResult opsCloudEnvGetInstanceResult =
                        JSONObject.parseObject(propertiesJson, OpsCloudEnvGetInstanceResult.class);
                if (Objects.nonNull(opsCloudEnvGetInstanceResult)) {
                    opsCloudEnvGetInstanceResults.add(opsCloudEnvGetInstanceResult);
                    handleData(opsCloudEnvGetInstanceResult);
                }
            }
        }
        return RightCloudResult.success(opsCloudEnvGetInstanceResults);
    }


    @Override
    public RightCloudResult<PageResult<OpsCloudEnvGetInstanceResult>> getCloudEnvInstance(ResInstPageV1FeignForm param) {
        BizAssertUtils.notNull(param, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.ENV_CODE);
        param.setReturnProperties(getReturnProperties(OpsCloudEnvGetInstanceResult.class));
        final RightCloudResult<PageResult<Map<String, Object>>> resultRightCloudResult = cmdbResInstV1Client.postPage(param);
        BizAssertUtils.isTrue(resultRightCloudResult.isSuccess() && Objects.nonNull(resultRightCloudResult.getData()),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT, MonitorFieldKeyConstant.CLOUD_ENV);
        PageResult<OpsCloudEnvGetInstanceResult> result = new PageResult<>();
        result.setTotal(0L);
        result.setTotalPages(0);
        result.setPageNo(param.getPageNo());
        result.setPageSize(param.getPageSize());
        result.setList(new ArrayList<>());
        if (Objects.nonNull(resultRightCloudResult.getData())) {
            final PageResult<Map<String, Object>> data = resultRightCloudResult.getData();
            result.setTotal(data.getTotal());
            result.setTotalPages(data.getTotalPages());
            result.setPageNo(data.getPageNo());
            result.setPageSize(data.getPageSize());
            List<OpsCloudEnvGetInstanceResult> opsCloudEnvGetInstanceResults = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(data.getList())) {
                final List<Map<String, Object>> cmdbResInstPageFeignResults = data.getList();
                for (Map<String, Object> cmdbResInstPageFeignResult : cmdbResInstPageFeignResults) {
                    final String propertiesJson = JSONObject.toJSONString(cmdbResInstPageFeignResult);
                    OpsCloudEnvGetInstanceResult opsCloudEnvGetInstanceResult =
                            JSONObject.parseObject(propertiesJson, OpsCloudEnvGetInstanceResult.class);
                    opsCloudEnvGetInstanceResults.add(opsCloudEnvGetInstanceResult);
                }
            }
            result.setList(opsCloudEnvGetInstanceResults);
        }
        return RightCloudResult.success(result);
    }


    private static List<String> getReturnProperties(Class aClass) {
        final String instanceIdName = "id";
        final String codeName = "categoryCode";
        final String instanceNameName = "name";
        final String projectIdName = "projectId";
        final String urnKeyName = "urn";
        final String uriKeyName = "uri";
        Set<String> returnProperties = new HashSet<>();
        returnProperties.add(UU_ID);
        returnProperties.add(ORG_ID);
        returnProperties.add(instanceIdName);
        returnProperties.add(codeName);
        returnProperties.add(instanceNameName);
        returnProperties.add(urnKeyName);
        returnProperties.add(uriKeyName);
        returnProperties.add(projectIdName);
        //查询计算维度所需要的字段
        final Field[] declaredFields = aClass.getDeclaredFields();
        List<String> properties = Arrays.stream(declaredFields).map(Field::getName).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(properties)) {
            returnProperties.addAll(properties.stream().filter(item -> !returnProperties.contains(item)).collect(Collectors.toSet()));
        }

        return ListUtil.toList(returnProperties);
    }


    @Override
    public RightCloudResult<PageResult<OpsCategoryInstanceGetPageResult>> getCategoryInstanceGetPageResult(OpsCategoryInstanceGetPageParam param) {
        // 查询资源实例数据
        final ResInstPageV1FeignForm cmdbResInstPageFeignForm = BeanHelperUtil.copyForBean(
                ResInstPageV1FeignForm::new,
                param
        );
        final RightCloudResult<PageResult<Map<String, Object>>> page = cmdbResInstV1Client.postPage(cmdbResInstPageFeignForm);
        PageResult<OpsCategoryInstanceGetPageResult> result = new PageResult<>();
        result.setPageNo(param.getPageNo());
        result.setTotal(0L);
        result.setPageSize(param.getPageSize());
        result.setTotalPages(0);
        if (Objects.nonNull(page) && Objects.nonNull(page.getData())) {
            final PageResult<Map<String, Object>> data = page.getData();
            result.setPageNo(data.getPageNo());
            result.setTotal(data.getTotal());
            result.setPageSize(data.getPageSize());
            result.setTotalPages(data.getTotalPages());
            List<OpsCategoryInstanceGetPageResult> categoryInstanceGetPageResults = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(data.getList())) {
                categoryInstanceGetPageResults = data.getList().stream()
                        .filter(Objects::nonNull)
                        .map(this::buildCategoryInstanceGetPageResult)
                        .collect(Collectors.toList());
            }
            result.setList(categoryInstanceGetPageResults);
        }

        return RightCloudResult.success(result);
    }

    private OpsCategoryInstanceGetPageResult buildCategoryInstanceGetPageResult(Map<String, Object> propertiesMap) {
        final String instanceIdName = "id";
        final String codeName = "categoryCode";
        final String instanceNameName = "name";
        OpsCategoryInstanceGetPageResult resInstListFeignResult = null;
        if (CollectionUtil.isNotEmpty(propertiesMap)) {
            resInstListFeignResult = JSONObject.parseObject(JSONObject.toJSONString(propertiesMap), OpsCategoryInstanceGetPageResult.class);
            final Object instanceId = propertiesMap.get(instanceIdName);
            if (Objects.nonNull(instanceId)) {
                resInstListFeignResult.setInstanceId(instanceId.toString());
            }
            final Object code = propertiesMap.get(codeName);
            if (Objects.nonNull(code)) {
                resInstListFeignResult.setCode(code.toString());
            }
            final Object instanceName = propertiesMap.get(instanceNameName);
            if (Objects.nonNull(instanceName)) {
                resInstListFeignResult.setInstanceName(instanceName.toString());
            }

            final Set<String> keySet = propertiesMap.keySet();
            List<CmdbResInstPropValFeignResult> propValFeignResults = new ArrayList<>();
            for (String key : keySet) {
                final Object value = propertiesMap.get(key);
                if (Objects.nonNull(value)) {
                    CmdbResInstPropValFeignResult cmdbResInstPropValFeignResult = new CmdbResInstPropValFeignResult();
                    cmdbResInstPropValFeignResult.setPropCode(key);
                    cmdbResInstPropValFeignResult.setPropValue(value.toString());
                    propValFeignResults.add(cmdbResInstPropValFeignResult);
                }
            }
            resInstListFeignResult.setResInstPropValList(propValFeignResults);
        }
        return resInstListFeignResult;

    }


    @Override
    public RightCloudResult<List<OpsCategoryInstanceGetCountResult>> getCategoryInstanceCount(OpsCategoryInstanceGetListParam param) {
        List<OpsCategoryInstanceGetCountResult> categoryInstanceGetCountResults = new ArrayList<>();
        final CmdbResInstPageListForm form = BeanHelperUtil.copyForBean(
                CmdbResInstPageListForm::new,
                param
        );
        final RightCloudResult<List<CmdbResInstCountFeignResult>> resInstCount = cmdbResInstClient.getResInstCount(form);
        if (Objects.nonNull(resInstCount) && CollectionUtil.isNotEmpty(resInstCount.getData())) {
            categoryInstanceGetCountResults = BeanHelperUtil.copyForList(
                    OpsCategoryInstanceGetCountResult::new,
                    resInstCount.getData()
            );
        }
        return RightCloudResult.success(categoryInstanceGetCountResults);
    }

    @Override
    public RightCloudResult<List<String>> getCategoryInstanceByEnvId(List<String> instanceIds) {
        List<String> cloudEnvIdList = new ArrayList<>();
        ResInstPageV1FeignForm opsResInstanceGetListParam = new ResInstPageV1FeignForm();
        opsResInstanceGetListParam.setInstanceIds(instanceIds);
        opsResInstanceGetListParam.setReturnProperties(List.of(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME));
        final List<MorResInstListDto> resultRightCloudResultData = this.buildResInstListFeignResult(opsResInstanceGetListParam);
        if (CollectionUtil.isNotEmpty(resultRightCloudResultData)) {
            // 获取云环境id
            for (MorResInstListDto resultRightCloudResultDatum : resultRightCloudResultData) {
                final Map<String, Object> properties = resultRightCloudResultDatum.getProperties();
                if (CollectionUtil.isNotEmpty(properties)) {
                    final Object cloudEnvId = properties.get(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME);
                    if (Objects.nonNull(cloudEnvId)) {
                        cloudEnvIdList.add(cloudEnvId.toString());
                    }
                }
            }
        }
        return RightCloudResult.success(cloudEnvIdList);
    }

    /**
     * 获取资源数据
     *
     * @param pageV1FeignForm 条件参数
     */
    private CmdbResInstDetailsFeignResult getResInstByPropertiesDetail(ResInstPageV1FeignForm pageV1FeignForm) {
        CmdbResInstDetailsFeignResult detailsFeignResult = null;
        // 获取云环境信息
        if (Objects.nonNull(pageV1FeignForm)) {
            final RightCloudResult<PageResult<Map<String, Object>>> resInstDetails = cmdbResInstV1Client.postPage(pageV1FeignForm);
            if (!resInstDetails.isSuccess()) {
                log.error(
                        "Get cloud environment data, failed, error message: {}",
                        resInstDetails.getMessage()
                );
            }
            if (Objects.nonNull(resInstDetails)
                    && resInstDetails.isSuccess()
                    && Objects.nonNull(resInstDetails.getData())
                    && CollectionUtil.isNotEmpty(resInstDetails.getData().getList())) {
                final List<Map<String, Object>> envResInstanceListMap = resInstDetails.getData().getList();
                final Map<String, Object> envResInstanceMap = envResInstanceListMap.stream()
                        .filter(Objects::nonNull)
                        .findFirst()
                        .orElse(new HashMap<>());
                if (CollectionUtil.isNotEmpty(envResInstanceMap)) {
                    final String propertiesJson = JSONObject.toJSONString(envResInstanceMap);
                    detailsFeignResult = JSONObject.parseObject(propertiesJson, CmdbResInstDetailsFeignResult.class);
                    final Object name = envResInstanceMap.get("name");
                    if (Objects.nonNull(name)) {
                        detailsFeignResult.setInstanceName(envResInstanceMap.get("name").toString());
                    }
                    final Object id = envResInstanceMap.get("id");
                    if (Objects.nonNull(id)) {
                        detailsFeignResult.setInstanceId(envResInstanceMap.get("id").toString());
                    }
                    detailsFeignResult.setProperties(envResInstanceMap);
                }
            }
        }
        return detailsFeignResult;
    }


    /**
     * map建树
     *
     * @param list 数据集合
     * @param <T>  泛型
     * @return 树菜单
     */
    private <T extends AbstractTreeV2Result> List<T> buildTree(List<T> list) {
        List<T> result = new ArrayList<>();
        Map<String, T> map = list.stream().collect(Collectors.toMap(T::getCode, x -> x));
        for (T t : list) {
            T category = map.get(t.getCode());
            String parentId = category.getParentCode();
            if (StringUtils.isEmpty(parentId)) {
                result.add(category);
            } else {
                T parentCategory = map.get(parentId);
                if (Objects.nonNull(parentCategory)) {
                    if (CollectionUtil.isEmpty(parentCategory.getChildren())) {
                        parentCategory.setChildren(new ArrayList<>());
                    }
                    parentCategory.getChildren().add(category);
                } else {
                    result.add(category);
                }
            }
        }
        return result;
    }

    /**
     * 处理该对象的某些字段的数据以使其合规
     *
     * <AUTHOR>
     */
    private void handleData(OpsCloudEnvGetInstanceResult opsCloudEnvGetInstanceResult) {
        //处理 MonitorResources 字段数据，将字符串拆分成数组
        opsCloudEnvGetInstanceResult.setMonitorResources(opsCloudEnvGetInstanceResult.getMonitorResources().stream()
                .filter(item -> StrUtil.isNotBlank(item))
                .map(item -> {
                    if (item.startsWith("[") && item.endsWith("]")) {
                        return JSON.parseArray(item).stream().map(item2 -> StrUtil.toString(item2)).collect(Collectors.toList());
                    } else {
                        return ListUtil.toList(item);
                    }
                })
                .flatMap(item -> item.stream())
                .collect(Collectors.toList())
        );
    }

}
