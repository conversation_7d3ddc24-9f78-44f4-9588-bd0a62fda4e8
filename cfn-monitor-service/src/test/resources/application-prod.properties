#springåºç¡ä¿¡æ¯éç½®
spring.messages.basename=messages
spring.messages.encoding=utf-8

#åºç¨ä¿¡æ¯éç½®
applicationId=355afb1ff03f446aba1454a919af1ea2
smart.app.applicationId=4bab8a4fb7cf11e8b55e0cc47ad87bef

#redisä¿¡æ¯éç½®
spring.redis.host=redis.service.consul
spring.redis.port=6379
spring.redis.password=Yuncong2018
spring.redis.database=2
spring.redis.timeout=0
spring.redis.pool.max-active=10
spring.redis.pool.max-wait=-1
spring.redis.pool.max-idle=10
spring.redis.pool.min-idle=0

#æå°æ¥å¿ä¿¡æ¯éç½®
logging.level.root=info
logging.config=./logback.xml
logging.path=logs
logging.level.cn.cloudwalk=debug

#æ°æ®åºè¿æ¥éç½®
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.url=******************************************************************************************************************************************************************************
spring.datasource.username=cloudwalk
spring.datasource.password=Yuncong2018
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.max-lifetime=1765000
spring.datasource.hikari.auto-commit=true
mybatis.mapper-locations=classpath*:cn/cloudwalk/data/**/mysql/*.xml
mybatis.config-location=classpath:mapper/mybatis-config.xml

#consulæå¡åç°ãéç½®ç®¡çä¸­å¿éç½®
spring.cloud.consul.host=http://127.0.0.1
spring.cloud.consul.port=8500
spring.cloud.consul.discovery.prefer-ip-address=true
cloud.consul.host=127.0.0.1
cloud.consul.port=8500

#ä¼ä¸ID
gansuwanwei.custId=150

