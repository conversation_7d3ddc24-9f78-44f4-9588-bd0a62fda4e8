package com.example.monitor.dao.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.example.monitor.entity.result.OpsTableNotifyTargetContactsGroupResultDTO;
import com.example.monitor.mapper.OpsNotifyTargetContactsGroupMapper;
import com.example.monitor.dao.OpsNotifyTargetContactsGroupDao;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>描述: [OpsNotifyTargetContactsGroup 服务实现层] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Repository
public class OpsNotifyTargetContactsGroupDaoImpl implements OpsNotifyTargetContactsGroupDao {
    
    private OpsNotifyTargetContactsGroupMapper opsNotifyTargetContactsGroupMapper;
    
    public OpsNotifyTargetContactsGroupDaoImpl(OpsNotifyTargetContactsGroupMapper opsNotifyTargetContactsGroupMapper) {
        this.opsNotifyTargetContactsGroupMapper = opsNotifyTargetContactsGroupMapper;
    }

    @Override
    public List<OpsTableNotifyTargetContactsGroupResultDTO> selectList(Wrapper<OpsTableNotifyTargetContactsGroupResultDTO> queryWrapper) {
        return opsNotifyTargetContactsGroupMapper.selectList(queryWrapper);
    }

    @Override
    public OpsTableNotifyTargetContactsGroupResultDTO selectOne(Wrapper<OpsTableNotifyTargetContactsGroupResultDTO> queryWrapper) {
        return opsNotifyTargetContactsGroupMapper.selectOne(queryWrapper);
    }
}
