package com.example.monitor.entity.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>描述: [OpsNotifyTargetContacts 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OpsTableNotifyTargetContactsParamDto implements Serializable {
    private static final long serialVersionUID = -55034945747531966L;
    /**
    * id
    */
    private String id;
    /**
    * 用户名称
    */
    private String userName;
    /**
    * 用户手机号
    */
    private String userPhone;
    /**
    * 用户邮箱
    */
    private String userEmail;
    /**
    * 记录创建时间
    */
    private Date createdDt;
    /**
    * 记录修改时间
    */
    private Date updateDt;
    /**
    * 创建用户ID
    */
    private String createBy;
    /**
    * 最后修改用户ID
    */
    private String updateBy;
    /**
    * 组织id
    */
    private String orgId;
    /**
    * 版本号
    */
    private Long version;

}
