<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.operation.mapper.OpsNotifyTargetMapper">

    <resultMap type="com.example.monitor.entity.result.OpsTableNotifyTargetResultDTO" id="OpsNotifyTargetMap">
        <result property="id" column="id" jdbcType=""/>
        <result property="notifyPolicyId" column="notify_policy_id" jdbcType=""/>
        <result property="notifyWay" column="notify_way" jdbcType=""/>
        <result property="notifyContactsCategory" column="notify_contacts_category" jdbcType=""/>
        <result property="notifyId" column="notify_id" jdbcType=""/>
        <result property="createdBy" column="created_by" jdbcType=""/>
        <result property="createdDt" column="created_dt" jdbcType=""/>
        <result property="updatedBy" column="updated_by" jdbcType=""/>
        <result property="updatedDt" column="updated_dt" jdbcType=""/>
        <result property="version" column="version" jdbcType=""/>
    </resultMap>
    
</mapper>
