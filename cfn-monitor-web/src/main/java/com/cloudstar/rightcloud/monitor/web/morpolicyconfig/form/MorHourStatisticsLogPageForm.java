package com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;

import java.io.Serializable;

import lombok.Data;

/**
 * 小时汇聚任务日志
 *
 * @author: hjy
 * @date: 2023/11/10 17:19
 */
@Data
public class MorHourStatisticsLogPageForm extends PageForm implements Serializable {
    /**
     * 资源类型
     */
    private String resTypeCode;
    /**
     * 统计指标项
     */
    private String performanceMetricIndicatorCode;
    /**
     * 监控采集指标id
     */
    private Long monitorCollectMetricId;

    /**
     * 监控采集指标
     */
    private String monitorMetricCode;
    /**
     * 监控采集指标
     */
    private String monitorCollectMetricName;
    /**
     * 状态;success 成功 fail 失败
     */
    private String status;
    /**
     * 创建用户
     */
    private String createdBy;

    /**
     * 汇聚开始时间
     */
    private String mergeTimeStart;

    /**
     * 汇聚结束时间
     */
    private String mergeTimeEnd;
}
