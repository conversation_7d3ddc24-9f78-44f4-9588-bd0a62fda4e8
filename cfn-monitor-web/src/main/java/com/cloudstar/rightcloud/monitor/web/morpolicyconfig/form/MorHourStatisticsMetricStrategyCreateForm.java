package com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;

import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import lombok.Data;

/**
 * 小时性能汇聚指标策略创建参数
 *
 * @author: hjy
 * @date: 2023/11/8 10:42
 */
@Data
public class MorHourStatisticsMetricStrategyCreateForm implements Serializable {

    /**
     * 资源类型
     */
    @NotBlank
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.RES_TYPE_CODE)
    @Length(max = 255)
    private String resTypeCode;
    /**
     * 统计指标项
     */
    @NotBlank
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.PERFORMANCE_METRIC_INDICATOR_CODE)
    @Length(max = 255)
    private String performanceMetricIndicatorCode;
    /**
     * 监控指标id组
     */
    @NotEmpty
    private List<MorMetricRelaForm> metrics;

}
