package com.cloudstar.rightcloud.monitor.web.opsaccess.controller;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.monitor.client.opsaccess.param.OpsAccessEnvParam;
import com.cloudstar.rightcloud.monitor.client.opsaccess.param.OpsAccessMonitorEnvCheckParam;
import com.cloudstar.rightcloud.monitor.client.opsaccess.param.OpsAccessResourceParam;
import com.cloudstar.rightcloud.monitor.client.opsaccess.service.OpsAccessService;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmNotifyWebHookParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.service.OpsAlarmNotifyWebHookService;
import com.cloudstar.rightcloud.monitor.web.opsaccess.form.OpsAccessEnvForm;
import com.cloudstar.rightcloud.monitor.web.opsaccess.form.OpsAccessMonitorEnvCheckForm;
import com.cloudstar.rightcloud.monitor.web.opsaccess.form.OpsAccessResourceForm;
import com.cloudstar.rightcloud.monitor.web.opsaccess.form.OpsAccessWebHookAlertsForm;
import com.cloudstar.rightcloud.monitor.web.opsaccess.form.OpsAccessWebHookAnnotationsForm;
import com.cloudstar.rightcloud.monitor.web.opsaccess.form.OpsAccessWebHookLabelsFormData;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 云环境监控
 *
 * @author: wanglang
 * @date: 2023/2/13 8:05 PM
 */
@RestController
@RequestMapping("/access")
@AllArgsConstructor
@Slf4j
public class OpsAccessController {


    private final OpsAccessService opsAccessService;

    private final OpsAlarmNotifyWebHookService opsAlarmNotifyWebHookService;


    /**
     * 告警web hook 推送[内部]
     *
     * @param webHookForm web hook 推送参数
     * @return
     */
    @PostMapping("/web/hook")
    public RightCloudResult<Boolean> webHookNotify(@RequestBody OpsAccessWebHookAlertsForm webHookForm) {
        log.warn("webHookForm:{}", JSONUtil.toJsonStr(webHookForm));
        final List<OpsAlarmNotifyWebHookParam> opsAlarmNotifyWebHookParams = new ArrayList<>();
        if (Objects.nonNull(webHookForm)) {
            final OpsAlarmNotifyWebHookParam opsAlarmNotifyWebHookParam = BeanHelperUtil.copyForBean(OpsAlarmNotifyWebHookParam::new, webHookForm);
            final OpsAccessWebHookAnnotationsForm annotations = webHookForm.getAnnotations();
            final OpsAccessWebHookLabelsFormData labels = webHookForm.getLabels();
            BeanUtils.copyProperties(labels, opsAlarmNotifyWebHookParam);
            opsAlarmNotifyWebHookParam.setContext(annotations.getDescription());
            opsAlarmNotifyWebHookParam.setName(webHookForm.getRuleName());
            opsAlarmNotifyWebHookParams.add(opsAlarmNotifyWebHookParam);
        }
        return opsAlarmNotifyWebHookService.webHookNotify(opsAlarmNotifyWebHookParams);
    }

    /**
     * 云环境接入监控[内部]
     *
     * @param form 接入云环境监控数据
     * @return true 操作成功 false 操作失败
     */
    @PostMapping("/cloud/env")
    public RightCloudResult<Boolean> accessEnv(@RequestBody List<OpsAccessEnvForm> form) {
        return opsAccessService.accessEnv(BeanHelperUtil.copyForList(OpsAccessEnvParam::new, form));
    }

    /**
     * 资源推送[内部]
     *
     * @param form 接入云环境监控数据
     * @return true 操作成功 false 操作失败
     */
    @PostMapping("/cloud/resource")
    public RightCloudResult<Boolean> accessEnvResource(@RequestBody List<OpsAccessResourceForm> form) {
        return opsAccessService.accessEnvResource(BeanHelperUtil.copyForList(OpsAccessResourceParam::new, form));
    }


    /**
     * 校验监控是否允许开启
     *
     * @param form 参数
     * @return true 允许 false 不允许
     */
    @GetMapping("/check/configuration")
    public RightCloudResult<Boolean> checkMonitorConfiguration(OpsAccessMonitorEnvCheckForm form) {
        final OpsAccessMonitorEnvCheckParam opsAccessMonitorEnvCheckParam = BeanHelperUtil.copyForBean(
                OpsAccessMonitorEnvCheckParam::new,
                form
        );
        return opsAccessService.checkMonitorConfiguration(opsAccessMonitorEnvCheckParam);
    }

}
