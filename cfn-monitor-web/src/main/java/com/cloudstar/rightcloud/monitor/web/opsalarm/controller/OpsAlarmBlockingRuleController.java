package com.cloudstar.rightcloud.monitor.web.opsalarm.controller;

import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.ParamAssertUtils;
import com.cloudstar.rightcloud.log.common.annotation.OperationLog;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmBlockingQueryParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmBlockingDetailResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmBlockingRulePageResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.service.OpsAlarmBlockingRuleService;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationMessageConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogSourceConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogTypeConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.web.opsalarm.form.OpsAlarmBlockingCreateForm;
import com.cloudstar.rightcloud.monitor.web.opsalarm.form.OpsAlarmBlockingPageQueryForm;
import com.cloudstar.rightcloud.monitor.web.opsalarm.form.OpsAlarmBlockingUpdateForm;

import lombok.AllArgsConstructor;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * 告警屏蔽规则
 *
 * @author: 卢泳舟
 * @date: 2023/6/7 11:34
 */
@RestController
@RequestMapping("/alarm/blocking")
@AllArgsConstructor
@Validated
public class OpsAlarmBlockingRuleController {

    private final OpsAlarmBlockingRuleService opsAlarmBlockingRuleService;

    /**
     * 查询告警屏蔽（分页）[废弃]
     *
     * @param pageQueryForm 告警屏蔽查询条件
     * @return OpsAlarmBlockingResult 告警屏蔽列表数据
     */
    @GetMapping
    public RightCloudResult<PageResult<OpsAlarmBlockingRulePageResult>> getAlarmBlockingPage(OpsAlarmBlockingPageQueryForm pageQueryForm) {

        // 参数转化类型
        OpsAlarmBlockingQueryParam param = BeanHelperUtil.copyForBean(OpsAlarmBlockingQueryParam::new, pageQueryForm);
        // 分页查询
        return opsAlarmBlockingRuleService.getBlockingRulePage(param);
    }


    /**
     * 查询告警屏蔽详情[废弃]
     *
     * @param id 告警屏蔽id
     * @return OpsAlarmBlockingDetailsResult 告警屏蔽详情信息
     */
    @GetMapping("/{id}")
    public RightCloudResult<OpsAlarmBlockingDetailResult> get(@PathVariable("id") String id) {
        return RightCloudResult.success(new OpsAlarmBlockingDetailResult());
    }

    /**
     * 创建告警屏蔽[废弃]
     *
     * @param alarmBlockingForm 告警屏蔽数据
     * @return id 创建成功数据id
     */
    @PostMapping
    @OperationLog(type = MonitorOperationlogTypeConstant.CREATE_SHIELD_RULE, objectName = "#alarmBlockingForm.name",
            resource = MonitorOperationlogSourceConstant.SHIELD_RULE,
            msg = MonitorOperationMessageConstant.ACTION_SHIELD_RULE_CREATE, msgParams = {"#alarmBlockingForm.name"})
    public RightCloudResult<String> create(@RequestBody OpsAlarmBlockingCreateForm alarmBlockingForm) {
        return RightCloudResult.success("");
    }

    /**
     * 编辑告警屏蔽[废弃]
     *
     * @param alarmBlockingUpdateForm 告警屏蔽数据
     * @return Boolean true 成功 false 失败
     */
    @PutMapping
    @OperationLog(type = MonitorOperationlogTypeConstant.UPDATE_SHIELD_RULE, objectId = "#alarmBlockingUpdateForm.id",
            resource = MonitorOperationlogSourceConstant.SHIELD_RULE,
            msg = MonitorOperationMessageConstant.ACTION_SHIELD_RULE_UPDATE, msgParams = {"#alarmBlockingUpdateForm.id"})
    public RightCloudResult<Boolean> update(@RequestBody OpsAlarmBlockingUpdateForm alarmBlockingUpdateForm) {
        return RightCloudResult.success(true);
    }

    /**
     * 禁用/启用告警屏蔽[废弃]
     *
     * @param id 告警屏蔽id
     * @return Boolean true 成功 false 失败
     */
    @PutMapping("/{id}/status")
    @OperationLog(type = MonitorOperationlogTypeConstant.UPDATE_SHIELD_RULE_STATUS, objectId = "#id",
            resource = MonitorOperationlogSourceConstant.SHIELD_RULE,
            msg = MonitorOperationMessageConstant.ACTION_SHIELD_RULE_STATUS, msgParams = {"#id"})
    public RightCloudResult<Boolean> updateStatus(@PathVariable("id") @NotBlank String id) {
        ParamAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                                 MonitorFieldKeyConstant.ID);
        return opsAlarmBlockingRuleService.updateBlockingStatus(id);
    }

    /**
     * 删除告警屏蔽规则[废弃]
     *
     * @param ids 告警屏蔽ids
     * @return Boolean true 成功 false 失败
     */
    @DeleteMapping
    @OperationLog(type = MonitorOperationlogTypeConstant.DELETE_SHIELD_RULE, objectId = "#ids",
            resource = MonitorOperationlogSourceConstant.SHIELD_RULE,
            msg = MonitorOperationMessageConstant.ACTION_SHIELD_RULE_DELETE, msgParams = {"#ids"})
    public RightCloudResult<Boolean> delete(@RequestBody List<String> ids) {
        return RightCloudResult.success(true);
    }

}
