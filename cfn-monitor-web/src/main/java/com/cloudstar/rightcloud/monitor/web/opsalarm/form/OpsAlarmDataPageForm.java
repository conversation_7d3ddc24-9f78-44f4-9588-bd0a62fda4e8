package com.cloudstar.rightcloud.monitor.web.opsalarm.form;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 告警数据分页请求参数
 *
 * @author: wanglang
 * @date: 2023/6/1 6:07 PM
 */
@Data
public class OpsAlarmDataPageForm extends PageForm {
    /**
     * 告警名称
     */
    private String name;

    /**
     * 告警对象
     */
    private String target;

    /**
     * 告警来源
     */
    private String source;


    /**
     * 所属组织
     */
    private Long orgId;

    /**
     * 所属云环境
     */
    private Long envId;

    /**
     * 所属项目
     */
    private Long projectId;

    /**
     * 处理人
     */
    private String processingUser;

    /**
     * 对象类型
     */
    private String targetType;

    /**
     * 告警持续时长
     */
    private String duration;

    /**
     * 处理状态
     */
    private String processingStatus;

    /**
     * 告警级别
     */
    private String level;
    /**
     * 状态
     */
    private String status;
    /**
     * 开始发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startOccurTime;
    /**
     * 结束发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endOccurTime;

    /**
     * 告警对象实例id
     */
    private Long objectInstanceId;

}
