package com.cloudstar.rightcloud.monitor.web.opsalarm.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.validator.HorizontalAuth;

import org.hibernate.validator.constraints.Length;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 编辑通知策略详情
 *
 * @author: 卢泳舟
 * @date: 2023/6/5 9:17
 */
@Data
public class OpsNotifyPolicyUpdateForm {

    /**
     * id
     */
    @NotNull
    @HorizontalAuth(table = "monitor_notify_policy", ignoreProjectFilter = true)
    private Long id;

    /**
     * 通知策略名称
     */
    @SafeHtml
    @Length(max = 64, min = 2)
    @NotBlank
    private String name;

    /**
     * 通知对象
     */
    @NotEmpty
    @Valid
    private List<OpsNotifyPolicyTargetForm> opsNotifyPolicyTargetForms;

    /**
     * 告警恢复通知状态
     */
    @NotBlank
    private String alarmRestorationNotifyStatus;

    /**
     * 告警通知模版类型code
     */
    @NotBlank
    private String alarmNotifyTemplateTypeCode;

    /**
     * 告警恢复模版类型code
     */
    @NotBlank
    private String alarmRestorationTemplateTypeCode;

    /**
     * 告警通知开始时间段
     */
    @NotBlank
    private String alarmNotifyStartTimePeriod;

    /**
     * 告警通知结束时间段
     */
    @NotBlank
    private String alarmNotifyEndTimePeriod;


    /**
     * 重复通知状态
     */
    @NotBlank
    private String repetitionNotifyStatus;


    /**
     * 重复通知间隔时间
     */
    private String repetitionNotifyIntervalTime;


    /**
     * 告警升级状态
     */
    private String alarmNotifyUpgradePolicyStatus;

    /**
     * 告警升级策略id
     */
    private Long alarmNotifyUpgradePolicyId;

}
