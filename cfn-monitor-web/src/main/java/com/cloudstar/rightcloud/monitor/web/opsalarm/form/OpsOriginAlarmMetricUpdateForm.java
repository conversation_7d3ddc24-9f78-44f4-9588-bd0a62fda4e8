package com.cloudstar.rightcloud.monitor.web.opsalarm.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.common.validator.EnumValue;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;

import javax.validation.constraints.Digits;

/**
 * 原生告警指标修改参数
 *
 * <AUTHOR>
 */
@Data
public class OpsOriginAlarmMetricUpdateForm {

    /**
     * 告警名称
     */
    @SafeHtml
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ORIGIN_ALARM_METRIC_NAME)
    private String name;
    /**
     * 告警编码
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ALARM_CODE)
    private String alarmCode;
    /**
     * 是否启用
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ALARM_STATUS)
    private String status;
    /**
     * 描述
     */
    @SafeHtml
    private String description;
    /**
     * 告警来源
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ALARM_SOURCE)
    private String source;
    /**
     * 告警分类（env_code_alarm 云平台告警 res_type_code_alarm 云资源告警）
     */
    @EnumValue(strValues = {"env_code_alarm", "res_type_code_alarm"})
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ALARM_CLASSIFICATION)
    private String alarmClassification;
    /**
     * 产品组件
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ALARM_PRODUCT_COMPONENT)
    private String productComponent;
    /**
     * 告警级别状态
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.ALARM_LEVEL)
    private String alarmLevelStatus;
    /**
     * 告警通知策略id
     */
    @Digits(integer = 22, fraction = 0)
    private Long opsNotifyPolicyId;
    /**
     * 告警转译模版
     */
    @SafeHtml
    private String alarmTranslateTemplate;
    /**
     * 告警转译字段
     */
    private String alarmTranslateField;
    /**
     * 告警原文示例
     */
    private String alarmOriginalText;

}
