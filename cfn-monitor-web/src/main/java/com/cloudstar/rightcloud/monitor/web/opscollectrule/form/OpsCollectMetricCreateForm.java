package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;

import org.hibernate.validator.constraints.Length;

import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * 监控指标创建
 *
 * @author: wanglang
 * @date: 2023/6/16 15:57
 */
@Data
public class OpsCollectMetricCreateForm implements Serializable {

    /**
     * 采集指标名称
     */
    @NotBlank
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.MONITOR_METRIC_NAME)
    private String name;

    /**
     * 状态
     */
    @NotBlank
    @Length(max = 50)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.MONITOR_METRIC_STATUS)
    private String status;

    /**
     * 资源类型编码
     */
    @NotBlank
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.RES_TYPE_CODE)
    private String resTypeCode;

    /**
     * 采集规则维度id
     */
    @Digits(integer = 22, fraction = 0)
    private Long opsCollectRuleDimensionsId;

    /**
     * 采集规则id
     */
    @NotNull
    @Digits(integer = 22, fraction = 0)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_ID)
    private Long opsCollectRuleId;


    /**
     * 采集指标统一编码id
     */
    @NotNull
    @Digits(integer = 22, fraction = 0)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COMMON_MONITOR_METRIC)
    private Long monitorCommonMetricId;

    /**
     * 采集指标原始编码
     */
    @NotBlank
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.MONITOR_METRIC_ORIGINAL_CODING)
    private String originalCoding;

    private String unit;

    /**
     * 排序编码
     */
    @NotNull
    @Digits(integer = 11, fraction = 0)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.MONITOR_METRIC_SORT_RANK)
    private Integer sortRank;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 255)
    private String description;

    /**
     * 单位转换因子
     */
    private Double unitConvFactor;

    /**
     * 指标命名空间
     */
    private String namespace;

    /**
     * 是否默认展示
     */
    private Boolean defaultDisplay;

    /**
     * uuid
     */
    private String uuid;

}
