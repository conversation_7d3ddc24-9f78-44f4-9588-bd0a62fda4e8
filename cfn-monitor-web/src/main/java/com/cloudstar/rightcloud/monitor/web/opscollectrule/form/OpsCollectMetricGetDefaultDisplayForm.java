package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控指标查询条件参数
 *
 * @author: wanglang
 * @date: 2023/6/16 11:38
 */
@Data
public class OpsCollectMetricGetDefaultDisplayForm extends PageForm implements Serializable {


    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 采集规则id
     */
    private Long collectRuleId;

    /**
     * 维度名称
     */
    private String dimensionsName;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 云平台
     */
    private String envCode;

    /**
     * 云平台编码
     */
    private String envVersion;

    /**
     * 云环境id
     */
    private String cloudEnvId;
}
