package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * 采集规则修改参数
 *
 * @author: wanglang
 * @date: 2023/07/20 11:16
 */
@Data
public class OpsCollectRuleUpdateForm implements Serializable {

    /**
     * 名称
     */
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_NAME)
    private String name;

    /**
     * 资源类型
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.RES_TYPE_CODE)
    private String resTypeCode;

    /**
     * 云平台
     */
    private String envCode;

    /**
     * 云平台版本号
     */
    private String envVersion;


    /**
     * 采集组件id
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.OPS_EXPORTER_ID)
    private Long opsExporterId;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 255)
    private String description;

}
