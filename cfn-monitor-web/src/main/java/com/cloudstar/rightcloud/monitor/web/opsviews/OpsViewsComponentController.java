package com.cloudstar.rightcloud.monitor.web.opsviews;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsViewsComponentTopMetricDataParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentAlarmRecentTrendsResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentCategoryAlarmResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentCategoryAlarmTotalResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentMetricResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentRecentAlarmDataResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentTopMetricDataResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.service.OpsViewsComponentService;
import com.cloudstar.rightcloud.monitor.web.opsviews.form.OpsViewsComponentTopMetricDataForm;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 视图-组件
 *
 * @author: wanglang
 * @date: 2023/8/17 19:52
 */
@RestController
@RequestMapping("/views/component")
@AllArgsConstructor
public class OpsViewsComponentController {

    private final OpsViewsComponentService opsViewsComponentService;


    /**
     * 查询分类告警统计条数
     *
     * @return OpsViewsCategoryAlarmTotalResult 分类告警总数
     */
    @GetMapping("/category/alarm/total")
    public RightCloudResult<OpsViewsComponentCategoryAlarmTotalResult> getCategoryAlarmTotal(String status) {
        return opsViewsComponentService.getCategoryAlarmTotal(status);
    }

    /**
     * 查询分类告警统计总数
     *
     * @return OpsViewsCategoryAlarmResult 分类告警
     */
    @GetMapping("/category/alarm/count")
    public RightCloudResult<List<OpsViewsComponentCategoryAlarmResult>> getCategoryAlarmCount(String status) {
        return opsViewsComponentService.getCategoryAlarmCount(status);
    }

    /**
     * 查询最近告警趋势
     *
     * @return OpsViewsAlarmRecentTrendsResult 最近告警趋势
     */
    @GetMapping("/alarm/recent/trends")
    public RightCloudResult<List<OpsViewsComponentAlarmRecentTrendsResult>> getAlarmRecentTrends() {
        return opsViewsComponentService.getAlarmRecentTrends();
    }

    /**
     * 查询近期告警数据
     *
     * @return Map 告警条数
     */
    @GetMapping("/alarm/total")
    public RightCloudResult<Map<String, String>> getAlarmTotal(String status) {
        return opsViewsComponentService.getAlarmTotal(status);
    }

    /**
     * 获取指标数据
     *
     * @param categoryCode 分类
     * @return OpsViewsComponentMetricResult 指标数据
     */
    @GetMapping("/metrics/{categoryCode}")
    public RightCloudResult<List<OpsViewsComponentMetricResult>> getMetricList(@PathVariable("categoryCode") String categoryCode) {
        return opsViewsComponentService.getMetricList(categoryCode);
    }


    /**
     * 查询Top指标数据
     *
     * @param form 条件参数
     * @return OpsViewsComponentTopMetricDataResult top指标数据
     */
    @GetMapping("/metric/value")
    public RightCloudResult<List<OpsViewsComponentTopMetricDataResult>> getTopMetricData(OpsViewsComponentTopMetricDataForm form) {
        final OpsViewsComponentTopMetricDataParam param = BeanHelperUtil.copyForBean(
                OpsViewsComponentTopMetricDataParam::new,
                form
        );
        return opsViewsComponentService.getTopMetricData(param);
    }


    /**
     * 查询告警条数
     *
     * @param minute 分钟
     * @return OpsViewsComponentAlarmDataResult 组件告警数据
     */
    @GetMapping("/recent/alarm/{minute}")
    public RightCloudResult<List<OpsViewsComponentRecentAlarmDataResult>> getRecentAlarmData(@PathVariable("minute") Integer minute) {
        return opsViewsComponentService.getRecentAlarmData(minute);
    }

}
