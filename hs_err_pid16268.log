#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 257949696 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=16268, tid=8300
#
# JRE version:  (21.0.6+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+8-b631.39, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://git.rightcloud.com.cn': 

Host: AMD Ryzen 5 5600U with Radeon Graphics         , 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.457)
Time: Mon May 26 18:06:41 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.457) elapsed time: 0.016513 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000025373d21840):  JavaThread "Unknown thread" [_thread_in_vm, id=8300, stack(0x0000007c68b00000,0x0000007c68c00000) (1024K)]

Stack: [0x0000007c68b00000,0x0000007c68c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5d39]
V  [jvm.dll+0x8c4133]
V  [jvm.dll+0x8c668e]
V  [jvm.dll+0x8c6d73]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0x6e25f5]
V  [jvm.dll+0x6d60aa]
V  [jvm.dll+0x3635bb]
V  [jvm.dll+0x36b186]
V  [jvm.dll+0x3bd4b6]
V  [jvm.dll+0x3bd788]
V  [jvm.dll+0x335d2c]
V  [jvm.dll+0x336a1b]
V  [jvm.dll+0x88b589]
V  [jvm.dll+0x3ca688]
V  [jvm.dll+0x874698]
V  [jvm.dll+0x45f04e]
V  [jvm.dll+0x460d31]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1244d]
C  [ntdll.dll+0x5df78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd3a8ca148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x0000025375fe5f30 WorkerThread "GC Thread#0"                     [id=20008, stack(0x0000007c68c00000,0x0000007c68d00000) (1024K)]
  0x0000025375ff8330 ConcurrentGCThread "G1 Main Marker"            [id=22156, stack(0x0000007c68d00000,0x0000007c68e00000) (1024K)]
  0x0000025375ff9d40 WorkerThread "G1 Conc#0"                       [id=15860, stack(0x0000000000000000,0x0000000000000000) (0B)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd39fb8de7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd3a93eb30] Heap_lock - owner thread: 0x0000025373d21840

Heap address: 0x000000070ae00000, size: 3922 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x000000070ae00000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000002537ac20000,0x000002537b3d0000] _byte_map_base: 0x00000253773c9000

Marking Bits: (CMBitMap*) 0x0000025375fe8540
 Bits: [0x000002537b3d0000, 0x000002537f118000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.011 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7a2230000 - 0x00007ff7a223a000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\java.exe
0x00007ffda1bf0000 - 0x00007ffda1e04000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffda0020000 - 0x00007ffda00e2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd9f0f0000 - 0x00007ffd9f48c000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd9f5a0000 - 0x00007ffd9f6b1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd48bd0000 - 0x00007ffd48be8000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\jli.dll
0x00007ffda13d0000 - 0x00007ffda157b000 	C:\Windows\System32\USER32.dll
0x00007ffd9f950000 - 0x00007ffd9f976000 	C:\Windows\System32\win32u.dll
0x00007ffd9fdc0000 - 0x00007ffd9fde9000 	C:\Windows\System32\GDI32.dll
0x00007ffd9f830000 - 0x00007ffd9f942000 	C:\Windows\System32\gdi32full.dll
0x00007ffd73720000 - 0x00007ffd7373b000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\VCRUNTIME140.dll
0x00007ffd9f500000 - 0x00007ffd9f59a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd8cf90000 - 0x00007ffd8d21e000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.317_none_a9434687c10c9fa2\COMCTL32.dll
0x00007ffda03d0000 - 0x00007ffda0477000 	C:\Windows\System32\msvcrt.dll
0x00007ffd9fc70000 - 0x00007ffd9fca1000 	C:\Windows\System32\IMM32.DLL
0x00007ffd88e90000 - 0x00007ffd88e9c000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\vcruntime140_1.dll
0x00007ffd3f190000 - 0x00007ffd3f21d000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\msvcp140.dll
0x00007ffd39c70000 - 0x00007ffd3aa31000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\server\jvm.dll
0x00007ffda0b40000 - 0x00007ffda0bee000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffda0580000 - 0x00007ffda0624000 	C:\Windows\System32\sechost.dll
0x00007ffda1580000 - 0x00007ffda1695000 	C:\Windows\System32\RPCRT4.dll
0x00007ffd9ff90000 - 0x00007ffda0001000 	C:\Windows\System32\WS2_32.dll
0x00007ffd9ef00000 - 0x00007ffd9ef4d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd983a0000 - 0x00007ffd983d4000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd96d50000 - 0x00007ffd96d5a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffd9eee0000 - 0x00007ffd9eef3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd9e0b0000 - 0x00007ffd9e0c8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffd88a50000 - 0x00007ffd88a5a000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\jimage.dll
0x00007ffd9cb40000 - 0x00007ffd9cd6e000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffda0750000 - 0x00007ffda0ad9000 	C:\Windows\System32\combase.dll
0x00007ffd9fb80000 - 0x00007ffd9fc57000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffd8d890000 - 0x00007ffd8d8c2000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd9f980000 - 0x00007ffd9f9fb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd73700000 - 0x00007ffd73720000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.317_none_a9434687c10c9fa2;D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://git.rightcloud.com.cn': 
java_class_path (initial): D:/Work/IntelliJ IDEA 2024.3.4/plugins/vcs-git/lib/git4idea-rt.jar;D:/Work/IntelliJ IDEA 2024.3.4/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4112515072                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4112515072                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Work\JDK\jdk-11
CLASSPATH=.;D:\Work\JDK\jdk-11\lib;D:\Work\JDK\jdk-11\lib\tools.jar;
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\Work\JDK\jdk-11\bin;D:\Work\JDK\jdk-11\jre\bin;D:\Work\Erlang OTP\bin;D:\Work\Nodejs\;D:\Work\Maven\apache-maven-3.9.9\bin;C:\Program Files\TortoiseSVN\bin;D:\Work\protoc-30.2-win64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Work\Microsoft VS Code\bin
USERNAME=qinpeng
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12204K (0% of 16057404K total physical memory with 1419068K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.457)
OS uptime: 39 days 8:07 hours

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, fsrm, f16c, pku, cet_ss
Processor Information for processor 0
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 1
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 2
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 3
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 4
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 5
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 6
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 7
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 8
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 9
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 10
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301
Processor Information for processor 11
  Max Mhz: 2301, Current Mhz: 1990, Mhz Limit: 2301

Memory: 4k page, system-wide physical 15681M (1385M free)
TotalPageFile size 29725M (AvailPageFile size 80M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 64M, peak: 310M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+8-b631.39) for windows-amd64 JRE (21.0.6+8-b631.39), built on 2025-02-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
