{"name": "cfn-module-integration", "version": "1.0.0", "description": "- controller 对外接口\r - dao 数据库操作\r     - mapper 数据库操作接口\r     - model 数据库DO\r - service 业务包\r   - pojo 数据对象包\r     - dto 数据传输对象 前端参数、业务转换参数等\r     - vo 返回展示到前端的对象包\r - resources\r   - docker dockerfile文件\r   - k8s 应用在k8s中的一些配置 service、deploy、configmap等\r   - smart 接口文档配置及输出", "main": "index.js", "dependencies": {}, "devDependencies": {"husky": "^8.0.1"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "npx husky install"}, "repository": {"type": "git", "url": "https://**************:8443/cfn/cfn-module-integration.git"}, "author": "", "license": "ISC"}