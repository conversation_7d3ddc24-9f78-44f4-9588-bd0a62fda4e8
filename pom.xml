<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cloudstar</groupId>
        <artifactId>cfn-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cloudstar.module.integration</groupId>
    <artifactId>cfn-module-integration</artifactId>
    <version>${cfn.module.integration}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.cloudstar.common.base</groupId>
            <artifactId>cfn-common-base</artifactId>
            <version>${cfn.common.base}</version>
        </dependency>

        <dependency>
            <groupId>com.cloudstar.common.sysmconfig</groupId>
            <artifactId>cfn-common-component-sysmconfig-starter</artifactId>
            <version>${cfn.common.component.sysmconfig.starter}</version>
        </dependency>

        <dependency>
            <groupId>com.cloudstar.common.component.redis</groupId>
            <artifactId>cfn-common-component-redis-starter</artifactId>
            <version>${cfn.common.component.redis.starter}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java-bundle</artifactId>
            <version>${esdk-obs-java-bundle.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-swr</artifactId>
            <version>${huaweicloud-sdk-swr-version}</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-iam</artifactId>
            <version>${huaweicloud-sdk-iam-version}</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.apigateway</groupId>
            <artifactId>java-sdk-core</artifactId>
            <version>3.2.6</version>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>
