package com.cloudstar;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 安排组装
 *
 * <AUTHOR>
 */
@EnableAsync(proxyTargetClass = true)
@MapperScan({"com.cloudstar.dao.mapper"})
@EnableFeignClients
@EnableScheduling
@SpringBootApplication
public class ScheduleAssembly {

    public static void main(String[] args) {
        SpringApplication.run(ScheduleAssembly.class, args);
    }
}
