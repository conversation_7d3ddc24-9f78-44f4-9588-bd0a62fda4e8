package com.cloudstar.aimarket.camunda;


import com.cloudstar.aimarket.enums.CamundaExecutionEnum;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

/**
 * start event execution listener
 *
 * <AUTHOR>
 * @date 2025/04/27
 */
@Slf4j
@Component
public class StartEventExecutionListener extends BaseProcessExecution implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) {
        log.info("businessKey:{},开始审批", delegateExecution.getBusinessKey());
        execution(delegateExecution);
    }

    @Override
    CamundaExecutionEnum currentTriggerExecution() {
        return CamundaExecutionEnum.START_EVENT;
    }
}
