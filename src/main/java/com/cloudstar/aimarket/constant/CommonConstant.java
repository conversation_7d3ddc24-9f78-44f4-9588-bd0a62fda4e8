package com.cloudstar.aimarket.constant;

/**
 * bss客户端共同常量
 *
 * <AUTHOR>
 * @date 2023/5/22 14:02
 */
public interface CommonConstant {

    /**
     * redis缓存token key
     */
    String AUTH_TOKEN_REDIS_KEY = "BSS_AUTH_TOKEN";

    /**
     * 请求module_type
     */
    String REQUEST_MODULE_TYPE = "bss";

    /**
     * 请求session_id头
     */
    String BSS_SESSION_ID = "CUST_SESSION_ID";


    /**
     * 请求session_id头
     */
    String BSS_SESSION = "SESSIONID";

    /**
     * 请求module_type头
     */
    String BSS_MODULE_TYPE = "moduleType";

    /**
     * 请求subject头
     */
    String BSS_SUBJECT = "subject";

    /**
     * 请求auth头
     */
    String BSS_AUTHORIZATION = "authorization";

    /**
     * Accept-Language头
     */
    String ACCEPT_LANGUAGE = "Accept-Language";

    /**
     *  zh-CN
     */
    String LANGUAGE_ZH_CN = "zh-CN";

    /**
     * 获取token
     */
    String AUTH_TOKEN_URL = "/api/v1/iam/auth/token";

    /**
     * 共享资源池 产品code
     */
    String SHARE_POOL_PRODUCT_CODE = "ModelArts";

    /**
     * 专属资源池 产品code
     */
    String DRP_POOL_PRODUCT_CODE = "DRP";
}
