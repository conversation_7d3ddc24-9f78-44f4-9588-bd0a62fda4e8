package com.cloudstar.aimarket.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * process audit status enum
 *
 * <AUTHOR>
 * @date 2025/04/27
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum ProcessAuditStatusEnum {

    UNFINISHED("未审批"),
    FINISHED("已审批");

    private String desc;
}
