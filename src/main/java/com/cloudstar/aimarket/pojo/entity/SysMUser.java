package com.cloudstar.aimarket.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户表
 * @TableName sys_m_user
 */
@TableName(value = "sys_m_user")
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class SysMUser implements Serializable {
    /**
     * 用户SID
     */
    @TableId(type = IdType.AUTO)
    private Long userSid;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 账号
     */
    private String account;

    /**
     * password
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 性别 0:男 1:女
     */
    private Integer sex;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 职务头衔
     */
    private String title;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 用户状态（0:禁用，1:有效，2:锁定）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码错误次数
     */
    private Integer errorCount;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 上次登录IP地址
     */
    private String lastLoginIp;

    /**
     * 账号有效开始时间
     */
    private Date startTime;

    /**
     * 账号有效开始时间
     */
    private Date endTime;

    /**
     * 服务限制数量
     */
    private Integer serviceLimitQuantity;

    /**
     * 申请理由
     */
    private String applyReason;

    /**
     * 最大短信数
     */
    private Integer smsMax;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户偏好主题
     */
    private String skinTheme;

    /**
     * 第三方认证绑定ID
     */
    private String authId;

    /**
     * 账户认证类型 1. local 2. ad
     */
    private String authType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * open ID
     */
    private String openId;

    /**
     * 微信头像
     */
    private String avatarUrl;

    /**
     * 微信用户所在省
     */
    private String province;

    /**
     * 微信用户所在城市
     */
    private String city;

    /**
     * 微信用户所在国家
     */
    private String country;

    /**
     * 微信账号名称
     */
    private String wechatName;

    /**
     * IAM域ID
     */
    private String domainId;

    /**
     * IAM用户ID
     */
    private String iamId;

    /**
     * IAM密码过期时间
     */
    private String passwordExpiresAt;

    /**
     * IAM是否强制重置密码
     */
    private String forceresetpwd;

    /**
     * IAM默认项目ID
     */
    private String defaultProjectId;

    /**
     * IAM最后访问项目ID
     */
    private String lastProjectId;

    /**
     * IAM密码强度
     */
    private String pwdStrength;

    /**
     * 父账号ID
     */
    private Long parentSid;

    /**
     * noAuth 待认证, authing 认证中, authSucceed认证成功, authFiled认证失败
     */
    private String certificationStatus;

    /**
     * 身份证正面图片路径
     */
    private String idCardFront;

    /**
     * 身份证反面图片路径
     */
    private String idCardReverse;

    /**
     *  用户资源状态（0:禁用，1:启用）
     */
    private String freezeStatus;

    /**
     * 冻结类型（0为自动1为手动）
     */
    private String unfreezeType;

    /**
     * 身份证名称
     */
    private String authName;

    /**
     * 导航栏确认（已经操作过为Y）
     */
    private String navigationConfirm;

    /**
     * 所处行业
     */
    private String industry;

    /**
     * 密码结束时间
     */
    private Date pwdEndTime;

    /**
     * 是否同意隐私协议： 0未同意、1已同意
     */
    private Integer policyAgreeSign;

    /**
     * 同意隐私协议时间
     */
    private Date policyAgreeTime;

    /**
     * 解冻时间
     */
    private Date unfreezeTime;

    /**
     * 业务标识tag，多个以;分隔，拓展中：expansion，已备案：recorded，试算中：trial，已签单：signed，商用中：commercial，欠费中：arrearage[]，已注销：cancelled
     */
    private String businessTag;

    /**
     * 密码有效开始时间
     */
    private Date pwdStartTime;

    /**
     * 国密计算的mac值
     */
    private String ccspMac;

    /**
     * 大模型授权标识
     */
    private String authorizeTag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
