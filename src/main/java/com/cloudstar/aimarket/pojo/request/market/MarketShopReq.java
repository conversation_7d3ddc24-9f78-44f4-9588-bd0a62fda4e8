package com.cloudstar.aimarket.pojo.request.market;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * market shop req
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
public class MarketShopReq {

    /**
     * 主键
     */
    private String shopId;

    /**
     * 所属人userId
     */
    private Long ownerId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品标签：多个逗号隔开
     */
    private List<Long> labels;

    /**
     * 被删除规格List
     */
    private List<ShopPriceAttr> delPriceAttrsList;

    /**
     * 被删除规格List
     */
    private List<ShopPriceAttr> savePriceAttrsList;

    /**
     * 商品logo
     */
    private String logoPath;

    /**
     * 商品来源：platform 平台自营，supplier 供应商
     */
    private String shopSource;

    /**
     * 商品状态：pending 待审核，refuse 审核拒绝，lineding 待上架，online 已上架，offline 已下架
     */
    private String status;

    /**
     * 浏览次数
     */
    private Integer browseNum;

    /**
     * 订阅次数
     */
    private Integer subscribeNum;

    /**
     * 收藏次数
     */
    private Integer collectNum;

    /**
     * 商品价格规格
     */
    private String priceAttr;

    /**
     * 商品简介
     */
    private String introduce;

    /**
     * 商品说明
     */
    private String description;

    /**
     * 资产类型（目前只支持算法）：model：模型，algo：算法，simple_workflow：workflow。
     */
    private String type;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    private Integer shopType;

    /**
     * 商品是否免费：0免费，1按月，2按年
     */
    private Integer sellType;

    /**
     * 可见性：为public（全部
     可见）、group（组内可见）和
     private（仅自己可见）（影响aihub）
     */
    private String visibility = "private";

    /**
     * 组内用户，当可见性为
     “public”或"private"时，该参
     数不需要设置；当可见性为
     “group”，至少需要填写一个
     用户的租户ID值。（影响aihub）
     */
    private String groupUsers;

    /**
     * 算法id：选择的算法id
     */
    private String arithmetic;

    /**
     * 对应aihub的资产ids，多个逗号隔开。一对多，对应多个价格规格
     */
    private String aiHubShopIds;

    /**
     * MA来源：默认值ModelArts,从配置表中取market_shop_source
     */
    private String aiHubSource;

    /**
     * ModelArts区域，默认cn-east-325,从配置表中取market_shop_area
     */
    private String aiHubArea;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
