package com.cloudstar.aimarket.pojo.request.market;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * AI商品版本
 * @TableName market_shop_version
 */
@Data
public class MarketVersionStatusReq  implements Serializable {

    /**
     * 版本状态：0上架，1下架
     */
    private String status;


    /**
     * 商品id
     */
    @NotBlank(message = "商品id不能为空")
    private String shopId;

    /**
     * 版本描述
     */
    private String versionDesc;

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String versionNum;









}
