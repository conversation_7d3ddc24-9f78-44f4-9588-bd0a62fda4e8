/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package com.cloudstar.aimarket.pojo.response.discount;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * describe discount response
 *
 * <AUTHOR>
 * @date 2020/3/19.
 */
@Data
public class DescribeDiscountResponse {
    /**
     * 折扣sid
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long discountSid;

    /**
     * 折扣名字
     */
    private String discountName;

    /**
     * 折扣类型
     */
    private String discountType;

    /**
     * 用户sid
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userSid;

    /**
     * 折扣来源
     */
    private String originType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 适用环境
     */
    private List<String> cloudEnvScopes;

    /**
     * 适用产品
     */
    private List<String> productScopes;

    /**
     * 应用范围
     */
    private String scopeType;

    /**
     * 范围值
     */
    private String scopeValue;

    /**
     * 折扣系数
     */
    private BigDecimal discountRatio;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 描述
     */
    private String description;
}
