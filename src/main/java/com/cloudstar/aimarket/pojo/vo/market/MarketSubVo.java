package com.cloudstar.aimarket.pojo.vo.market;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订阅信息
 *
 * <AUTHOR>
 * @date 2023/08/17
 */
@Data
public class MarketSubVo {

    /**
     * 订阅id
     */
    private String subscribeId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 付款金额
     */
    private BigDecimal payPrice;

    /**
     * 订阅人
     */
    private String payOrgName;

    /**
     * 商品提供商名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 资金监管状态
     */
    private String superviseStatus;

    /**
     * 结算状态
     */
    private String settlementStatus;

}
