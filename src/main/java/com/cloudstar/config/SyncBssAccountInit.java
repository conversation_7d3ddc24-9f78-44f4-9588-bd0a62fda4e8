package com.cloudstar.config;

import cn.hutool.system.SystemUtil;
import com.cloudstar.service.facade.sync.SyncBssAccountService;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.PostConstruct;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 同步运营账号
 *
 * <AUTHOR>
 * @date 2023/6/19 10:54
 */
@ConditionalOnProperty(prefix = "cfn.mq", name = "start", havingValue = "true")
@Configuration
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@DependsOn(value = {"bssConfigInit"})
public class SyncBssAccountInit {

    SyncBssAccountService service;

    /**
     * init
     */
    @PostConstruct
    public void init() {
        if (!SystemUtil.getBoolean("SYNC_BSS_USER_FLAG", true)) {
            return;
        }
        try {
            service.syncManager();
            service.syncUser();
            service.syncSubUser();
        } catch (Exception e) {
            log.error("初始化同步运营账号失败：", e);
        }
    }
}
