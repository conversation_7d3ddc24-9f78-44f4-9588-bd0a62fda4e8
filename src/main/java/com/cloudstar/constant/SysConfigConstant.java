package com.cloudstar.constant;

/**
 * 系统常量
 *
 * <AUTHOR>
 * @date 2024/6/12 15:29
 */
public interface SysConfigConstant {

    interface AgentLoginConstant {

        /**
         * 登录type
         */
        String CONFIG_TYPE = "cluster_agent_login_field";

        /**
         * 登录-用户名
         */
        String ACCOUNT = "agent_login_account";

        /**
         * 登录-密码
         */
        String SECRET = "agent_login_secret";
    }

    interface AgentSysEnvConstant {

        /**
         * 环境信息type
         */
        String CONFIG_TYPE = "agent_sys_env_info";

        /**
         * 环境信息-最后同步时间
         */
        String CLUSTER_FINAL_SYNC = "cluster_final_sync";

        /**
         * 环境信息-适配器集群UUID
         */
        String CLUSTER_UUID = "agent_cluster_uuid";

        /**
         * 环境信息-监控采集间隔
         */
        String MONITOR_COLLECT = "monitor_collect";

        /**
         * 环境信息-心跳检查间隔
         */
        String HEART_CHECK = "heart_check";

        /**
         * 环境信息-数据采集间隔
         */
        String DATA_COLLECT = "data_collect";

        /**
         * 环境信息-集群名称
         */
        String CLUSTER_NAME = "cluster_name";

        /**
         * 环境信息-集群端口
         */
        String CLUSTER_PORT = "cluster_port";

        /**
         * 环境信息-集群ak
         */
        String CLUSTER_ACCESS_KEY = "cluster_access_key";

        /**
         * 环境信息-集群sk
         */
        String CLUSTER_SECURITY_KEY = "cluster_security_key";

        /**
         * 环境信息-集群状态
         */
        String CLUSTER_STATUS = "cluster_status";

        /**
         * 环境信息-适配器网关地址
         */
        String CLUSTER_GATEWAY_ADDRESS = "cluster_gateway_address";

        /**
         * 环境信息-集群地址
         */
        String CLUSTER_ADDRESS = "cluster_address";

        /**
         * 环境信息-集群类型
         */
        String CLUSTER_TYPE = "cluster_type";

        /**
         * 环境信息-算力底座
         */
        String CALCULATE_BASE = "calculate_base";

        /**
         * 环境信息-HCS地址
         */
        String HCS_ADDRESS = "hcs_address";

        /**
         * 环境信息-AK
         */
        String ACCESS_KEY = "access_key";

        /**
         * 环境信息-SK
         */
        String SECRET_KEY = "secret_key";

        /**
         * 环境信息-对象存储
         */
        String OBS = "obs";

        /**
         * 环境信息-华为obs地址
         */
        String HW_OBS_ADDRESS = "hw_obs_address";

        /**
         * 环境信息-自建obs地址
         */
        String SELF_OBS_ADDRESS = "self_obs_address";

        /**
         * 环境信息-自建obs账号
         */
        String SELF_OBS_ACCOUNT = "self_obs_account";

        /**
         * 环境信息-自建obs密码
         */
        String SELF_OBS_PASSWORD = "self_obs_password";

        /**
         * 环境信息-镜像仓库
         */
        String SWR = "swr";

        /**
         * 环境信息-华为swr地址
         */
        String HW_SWR_ADDRESS = "hw_swr_address";

        /**
         * 环境信息-华为公共镜像组织
         */
        String HW_PUBLIC_MIRROR_ORGANIZATION = "hw_public_mirror_organization";

        /**
         * 环境信息-自建swr地址
         */
        String SELF_SWR_ADDRESS = "self_swr_address";

        /**
         * 环境信息-自建swr账号
         */
        String SELF_SWR_ACCOUNT = "self_swr_account";

        /**
         * 环境信息-自建swr密码
         */
        String SELF_SWR_PASSWORD = "self_swr_password";

        /**
         * 环境信息-自建公共镜像组织
         */
        String SELF_PUBLIC_MIRROR_ORGANIZATION = "self_public_mirror_organization";
    }
}
