package com.cloudstar.controller;

import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.enums.TrainingJobGroupStatusEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.DataStorageResourceMessage;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.mapper.tenantmapping.ClusterUserMappingMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.dao.model.training.TrainingJobGroup;
import com.cloudstar.sdk.schedule.pojo.TrainingJobLogResp;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.datastorage.SlurmAossService;
import com.cloudstar.service.facade.training.TrainingJobGroupService;
import com.cloudstar.service.grpc.AgentShowTrainingJobMetricsProto.TrainingJobMetrics;
import com.cloudstar.service.grpcservice.facade.AgentObsService;
import com.cloudstar.service.grpcservice.facade.AgentShowTrainingJobMetricsService;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobGroupService;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobService;
import com.cloudstar.service.pojo.dto.cluster.ClusterResourcePoolDto;
import com.cloudstar.service.pojo.dto.cluster.ClusterUserMappingBillDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 作业训练
 *
 * <AUTHOR>
 * @date 2022/8/31 16:22
 */
@RestController
@RequestMapping("/training")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class TrainingJobEntityController {

    TrainingJobEntityMapper trainingJobEntityMapper;

    ClusterResourcePoolMapper clusterResourcePoolMapper;

    ClusterEntityMapper clusterEntityMapper;
    TrainingJobGroupService trainingJobGroupService;

    SlurmAossService slurmAossService;

    ServerClient serverClient;

    ClusterUserMappingMapper clusterUserMappingMapper;

    /**
     * 查询日志
     *
     * @param jobId jobid
     */
    @GetMapping("/log")
    public Rest<TrainingJobLogResp> queryTrainingJobLog(@RequestParam Long jobId, @RequestParam String podName) {
        TrainingJobEntity trainingJob = trainingJobEntityMapper.selectById(jobId);
        if (ObjectUtil.isEmpty(trainingJob)) {
            log.error("作业不存在");
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        ClusterEntity entity = clusterEntityMapper.selectById(trainingJob.getClusterId());
        if (ObjectUtil.isEmpty(entity)) {
            log.error("集群不存在");
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        TrainingJobLogResp resp = new TrainingJobLogResp();
        if (ObjectUtil.isEmpty(trainingJob.getJobId())) {

            resp.setFullSize(0);
            resp.setCurrentSize(0);
            resp.setContent("底层作业未创建，无日志！");
            log.info("底层作业未创建，无日志！");
            return Rest.ok(resp);
        }
        AgentTrainingJobService jobService = AgentTrainingJobService.build(entity.getAdapterUuid());
        return Rest.ok(jobService.queryTrainingJobLog(trainingJob, podName));
    }

    /**
     * 查询作业组日志
     *
     * @param groupId jobid
     */
    @GetMapping("/group/log/{groupId}")
    public Rest<String> queryTrainingJobGroupLog(@PathVariable("groupId") Long groupId) {
        Map<String, Object> params = new HashMap<>();
        params.put("cluster_flag", "SYSTEM");
        ClusterEntity entity = clusterEntityMapper.selectByMap(params).get(0);
        TrainingJobGroup jobGroup = trainingJobGroupService.getGroupById(groupId);
        if (jobGroup.getStatus().equalsIgnoreCase(TrainingJobGroupStatusEnum.NOT_DEFINED.name())) {
            return Rest.ok("");
        }
        AgentTrainingJobGroupService agentTrainingJobGroupService = AgentTrainingJobGroupService.build(
                entity.getAdapterUuid());
        try {
            return Rest.ok(agentTrainingJobGroupService.showTrainingJobGroupLogsPreview(groupId));
        } catch (Exception e) {
            return Rest.ok("无法获取日志，容器启动中");
        }
    }

    /**
     * 查询作业组的子作业日志 后期需要作业那边兼容
     *
     * @param groupId jobid
     */
    @GetMapping("/group/job/log/{groupId}")
    public Rest<String> queryTrainingJobGroupChildLog(@PathVariable("groupId") Long groupId,
                                                      @RequestParam("jobId") Long jobId) {
        Map<String, Object> params = new HashMap<>();
        params.put("cluster_flag", "SYSTEM");
        ClusterEntity entity = clusterEntityMapper.selectByMap(params).get(0);
        TrainingJobGroup jobGroup = trainingJobGroupService.getGroupById(groupId);
        if (jobGroup.getStatus().equalsIgnoreCase(TrainingJobGroupStatusEnum.NOT_DEFINED.name())) {
            return Rest.ok("");
        }
        AgentTrainingJobGroupService agentTrainingJobGroupService = AgentTrainingJobGroupService.build(
                entity.getAdapterUuid());
        try {
            return Rest.ok(agentTrainingJobGroupService.showTrainingJobGroupLogsPreview(groupId));
        } catch (Exception e) {
            return Rest.ok("无法获取日志，容器启动中");
        }
    }

    /**
     * 同步创建文件夹
     *
     * @param message 消息
     */
    @PostMapping("/dir/create")
    public Rest createDir(@RequestBody DataStorageResourceMessage message) {
        log.info("创建文件夹开始...");
        ClusterEntity entity = clusterEntityMapper.selectById(message.getClusterId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }

        //创建文件夹
        try {
            if (ClusterTypeEnum.BMS.getType().equals(entity.getClusterType())) {
                final ClusterSubAccountRespDto data = getClusterSubAccountReqDto(message);
                slurmAossService.createFolder(data, message.getFileDir(), entity.getClusterType());
            } else {
                AgentObsService service = AgentObsService.build(entity.getAdapterUuid());
                service.createFileDirRequest(message);
            }
        } catch (Exception e) {
            log.error("创建文件夹请求参数：{},创建文件夹错误：{}", message, e);
            return Rest.e(e.getMessage());
        }
        return Rest.ok(true);
    }


    /**
     * 获取用户信息
     *
     * @param message message
     */
    private ClusterSubAccountRespDto getClusterSubAccountReqDto(DataStorageResourceMessage message) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        clusterSubAccountReqDto.setClusterId(message.getClusterId());
        final List<ClusterUserMappingBillDto> allUser = clusterUserMappingMapper.getAllUser(message.getClusterId());
        final List<ClusterUserMappingBillDto> collect = allUser.stream()
                                                               .filter(user -> message.getUserId().equals(user.getUserId()))
                                                               .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            throw new BizException("未找到对应账号信息");
        }
        clusterSubAccountReqDto.setUserSid(collect.get(0).getUserSid());
        ClusterSubAccountRespDto data = serverClient.getClusterSubAccount(clusterSubAccountReqDto).getData();
        data.setAccessKey(CrytoUtilSimple.decrypt(data.getAccessKey()));
        data.setSecretKey(CrytoUtilSimple.decrypt(data.getSecretKey()));
        log.info("用户信息桶信息:{}", JSONUtil.toJsonStr(data));
        return data;
    }


    /**
     * 查询训练作业指定任务的运行指标
     *
     * @param jobId 作业id
     */
    @GetMapping("/metrics/{jobId}")
    public Rest<Map<String, List<Float>>> showTrainingJobMetrics(@PathVariable("jobId") Long jobId) {
        TrainingJobEntity trainingJob = trainingJobEntityMapper.selectById(jobId);
        if (ObjectUtil.isEmpty(trainingJob)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        if (ObjectUtil.isEmpty(trainingJob.getJobId())) {
            return Rest.ok(new HashMap<>(1));
        }
        ClusterEntity entity = clusterEntityMapper.selectById(trainingJob.getClusterId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }

        String poolId = null;
        if (trainingJob.getPoolId() != null) {
            ClusterResourcePoolDto resourcePoolDto = clusterResourcePoolMapper.queryById(trainingJob.getPoolId());
            poolId = resourcePoolDto.getPoolId();
        } else {
            poolId = "-1";
        }

        List<TrainingJobMetrics> metrics = AgentShowTrainingJobMetricsService.build(entity.getAdapterUuid())
                                                                             .showTrainingJobMetrics(
                                                                                     trainingJob.getJobId(), poolId);
        Map<String, List<Float>> collect = metrics.stream().collect(Collectors.toMap(TrainingJobMetrics::getMetric,
                                                                                     TrainingJobMetrics::getValueList));
        return Rest.ok(collect);
    }

}
