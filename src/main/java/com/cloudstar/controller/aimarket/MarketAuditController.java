package com.cloudstar.controller.aimarket;


import cn.hutool.core.util.StrUtil;
import com.cloudstar.aimarket.pojo.request.market.MarketAuditListReq;
import com.cloudstar.aimarket.pojo.request.market.MarketAuditProcessReq;
import com.cloudstar.aimarket.pojo.request.market.MarketProcessDetailsReq;
import com.cloudstar.aimarket.pojo.response.market.MarketAuditDetailsResp;
import com.cloudstar.aimarket.pojo.response.market.MarketAuditListResp;
import com.cloudstar.aimarket.pojo.response.market.MarketProcessDetailsResp;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.aimarket.MarketAuditService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequestMapping("/audit")
public class MarketAuditController {

    MarketAuditService marketAuditService;

    /**
     * 审批列表
     */
    @GetMapping("/shops")
    public Rest<PageResult<MarketAuditListResp>> queryShopByPage(@Validated MarketAuditListReq req) {
        return Rest.ok(marketAuditService.listAudit(req));
    }

    /**
     * 审批详情
     */
    @GetMapping("/details")
    public Rest<MarketAuditDetailsResp> getAuditDetails(@RequestParam String orderNo) {
        MarketAuditDetailsResp auditDetails = marketAuditService.getAuditDetails(orderNo);

        // 隐藏部分信息
        auditDetails.setUserName(StrUtil.hide(auditDetails.getUserName(), 1, auditDetails.getUserName().length()));
        auditDetails.setUserPhone(hide(auditDetails.getUserPhone()));
        auditDetails.setUserEmail(hide(auditDetails.getUserEmail()));

        return Rest.ok(auditDetails);

    }

    /**
     * 审批流程详情
     */
    @GetMapping("/process")
    public Rest<MarketProcessDetailsResp> getProcessDetails(@Validated MarketProcessDetailsReq req) {
        return Rest.ok(marketAuditService.getProcessDetails(req));
    }

    /**
     * 审批商品
     */
    @PostMapping("/shop")
    public Rest auditProcess(@RequestBody @Validated MarketAuditProcessReq req) {
        marketAuditService.auditProcess(req);
        return Rest.ok("审批成功");
    }

    /**
     * 隐藏中间四位
     *
     * @param seuCardId 你身份证
     * @return {@link String }
     */
    public static String hide(String seuCardId) {
        if (StrUtil.isBlank(seuCardId)) {
            return "";
        }
        //计算中间四位的下标
        int index = seuCardId.length() / 2;
        return StrUtil.hide(seuCardId, index - 3, index + 3);
    }


}
