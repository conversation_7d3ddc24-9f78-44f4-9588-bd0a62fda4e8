package com.cloudstar.controller.alarm;


import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;


import com.cloudstar.sdk.monitor.form.OpsAlarmDataPageForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmNotificationDetailsPageForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmNotificationPageForm;
import com.cloudstar.sdk.monitor.form.OpsHistoricalAlarmPageForm;
import com.cloudstar.sdk.monitor.form.OpsProcessingAlarmForm;
import com.cloudstar.sdk.monitor.annotations.HorizontalAuth;
import com.cloudstar.sdk.monitor.result.OpsAlarmDataPageResult;
import com.cloudstar.sdk.monitor.result.OpsAlarmDetailsResult;
import com.cloudstar.sdk.monitor.result.OpsAlarmNotificationDetailsResult;
import com.cloudstar.sdk.monitor.result.OpsAlarmNotificationResult;
import com.cloudstar.sdk.monitor.result.OpsHistoricalAlarmResult;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.OpsAlarmDataService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/alarm/data")
public class OpsAlarmDataController {

    private final OpsAlarmDataService opsAlarmDataService;


    /**
     * 查询告警数据
     *
     * @param param 分页参数
     * @return OpsAlarmDataPageResult 告警数据
     */
    @GetMapping
    public Rest<PageResult<OpsAlarmDataPageResult>> getAlarmDataPage(OpsAlarmDataPageForm param) {
        return opsAlarmDataService.getAlarmDataPage(param);
    }

    /**
     * 清除告警数据
     *
     * @param alarmDataIds 告警数据id
     * @return true 成功 false 失败
     */
    @DeleteMapping("/clear")
    @CustomerActionLog(Type = ActionLogTypeEnum.DELETE_ALARM_DATA)
    public Rest<Boolean> clearAlarmData(
            @RequestBody @Validated @HorizontalAuth(table = "monitor_alarm_data", ignoreUserScope = true) List<String> alarmDataIds) {
        return opsAlarmDataService.clearAlarmData(alarmDataIds);
    }

    /**
     * 查询告警内容
     *
     * @param id 告警数据id
     * @return String 告警内容
     */
    @GetMapping("/{id}/contents")
    public Rest<String> getAlarmContent(@PathVariable("id") @Validated
                                        @HorizontalAuth(table = "monitor_alarm_data") String id) {
        return opsAlarmDataService.getAlarmContent(id);
    }

    /**
     * 处理告警数据
     *
     * @param processingAlarmForm 处理告警数据请求参数
     * @return true 成功 false 失败
     */
    @PutMapping("/processing")
    @CustomerActionLog(Type = ActionLogTypeEnum.PROCESS_ALARM_DATA)
    public Rest<Boolean> processingAlarmData(@RequestBody @Validated OpsProcessingAlarmForm processingAlarmForm) {
        return opsAlarmDataService.processingAlarmData(processingAlarmForm);
    }

    /**
     * 查询告警详情
     *
     * @param id 告警数据id
     * @return OpsAlarmDetailsResult 告警数据详情
     */
    @GetMapping("/{id}")
    public Rest<OpsAlarmDetailsResult> get(@PathVariable("id") @Validated
                                           @HorizontalAuth(table = "monitor_alarm_data") String id) {
        return opsAlarmDataService.get(id);
    }

    /**
     * 查询历史告警
     *
     * @param historicalAlarmPageForm 告警数据分页参数
     * @return OpsHistoricalAlarmResult 历史告警数据
     */
    @GetMapping("/historical")
    public Rest<PageResult<OpsHistoricalAlarmResult>> pageHistorical(@Validated OpsHistoricalAlarmPageForm historicalAlarmPageForm) {
        return opsAlarmDataService.pageHistorical(historicalAlarmPageForm);
    }

    /**
     * 查询告警通知记录
     *
     * @param form 告警规则id
     * @return OpsAlarmNotificationResult 告警通知记录
     */
    @GetMapping("/notify/{id}")
    public Rest<PageResult<OpsAlarmNotificationResult>> pageNotification(@PathVariable("id")
                                                                         @HorizontalAuth(table = "monitor_alarm_data",
                                                                                 ignoreUserScope = true) Long id,
                                                                         OpsAlarmNotificationPageForm form) {
        return opsAlarmDataService.pageNotification(id, form);
    }

    /**
     * 获取告警通知记录
     *
     * @param form 条件
     * @return 告警通知记录
     */
    @GetMapping("/notify/detail/{id}")
    public Rest<PageResult<OpsAlarmNotificationDetailsResult>> pageNotificationDetail(@PathVariable("id")
                                                                                      @HorizontalAuth(
                                                                                              table = "monitor_alarm_data",
                                                                                              ignoreUserScope = true)
                                                                                      Long id,
                                                                                      @Validated
                                                                                      OpsAlarmNotificationDetailsPageForm form) {
        return opsAlarmDataService.pageNotificationDetail(id, form);
    }

    /**
     * 查询通知记录详情
     */
    @GetMapping("/notify/detail/message/{id}")
    public Rest<String> pageNotificationDetailInfo(@PathVariable("id")
                                                       @HorizontalAuth(table = "monitor_alarm_data",
                                                               ignoreUserScope = true) Long id,
                                                   @Validated @NotNull Long logDetailsId) {
        return opsAlarmDataService.pageNotificationDetailInfo(id, logDetailsId);
    }

    /**
     * 导出告警数据
     *
     * @param opsAlarmDataPageForm 导出告警数据条件
     * @return
     */
    @GetMapping("/export")
    @CustomerActionLog(Type = ActionLogTypeEnum.EXPORT_ALARM_DATA)
    public Rest<Void> exportAlarmData(OpsAlarmDataPageForm opsAlarmDataPageForm, HttpServletResponse response) {
        return opsAlarmDataService.exportAlarmData(opsAlarmDataPageForm, response);
    }
}
