package com.cloudstar.controller.alarm;


import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.sdk.monitor.form.MorAlarmDataOriginGetListForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmDataOriginCreateForm;
import com.cloudstar.sdk.monitor.result.MorAlarmDataOriginGetListResult;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.OpsAlarmDataOriginService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/alarm/data/origin")
public class OpsAlarmDataOriginController {

    private final OpsAlarmDataOriginService alarmDataOriginService;

    /**
     * 添加原生告警数据[内部]
     *
     * @param source 来源
     * @param forms  参数
     * @return true 添加成功 false 添加失败
     */
    @PostMapping("/{source}")
    public Rest<Boolean> createByUpdateAlarmDataOrigin(@PathVariable("source") @NotBlank String source,
                                                       @RequestBody @Validated @NotEmpty List<OpsAlarmDataOriginCreateForm> forms) {
        return alarmDataOriginService.createByUpdateAlarmDataOrigin(forms, source);
    }

    /**
     * 查询原始告警明细列表[内部]
     *
     * @param form 条件参数
     * @return MorAlarmDataOriginGetListResult 列表返回数据
     */
    @GetMapping
    public Rest<PageResult<MorAlarmDataOriginGetListResult>> getPageList(MorAlarmDataOriginGetListForm form) {
        return alarmDataOriginService.getPageList(form);
    }

    /**
     * 查询原始告警明细内容[内部]
     *
     * @param id 告警数据id
     * @return String 告警内容
     */
    @GetMapping("/{id}/contents")
    public Rest<String> getContent(@PathVariable("id") Long id) {
        return alarmDataOriginService.getContent(id);
    }

    /**
     * 导出原始告警明细[内部]
     *
     * @return Long 任务id
     */
    @PostMapping("/export")
    @CustomerActionLog(Type = ActionLogTypeEnum.EXPORT_ALARM_ORIGIN)
    public Rest<Long> export(MorAlarmDataOriginGetListForm form) {
        return alarmDataOriginService.export(form);
    }
}
