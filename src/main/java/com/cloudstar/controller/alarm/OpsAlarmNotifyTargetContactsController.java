package com.cloudstar.controller.alarm;


import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.sdk.monitor.form.OpsAlarmContactsCreateForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmContactsListQueryForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmContactsPageQueryForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmContactsUpdateForm;
import com.cloudstar.sdk.monitor.form.OpsAlarmContactsUserCheckForm;
import com.cloudstar.sdk.monitor.form.OpsNotifyTargetContactUserPageForm;
import com.cloudstar.sdk.monitor.annotations.HorizontalAuth;
import com.cloudstar.sdk.monitor.result.OpsAlarmContactInfoResult;
import com.cloudstar.sdk.monitor.result.OpsAlarmContactsListResult;
import com.cloudstar.sdk.monitor.result.OpsAlarmContactsResult;
import com.cloudstar.sdk.monitor.result.OpsNotifyTargetContactUserPageResult;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.OpsAlarmNotifyTargetContactsService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/alarm/notification/contacts")
public class OpsAlarmNotifyTargetContactsController {

    private final OpsAlarmNotifyTargetContactsService contactsService;

    /**
     * 分页查询告警通知联系人列表
     *
     * @param opsAlarmContactsPageQueryForm 告警联系人查询条件
     * @return OpsAlarmContactsResult 告警联系人数据
     */
    @GetMapping
    public Rest<PageResult<OpsAlarmContactsResult>> getAlarmContactsPage(OpsAlarmContactsPageQueryForm opsAlarmContactsPageQueryForm) {
        return contactsService.getAlarmContactsPage(opsAlarmContactsPageQueryForm);
    }

    /**
     * 查询告警通知联系人列表
     *
     * @param form 告警联系人查询条件
     * @return OpsAlarmContactsResult 告警联系人数据
     */
    @GetMapping("/list")
    public Rest<List<OpsAlarmContactsListResult>> getAlarmContactsList(OpsAlarmContactsListQueryForm form) {
        return contactsService.getAlarmContactsList(form);
    }

    /**
     * 查询告警联系人信息
     *
     * @param id 告警联系人id
     * @return OpsAlarmContactsInfoResult 告警联系人信息
     */
    @GetMapping("/{id}")
    public Rest<OpsAlarmContactInfoResult> getDetails(@PathVariable("id")
                                                      @Validated @HorizontalAuth(table = "monitor_notify_target_contacts",
            ignoreProjectFilter = true) Long id) {
        return contactsService.getDetails(id);
    }

    /**
     * 查询告警通知联系人手机号
     *
     * @param id 告警联系人id
     * @return String 告警联系人手机号
     */
    @GetMapping("/phone/{id}")
    public Rest<String> getAlarmContactsPhone(@PathVariable("id")
                                              @HorizontalAuth(table = "monitor_notify_target_contacts",
                                                      ignoreProjectFilter = true, ignoreUserScope = true) Long id) {
        return contactsService.getAlarmContactsPhone(id);
    }

    /**
     * 查询告警通知联系人邮箱
     *
     * @param id 告警联系人id
     * @return String 告警联系人邮箱
     */
    @GetMapping("/email/{id}")
    public Rest<String> getAlarmContactsEmail(@PathVariable("id") @Validated
                                              @HorizontalAuth(table = "monitor_notify_target_contacts",
                                                      ignoreProjectFilter = true) Long id) {
        return contactsService.getAlarmContactsEmail(id);
    }

    /**
     * 查询告警通知联系人名称
     *
     * @param id 告警联系人id
     * @return String 告警联系人邮箱
     */
    @GetMapping("/name/{id}")
    public Rest<String> getAlarmContactsName(@PathVariable("id")
                                             @HorizontalAuth(table = "monitor_notify_target_contacts",
                                                     ignoreProjectFilter = true) Long id) {
        return contactsService.getAlarmContactsName(id);
    }

    /**
     * 创建告警通知联系人
     *
     * @param alarmContactsForm 告警联系人数据
     * @return Boolean true 成功 false 失败
     */
    @PostMapping
    @CustomerActionLog(Type = ActionLogTypeEnum.CREATE_NOTIFY_CONTACTS)
    public Rest<Long> createAlarmContacts(@RequestBody @Validated OpsAlarmContactsCreateForm alarmContactsForm) {
        return contactsService.createAlarmContacts(alarmContactsForm);
    }

    /**
     * 添加已有用户
     *
     * @param userIds 用户id
     * @return Boolean true 成功 false 失败
     */
    @PostMapping("/user")
    @CustomerActionLog(Type = ActionLogTypeEnum.ADD_USER_NOTIFY_CONTACTS)
    public Rest<Boolean> createAlarmContactsExistUser(@RequestBody @Validated List<Long> userIds) {
        return contactsService.createAlarmContactsExistUser(userIds);
    }

    /**
     * 编辑告警通知联系人
     *
     * @param alarmContactsForm 设置告警联系人数据
     * @return Boolean true 成功 false 失败
     */
    @PutMapping
    @CustomerActionLog(Type = ActionLogTypeEnum.UPDATE_NOTIFY_CONTACTS)
    public Rest<Boolean> updateAlarmContacts(@RequestBody @Validated OpsAlarmContactsUpdateForm alarmContactsForm) {
        return contactsService.updateAlarmContacts(alarmContactsForm);
    }

    /**
     * 删除告警通知联系人
     *
     * @param id 告警联系人id
     * @return Boolean true 成功 false 失败
     */
    @DeleteMapping("/{id}")
    @CustomerActionLog(Type = ActionLogTypeEnum.DELETE_NOTIFY_CONTACTS)
    public Rest<Boolean> deleteAlarmContacts(
            @PathVariable("id") @Validated @NotNull @HorizontalAuth(table = "monitor_notify_target_contacts",
                    ignoreUserScope = true, ignoreProjectFilter = true) Long id) {
        return contactsService.deleteAlarmContacts(id);
    }

    /**
     * 查询已有用户
     *
     * @param form 查询参数
     * @return OpsNotifyTargetContactUserPageResult 告警通知联系人用户列表
     */
    @GetMapping("/user")
    public Rest<PageResult<OpsNotifyTargetContactUserPageResult>> getAlarmContactsUserPage(OpsNotifyTargetContactUserPageForm form) {
        return contactsService.getAlarmContactsUserPage(form);
    }

    /**
     * 校验告警联系人信息
     *
     * @param form 校验参数
     * @return Boolean 校验结果
     */
    @GetMapping("/check")
    public Rest<Boolean> checkAlarmContactsUser(@Validated OpsAlarmContactsUserCheckForm form) {
        return contactsService.checkAlarmContactsUser(form);
    }

}
