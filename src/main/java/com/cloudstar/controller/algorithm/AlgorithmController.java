package com.cloudstar.controller.algorithm;

import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.algorithm.AlgorithmService;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmAddReq;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmBssPageReq;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmOperateReq;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmPageReq;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmUpdateReq;
import com.cloudstar.service.pojo.vo.responsevo.algorithm.AlgorithmBssPageResp;
import com.cloudstar.service.pojo.vo.responsevo.algorithm.AlgorithmDetailResp;
import com.cloudstar.service.pojo.vo.responsevo.algorithm.AlgorithmListResp;
import com.cloudstar.service.pojo.vo.responsevo.algorithm.AlgorithmPageResp;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 算法管理
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@RequestMapping("/algorithm")
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlgorithmController {


    AlgorithmService algorithmService;

    /**
     * 算法分页列表
     *
     * @param req 条件
     *
     * @return {@link Rest}<{@link PageResult}<{@link AlgorithmPageResp}>>
     */
    @GetMapping
    public Rest<PageResult<AlgorithmPageResp>> getAlgorithmPage(AlgorithmPageReq req, PageForm pageForm) {
        return Rest.ok(PageResult.of(algorithmService.getPage(req, pageForm)));
    }

    /**
     * 算法列表
     *
     * @return {@link Rest}<{@link PageResult}<{@link AlgorithmListResp}>>
     */
    @GetMapping("/list")
    public Rest<List<AlgorithmListResp>> getAlgorithmList(AlgorithmPageReq req) {
        return Rest.ok(algorithmService.getList(req));
    }


    /**
     * 创建算法
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CREATE_ALGORITHM)
    @PostMapping
    public Rest addAlgorithm(@Valid @RequestBody AlgorithmAddReq req) {
        algorithmService.addAlgorithm(req);
        return Rest.ok();
    }

    /**
     * 删除算法
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.DELETE_ALGORITHM)
    @DeleteMapping("/{id}")
    public Rest<Boolean> deleteAlgorithm(@PathVariable Long id) {
        return Rest.ok(algorithmService.removeById(id));
    }

    /**
     * 修改算法
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.UPDATE_ALGORITHM)
    @PutMapping
    public Rest updateAlgorithm(@Valid @RequestBody AlgorithmUpdateReq req) {
        algorithmService.updateAlgorithm(req);
        return Rest.ok();
    }

    /**
     * 查询详情
     */
    @GetMapping("/{id}")
    public Rest<AlgorithmDetailResp> getAlgorithmById(@PathVariable Long id) {
        return Rest.ok(algorithmService.getAlgorithmById(id));
    }


    /**
     * 发布算法 发布成功返回发布表ID
     */
    @PostMapping("/bss/publish")
    public Rest<Long> publish(@Valid @RequestBody AlgorithmOperateReq req) {
        return Rest.ok(algorithmService.publish(req));
    }

    /**
     * 订阅算法
     */
    @PostMapping("/bss/subscribe")
    public Rest<Boolean> subscribe(@Valid @RequestBody AlgorithmOperateReq req) {
        return Rest.ok(algorithmService.subscribe(req));
    }

    /**
     * 退订算法
     */
    @PostMapping("/bss/unsubscribe")
    public Rest<Boolean> unsubscribe(@Valid @RequestBody AlgorithmOperateReq req) {
        return Rest.ok(algorithmService.unsubscribe(req));
    }


    /**
     * 运营平台调分页查询
     *
     * @param req 条件
     */
    @GetMapping("/bss/page")
    public Rest<PageResult<AlgorithmBssPageResp>> getAlgorithmPageByBss(AlgorithmBssPageReq req, PageForm pageForm) {
        return Rest.ok(PageResult.of(algorithmService.getBssPage(req, pageForm)));
    }


    /**
     * 根据算法发布id 查询算法详情
     */
    @GetMapping("/publish/{id}")
    public Rest<AlgorithmDetailResp> getAlgorithmByPublishId(@PathVariable Long id) {
        return Rest.ok(algorithmService.getAlgorithmById(id));
    }


}
