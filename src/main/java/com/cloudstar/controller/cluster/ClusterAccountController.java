package com.cloudstar.controller.cluster;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.sdk.server.pojo.BucketUserInfoDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.cluster.ClusterAccountService;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryClusterAccountReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterAccountResp;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;


/**
 * 集群账号表 (cluster_account)表控制层
 *
 * <AUTHOR> scx
 * @date : 2022-7-15
 */
@RestController
@RequestMapping("/cluster_account")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ClusterAccountController {

    ClusterAccountService clusterAccountService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     *
     * @return 实例对象
     */
    @GetMapping("/{id}")
    public Rest<ClusterAccountResp> queryById(@PathVariable Long id) {
        return Rest.ok(clusterAccountService.queryById(id));
    }

    /**
     * 分页查询
     *
     * @param req 请求入参
     * @param pageForm 分页参数
     *
     * @return 查询结果
     */
    @GetMapping
    public Rest<PageResult<ClusterAccountResp>> page(@Validated QueryClusterAccountReq req, PageForm pageForm) {
        return Rest.ok(clusterAccountService.page(req, pageForm));
    }

    /**
     * 获取集群子账户
     *
     * @param clusterSubAccountReqDto 集群子帐户要求dto
     *
     * @return {@link ClusterSubAccountRespDto}
     */
    @GetMapping("/cluster_sub_account")
    public Rest<ClusterSubAccountRespDto> getClusterSubAccount(ClusterSubAccountReqDto clusterSubAccountReqDto) {

        return Rest.ok(clusterAccountService.getClusterSubAccount(clusterSubAccountReqDto));
    }


    /**
     * 根据账户 UUID 列表获取共享用户信息
     *
     * @param accountUuids 一系列账户的 UUID 列表，用于查询共享用户信息
     *
     * @return 用户信息（包含account_uuid）
     */
    @GetMapping("/bucket_shared_user_info")
    public Rest<List<BucketUserInfoDto>> getBucketSharedUserInfo(@RequestParam List<String> accountUuids) {
        return Rest.ok(clusterAccountService.getBucketSharedUserInfo(accountUuids));
    }


    /**
     * 用户通过账户ID或者账户名称精准查询用户。
     *
     * @param uuid 用户的账户 UUID，唯一标识用户
     * @param account 用户的账户名称，用于精确匹配
     * @param clusterId 集群的唯一标识符，用于指定查询范围
     *
     * @return 用户信息（包含account_uuid）
     */
    @GetMapping("/bucket_find_account_info")
    public Rest<List<BucketUserInfoDto>> getBucketUserAccountInfo(
            @RequestParam(required = false) String uuid,
            @RequestParam(required = false) String account,
            @RequestParam(required = true) Long clusterId) {
        return Rest.ok(clusterAccountService.getUserDetails(uuid, account, clusterId));
    }


}
