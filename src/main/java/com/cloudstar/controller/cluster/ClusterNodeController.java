package com.cloudstar.controller.cluster;

import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.sdk.server.pojo.ClusterEngineResDto;
import com.cloudstar.service.facade.cluster.ClusterNodeService;
import com.cloudstar.service.pojo.vo.requestvo.cluster.ClusterNodeInfoReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryClusterNodeListReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryClusterNodeReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryNodeContainerReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.UpdateComputeProductNameReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.UpdateNodePurposeReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterNodeContainerResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterNodeInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterNodeResp;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;



/**
 * 集群节点控制器
 *
 * <AUTHOR>
 * @date 2022/09/28
 */
@RestController
@RequestMapping("/cluster_node")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ClusterNodeController {

    ClusterNodeService clusterNodeService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @GetMapping("/{id}")
    public Rest<ClusterNodeInfoResp> queryById(@PathVariable Long id) {
        return Rest.ok(clusterNodeService.queryById(id));
    }

    /**
     * 分页查询
     *
     * @param req      筛选条件
     * @param pageForm 分页对象
     * @return 查询结果
     */
    @GetMapping
    public Rest<PageResult<ClusterNodeResp>> page(QueryClusterNodeReq req,
                                                  PageForm pageForm) {
        return Rest.ok(clusterNodeService.page(req, pageForm));
    }

    /**
     * 列表查询
     *
     * @param req      筛选条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public Rest<List<ClusterNodeResp>> list(QueryClusterNodeListReq req) {
        return Rest.ok(clusterNodeService.list(req));
    }

    /**
     * 创建集群节点
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_NODE_CREAT)
    @PostMapping("")
    public Rest createNode(@Valid @RequestBody ClusterNodeInfoReq req) {
        return Rest.ok(clusterNodeService.createNode(req));
    }

    /**
     * 修改集群节点
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_NODE_UPDATE)
    @PostMapping("/update")
    public Rest<Boolean> updateNode(@Valid @RequestBody ClusterNodeInfoReq req) {
        return Rest.ok(clusterNodeService.updateNode(req));
    }

    /**
     * 删除集群节点
     *
     * @return {@link Rest}<{@link ClusterEngineResDto}>
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_NODE_DELETE)
    @PostMapping("/delete/{id}")
    public Rest deleteNode(@PathVariable Long id) {
        return Rest.ok(clusterNodeService.deleteNode(id));
    }

    /**
     * 批量设置计算卡型号及其计算卡数量、是否虚拟化
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_NODE_UPDATE)
    @PostMapping("/update_compute_productName")
    public Rest<Boolean> updateComputeProductName(@RequestBody UpdateComputeProductNameReq req) {
        return Rest.ok(clusterNodeService.updateComputeProductNameByIds(req));
    }

    /**
     * 批量设置节点用途
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_NODE_UPDATE)
    @PostMapping("/update_node_purpose")
    public Rest<Boolean> updateNodePurpose(@RequestBody UpdateNodePurposeReq req) {
        return Rest.ok(clusterNodeService.updateNodePurseByIds(req.getIds(), req.getNodePurpose()));
    }


    /**
     * 发送节点驱逐请求
     *
     * @param nodeName 节点名称
     *
     * @return 操作结果
     */
    @PostMapping("/evict_node")
    public Rest<Boolean> evictNode(@RequestParam String nodeName) {
        return Rest.ok(clusterNodeService.evictNode(nodeName));
    }

    /**
     * 发送节点恢复请求
     *
     * @param nodeName 节点名称
     *
     * @return 操作结果
     */
    @PostMapping("/recover_node")
    public Rest<Boolean> recoverNode(@RequestParam String nodeName) {
        return Rest.ok(clusterNodeService.recoverNode(nodeName));
    }

    /**
     * 节点中容器的详情列表
     */
    @GetMapping("/node_container")
    public Rest<List<ClusterNodeContainerResp>> queryNodeContainer(QueryNodeContainerReq req) {
        return Rest.ok(clusterNodeService.queryNodeContainer(req));
    }

    /**
     * 节点监控
     */
    @GetMapping("/node_monitor")
    public Rest<String> getGrafanaUrl(@RequestParam String nodeName,
                                      @RequestParam Long clusterId) {
        return Rest.ok(clusterNodeService.getUrl(nodeName, clusterId));
    }

    /**
     * 作业监控
     */
    @GetMapping("/job_monitor")
    public Rest<String> getJobMonitorUrl(@RequestParam String nodeName,
                                         @RequestParam String containerId,
                                         @RequestParam Long clusterId) {
        return Rest.ok(clusterNodeService.getJobMonitorUrl(nodeName, containerId, clusterId));
    }
}
