package com.cloudstar.controller.datastorage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.integration.bss.pojo.RestResult;
import com.cloudstar.integration.bss.pojo.algorithm.MarketSubPageResp;
import com.cloudstar.integration.bss.service.facade.AiMarketAlgorithmService;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.BucketUserInfoDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.sdk.server.pojo.UserStorageResourceRes;
import com.cloudstar.service.facade.datastorage.DataStorageResourcesService;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmSubPageReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageBssPageReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageClusterFlavorReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageOperateReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageResourcesFeignPageReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageResourcesPageReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageResourcesSaveReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DataStorageResourcesUpdateReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.QueryDataStorageReq;
import com.cloudstar.service.pojo.vo.requestvo.obs.ObsBucketPolicyReq;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageBssPageResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageClusterEngineResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageClusterEntityResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageClusterFlavorResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageResourcePageResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.DataStorageResourceResp;

import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.validation.Valid;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据存储资源控制器
 *
 * <AUTHOR>
 * @date 2022/8/19 14:14
 */
@RestController
@RequestMapping("/data_storage_resources")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DataStorageResourceController {

    DataStorageResourcesService dataStorageResourcesService;

    ServerClient serverClient;

    ScheduleClient scheduleClient;
    AiMarketAlgorithmService aiMarketAlgorithmService;

    /**
     * 获取存储资源列表
     *
     * @param req 要求事情
     *
     * @return {@link Rest}<{@link PageResult}<{@link DataStorageResourcePageResp}>>
     */
    @GetMapping
    public Rest<PageResult<DataStorageResourcePageResp>> getStorageResourcesList(DataStorageResourcesPageReq req) {
        return Rest.ok(dataStorageResourcesService.getStorageResourcesList(req));
    }

    @GetMapping("/feign")
    public Rest<PageResult<DataStorageResourcePageResp>> getStorageResourcesList(DataStorageResourcesFeignPageReq req) {
        return Rest.ok(dataStorageResourcesService.getStorageResourcesList(req));
    }

    /**
     * 通过id查询文件资源
     *
     * @param id id
     *
     * @return {@link Rest}<{@link DataStorageResourceResp}>
     */
    @GetMapping("/{id}")
    public Rest<DataStorageResourceResp> queryById(@PathVariable Long id) {

        return Rest.ok(dataStorageResourcesService.queryById(id));
    }

    /**
     * 通过id删除文件资源
     *
     * @param id id
     *
     * @return {@link Rest}<{@link Boolean}>
     */
    @PostMapping("/delete/{id}")
    @CustomerActionLog(Type = ActionLogTypeEnum.DELETE_DATA_STORAGE)
    public Rest<Boolean> deleteById(@PathVariable Long id) {
        dataStorageResourcesService.deleteById(id);
        return Rest.ok(Boolean.TRUE);
    }


    /**
     * 创建数据资源
     *
     * @param req 要求事情
     *
     * @return {@link Rest}<{@link String}>
     */
    @PostMapping
    @CustomerActionLog(Type = ActionLogTypeEnum.CREAT_DATA_STORAGE)
    public Rest<String> add(@RequestBody @Valid DataStorageResourcesSaveReq req) {
        return Rest.ok(dataStorageResourcesService.insert(req));
    }


    /**
     * 更新数据存储
     *
     * @param req 要求事情
     *
     * @return {@link Rest}
     */
    @PostMapping("/update")
    @CustomerActionLog(Type = ActionLogTypeEnum.UPDATE_DATA_STORAGE)
    public Rest updateDataStorage(@RequestBody @Valid DataStorageResourcesUpdateReq req) {
        dataStorageResourcesService.updateDataStorage(req);
        return Rest.ok();
    }


    /**
     * 获取集群引擎
     *
     * @return {@link Rest}<{@link DataStorageClusterEngineResp}>
     */
    @GetMapping("/cluster_engine")
    public Rest<List<DataStorageClusterEngineResp>> getClusterEngine() {
        return dataStorageResourcesService.getClusterEngine();
    }


    /**
     * 获取算力资源类型
     *
     * @param req 要求事情
     *
     * @return {@link Rest}<{@link List}<{@link DataStorageClusterFlavorResp}>>
     */
    @GetMapping("/cluster_flavor")
    public Rest<List<DataStorageClusterFlavorResp>> getClusterFlavor(DataStorageClusterFlavorReq req) {
        return dataStorageResourcesService.getClusterFlavor(req);
    }


    /**
     * 获取集群实体
     *
     * @return {@link Rest}<{@link DataStorageClusterEntityResp}>
     */
    @GetMapping("/cluster_entity")
    public Rest<List<DataStorageClusterEntityResp>> getClusterEntity() {
        return dataStorageResourcesService.getClusterEntity();
    }

    /**
     * 根据类型查询数据集
     *
     * @return 查询结果
     */
    @GetMapping("/find_by_category")
    public Rest findAllByCategory(@Valid QueryDataStorageReq req) {
        return Rest.ok(dataStorageResourcesService.findAllByCategory(req));
    }

    /**
     * 总览资源统计
     *
     * @return 查询结果
     */
    @GetMapping("/overview/usage_info")
    public Rest<UserStorageResourceRes> usageInfo() {
        return serverClient.usageInfo();
    }

    /**
     * 获取集群信息
     */
    private ClusterSubAccountRespDto getClusterSubAccountRespDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = getClusterSubAccountReqDto(clusterId);
        ClusterSubAccountRespDto data = serverClient.getClusterSubAccount(clusterSubAccountReqDto).getData();
        data.setAccessKey(CrytoUtilSimple.decrypt(data.getAccessKey()));
        data.setSecretKey(CrytoUtilSimple.decrypt(data.getSecretKey()));
        return data;
    }

    /**
     * 根据集群id获取当前登录用户的集群子账户
     *
     * @param clusterId clusterId
     *
     * @return {@link ClusterSubAccountReqDto}
     *
     * <AUTHOR>
     * @date 2022/10/27
     **/
    private ClusterSubAccountReqDto getClusterSubAccountReqDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();

        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = Optional.ofNullable(authUser)
                               .map(AuthUser::getUserSid)
                               .orElseThrow(() -> new BizException(BizErrorEnum.MSG_1011_TOKEN_ERROR));
        clusterSubAccountReqDto.setUserSid(userSid);
        clusterSubAccountReqDto.setClusterId(clusterId);

        return clusterSubAccountReqDto;
    }

    /**
     * 设置桶策略。
     *
     * @param req 请求对象，包含集群ID、账户UUID和文件目录等信息
     *
     * @return 返回操作结果，包含成功与否
     */
    @PostMapping("/setBucketPolicy")
    public Rest<Boolean> setBucketPolicy(@RequestBody ObsBucketPolicyReq req) {
        Long clusterId = req.getClusterId();
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(clusterId);
        boolean result = dataStorageResourcesService.setBucketPolicy(
                clusterId,
                clusterSubAccount,
                req.getAccountUuid(),
                req.getFileDir()
        );
        return Rest.ok(result);
    }

    /**
     * 删除桶策略。
     *
     * @param req 请求对象，包含集群ID、账户UUID和文件目录等信息
     *
     * @return 返回操作结果，包含成功与否
     */
    @PostMapping("/deleteBucketPolicy")
    public Rest<Boolean> deleteBucketPolicy(@RequestBody ObsBucketPolicyReq req) {
        Long clusterId = req.getClusterId();
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(clusterId);
        boolean result = dataStorageResourcesService.deleteBucketPolicy(
                clusterId,
                clusterSubAccount,
                req.getAccountUuid(),
                req.getFileDir()
        );
        return Rest.ok(result);
    }

    /**
     * 获取IAM用户策略共享过的用户数据
     *
     * @param req 请求
     *
     * @return 用户信息列表
     */
    @PostMapping("/bucketPolicyDataSharing")
    public Rest<List<BucketUserInfoDto>> shareBucketPolicyData(@RequestBody ObsBucketPolicyReq req) {
        if (req == null) {
            throw new BizException("请求参数不能为空");
        }
        if (ObjectUtils.isEmpty(req.getFileDir())) {
            throw new BizException("文件目录不能为空");
        }
        if (ObjectUtils.isEmpty(req.getClusterId())) {
            throw new BizException("集群ID不能为空");
        }
        List<BucketUserInfoDto> userInfoList = scheduleClient.bucketPolicyData(req.getFileDir(), req.getClusterId())
                                                             .getData();
        return Rest.ok(userInfoList);
    }

    /**
     * 获取指定用户账户信息
     */
    @GetMapping("/findUserAccountInfo")
    public Rest<List<BucketUserInfoDto>> findUserAccountInfo(
            @RequestParam(required = false) String uuid,
            @RequestParam(required = false) String account,
            @RequestParam(required = true) Long clusterId) {
        // 获取用户账户信息
        List<BucketUserInfoDto> userInfoList = serverClient.getBucketUserAccountInfo(uuid, account, clusterId)
                                                           .getData();
        return Rest.ok(userInfoList);
    }


    /**
     * 发布算法 发布成功返回发布表ID
     */
    @PostMapping("/bss/publish")
    public Rest<Long> publish(@Valid @RequestBody DataStorageOperateReq req) {
        return Rest.ok(dataStorageResourcesService.publish(req));
    }

    /**
     * 订阅算法
     */
    @PostMapping("/bss/subscribe")
    public Rest<Boolean> subscribe(@Valid @RequestBody DataStorageOperateReq req) {
        return Rest.ok(dataStorageResourcesService.subscribe(req));
    }

    /**
     * 退订算法
     */
    @PostMapping("/bss/unsubscribe")
    public Rest<Boolean> unsubscribe(@Valid @RequestBody DataStorageOperateReq req) {
        return Rest.ok(dataStorageResourcesService.unsubscribe(req));
    }


    /**
     * 运营平台调分页查询
     *
     * @param req 条件
     */
    @GetMapping("/bss/page")
    public Rest<PageResult<DataStorageBssPageResp>> getAlgorithmPageByBss(DataStorageBssPageReq req, PageForm pageForm) {
        return Rest.ok(PageResult.of(dataStorageResourcesService.getBssPage(req, pageForm)));
    }

    /**
     * 数据集我的订阅
     *
     * @param req aa
     * @param pageForm a
     *
     * @return a
     */
    @GetMapping("/my/page")
    public Rest myAlgorithmPage(AlgorithmSubPageReq req, PageForm pageForm) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("未获取到用户信息");
        }
        final RestResult result = aiMarketAlgorithmService.getAlgorithmSubscribePage(authUser.getAccount(), req.getName(),
                                                                                     pageForm.getPageNo(), pageForm.getPageSize(), 3);
        int totalRows = result.getTotalRows();
        List<MarketSubPageResp> respList = new ArrayList<>();
        final List list = result.dataToList(MarketSubPageResp.class);
        if (CollectionUtil.isNotEmpty(list)) {
            respList.addAll(list);
        }
        Page<MarketSubPageResp> page = new Page<>();
        page.setTotal(totalRows);
        page.setRecords(respList);
        page.setCurrent(pageForm.getPageNo());
        page.setSize(pageForm.getPageSize());
        return Rest.ok(PageResult.of(page));
    }
}
