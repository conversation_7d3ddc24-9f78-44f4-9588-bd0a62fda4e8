package com.cloudstar.controller.higress;


import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.service.facade.higress.HigressService;
import com.cloudstar.service.pojo.vo.requestvo.higress.HigressLoginReq;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequestMapping("/higress")
public class HigressController {

    HigressService higressService;


    // 登录
    @PostMapping("login")
    public Rest higressLogin(@RequestBody HigressLoginReq req) {
        return Rest.ok(higressService.post("/session/login", req, HigressLoginReq.class));
    }

}
