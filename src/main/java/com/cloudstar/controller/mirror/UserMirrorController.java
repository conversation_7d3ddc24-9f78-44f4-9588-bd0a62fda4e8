package com.cloudstar.controller.mirror;

import com.cloudstar.common.base.pojo.mq.MirrorBuildMessage;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.mirror.UserMirrorService;
import com.cloudstar.service.pojo.vo.responsevo.mirror.CopyResponse;
import com.cloudstar.service.pojo.vo.responsevo.mirror.MirrorDetailResponse;
import com.cloudstar.service.pojo.vo.responsevo.mirror.UserMirrorResponse;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户镜像controller
 *
 * <AUTHOR>
 * @Date 2024/10/30 10:31
 */
@RestController
@RequestMapping("/images")
public class UserMirrorController {

    @Autowired
    private UserMirrorService userMirrorService;

    /**
     * 镜像生成
     * @param message 镜像参数以及名称
     * @return 布尔值判断是否执行成功
     */
    @PostMapping("/generateImage")
    public Rest createImage(@RequestBody MirrorBuildMessage message) {
        Boolean result = userMirrorService.generateImages(message);
        return Rest.ok(result);
    }

    /**
     * 分页查询镜像构建
     * @param pageNo 分页参数
     * @param pageSize 分页参数
     * @return 镜像构建列表
     */
    @GetMapping("/listMirror")
    public Rest<PageResult<UserMirrorResponse>> getUserMirror(@Param("mirrorName") String mirrorName,
                                                              @Param("pageNo") int pageNo,
                                                              @Param("pageSize") int pageSize) {
        PageResult<UserMirrorResponse> page = userMirrorService.page(mirrorName, pageNo, pageSize);
        return Rest.ok(page);
    }

    /**
     * 删除镜像构建记录
     * @param mirrorId 镜像id
     * @return 布尔值表示删除结果
     */
    @DeleteMapping("/{mirrorId}")
    public Rest removeMirror(@PathVariable Long mirrorId) {
        boolean result = userMirrorService.deleteMirror(mirrorId);
        return Rest.ok(result);
    }

    /**
     * 复制出一个基础镜像执行构建
     * @param mirrorId 镜像id
     * @return 基础镜像四个参数
     */
    @GetMapping("/{mirrorId}")
    public Rest<CopyResponse> copyToGenerate(@PathVariable Long mirrorId) {
        CopyResponse copyResponse = userMirrorService.buildNewMirror(mirrorId);
        return Rest.ok(copyResponse);
    }

    /**
     * 根据镜像名称查询构建日志
     * @param mirrorName 镜像名称
     * @return 日志集合
     */
    @GetMapping("/getLogs/{mirrorName}")
    public Rest<List<String>> queryBuildLogs(@PathVariable String mirrorName) {
        List<String> logs = userMirrorService.queryBuildLogs(mirrorName);
        return Rest.ok(logs);
    }

    /**
     * 获取镜像详情
     * @param mirrorId 镜像id
     * @return 镜像详情响应体
     */
    @GetMapping("/getDetail")
    public Rest<MirrorDetailResponse> getMirrorDetail(@RequestParam Long mirrorId) {
        MirrorDetailResponse detail = userMirrorService.getDetail(mirrorId);
        return Rest.ok(detail);
    }

    /**
     * 获取训练框架
     * @return 训练框架名称集合
     */
    @GetMapping("/frameworks")
    public Rest<List<String>> getFrameworks() {
        return Rest.ok(userMirrorService.getFrameworks());
    }

    /**
     * 获取训练框架对应版本
     * @param framework 训练框架名称
     * @return 版本集合
     */
    @GetMapping("/frameworks/versions")
    public Rest<List<String>> getFrameworkVersions(@RequestParam String framework) {
        return Rest.ok(userMirrorService.getFrameworkVersions(framework));
    }

    /**
     * 获取训练驱动
     * @return 训练驱动名称集合
     */
    @GetMapping("/drivers")
    public Rest<List<String>> getDrivers() {
        return Rest.ok(userMirrorService.getDrivers());
    }

    /**
     * 获取训练驱动对应版本
     * @param driver 驱动名称
     * @return 版本集合
     */
    @GetMapping("/drivers/versions")
    public Rest<List<String>> getDriverVersions(@RequestParam String driver) {
        return Rest.ok(userMirrorService.getDriverVersions(driver));
    }

    /**
     * 获取操作系统
     * @return 操作系统名称集合
     */
    @GetMapping("/os")
    public Rest<List<String>> getOperatingSystems() {
        return Rest.ok(userMirrorService.getOperatingSystems());
    }

    /**
     * 获取操作系统对应版本
     * @param os 操作系统名称
     * @return 版本集合
     */
    @GetMapping("/os/versions")
    public Rest<List<String>> getOsVersions(@RequestParam String os) {
        return Rest.ok(userMirrorService.getOsVersions(os));
    }

    /**
     * 获取平台架构
     * @return 平台机构名称
     */
    @GetMapping("/architectures")
    public Rest<List<String>> getArchitectures() {
        return Rest.ok(userMirrorService.getArchitectures());
    }

    /**
     * 查询镜像构建模板
     * @return Dockerfile文件模板
     */
    @GetMapping("/getDockerfile")
    public Rest<String> getDockerFile() {
        String dockerFileTemplate = userMirrorService.getDockerFileTemplate();
        return Rest.ok(dockerFileTemplate);
    }
}
