package com.cloudstar.controller.monitor;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.MonitorUnusedConsoleResponse;
import com.cloudstar.sdk.server.pojo.MonitorUnusedResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 监控控制器
 *
 * <AUTHOR>
 * @date 2023/05/29
 */
@Slf4j
@RestController
@RequestMapping("monitor")
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class MonitorController {

    ServerClient serverClient;

    ClusterResourcePoolMapper clusterResourcePoolMapper;

    /**
     * 获取资源池水位信息
     *
     * @param clusterId 集群id
     * @param poolId    池id
     * @return {@link Rest}<{@link MonitorUnusedConsoleResponse}>
     */
    @GetMapping("/unused")
    public Rest<MonitorUnusedConsoleResponse> getUnusedMonitor(@RequestParam Long clusterId, @RequestParam String poolId) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException("请登录后访问此接口");
        }
        ClusterResourcePool resourcePool = clusterResourcePoolMapper.selectOne(new LambdaQueryWrapper<ClusterResourcePool>()
                .eq(ClusterResourcePool::getPoolId, poolId));
        if (!resourcePool.getUserIds().contains(authUser.getUserSid() + "")) {
            throw new BizException("请勿越权访问！");
        }
        Rest<MonitorUnusedResponse> mur = serverClient.getUnusedMonitor(clusterId, poolId);
        log.info(JSONUtil.toJsonStr(mur));

        MonitorUnusedConsoleResponse response = new MonitorUnusedConsoleResponse();
        response.setCpuCapacity(mur.getData().getCpuCapacity());
        response.setNpuCapacity(mur.getData().getNpuCapacity());
        response.setGpuCapacity(mur.getData().getGpuCapacity());
        response.setMemoryCapacity(mur.getData().getMemoryCapacity());

        response.setCpu(mur.getData().getCpu());
        response.setNpu(mur.getData().getNpu());
        response.setMemory(mur.getData().getMemory());
        response.setGpu(mur.getData().getGpu());

        response.setPoolId(poolId);
        response.setPoolName(resourcePool.getPoolName());
        log.info(JSONUtil.toJsonStr(response));
        return Rest.ok(response);
    }

}
