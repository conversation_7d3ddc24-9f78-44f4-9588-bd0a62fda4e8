package com.cloudstar.controller.monitor;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.sdk.server.pojo.MonitorUnusedResponse;
import com.cloudstar.service.facade.monitor.MonitorService;
import com.cloudstar.service.pojo.dto.monitor.MonitorTopDto;
import com.cloudstar.service.pojo.vo.responsevo.monitor.MonitorActiveAlarmResponse;
import com.cloudstar.service.pojo.vo.responsevo.monitor.MonitorAlarmNumberResponse;
import com.cloudstar.service.pojo.vo.responsevo.monitor.MonitorResourceResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 监控相关
 *
 * <AUTHOR>
 * @date 2022/10/18 14:21
 */
@RestController
@RequestMapping("/monitor")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class MonitorController {

    MonitorService monitorService;
    /**
     * 获取资源信息
     */
    @GetMapping("/resource")
    public Rest<MonitorResourceResponse> getResource() {
        return Rest.ok(BeanUtil.toBean(monitorService.getResource(), MonitorResourceResponse.class));
    }
    /**
     * 获取告警数量
     */
    @GetMapping("/alarm_num")
    public Rest<MonitorAlarmNumberResponse> getAlarmNumber() {
        return Rest.ok(BeanUtil.toBean(monitorService.getAlarmNumber(), MonitorAlarmNumberResponse.class));
    }
    /**
     * 获取最新活动告警
     */
    @GetMapping("/active_alarm")
    public Rest<List<MonitorActiveAlarmResponse>> getActiveAlarm() {
        return Rest.ok(BeanUtil.copyToList(monitorService.getActiveAlarm(), MonitorActiveAlarmResponse.class));
    }
    /**
     * 获取监控top情况
     *
     * @param timeType 时间类型 now:实时 day：今天 week:本周 month:本月
     */
    @GetMapping("/top")
    public Rest<MonitorTopDto> getMonitorTop(@RequestParam String timeType) {
        return Rest.ok(monitorService.getMonitorTop(timeType));
    }
    /**
     * 获取集群的监控数据
     *
     * @param clusterId 集群id
     * @param poolId 资源池id
     */
    @GetMapping("/unused")
    public Rest<MonitorUnusedResponse> getUnusedMonitor(@RequestParam Long clusterId, @RequestParam String poolId) {
        return Rest.ok(
                BeanUtil.toBean(monitorService.getUnusedMonitor(clusterId, poolId), MonitorUnusedResponse.class));
    }
}
