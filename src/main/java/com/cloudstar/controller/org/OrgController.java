package com.cloudstar.controller.org;

import com.cloudstar.dao.model.user.SysMOrg;
import com.cloudstar.service.facade.user.SysMOrgService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * org控制器
 *
 * <AUTHOR>
 * @date 2022/09/14
 */
@RestController
@RequestMapping("/org")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class OrgController {

    SysMOrgService orgService;

    /**
     * 获取根org
     */
    @GetMapping("/root_org")
    public SysMOrg selectRootOrg(@RequestParam Long orgId) {
        return orgService.selectRootOrg(orgId);
    }

    /**
     * 获取旗下所有org组织
     */
    @GetMapping("/sub_org")
    public SysMOrg selectSubOrgById(@RequestParam Long orgId) {
        return orgService.selectRootOrg(orgId);
    }

}
