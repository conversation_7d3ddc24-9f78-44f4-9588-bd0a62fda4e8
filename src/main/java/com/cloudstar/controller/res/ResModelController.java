package com.cloudstar.controller.res;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.cluster.ClusterEngineFlavorService;
import com.cloudstar.service.facade.res.ResModelService;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelCreateRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelDeleteRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelPageRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelUpdateRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionCreateRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionDeleteRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionDetailRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionStatusRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionUpdateRequest;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterGroupEngineResp;
import com.cloudstar.service.pojo.vo.responsevo.model.MetaModelResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelCreateResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelPageResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelVersionCreateResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelVersionDetailResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.NormalModelResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 模型控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
public class ResModelController {

    @Resource
    private ResModelService resModelService;

    @Resource
    private ClusterEngineFlavorService clusterEngineFlavorService;

    /**
     * 分页查询模型列表
     *
     * @param request 请求体
     *
     * @return 分页数据
     */
    @GetMapping
    public Rest<PageResult<ModelPageResponse>> page(ModelPageRequest request) {
        PageResult<ModelPageResponse> page = resModelService.selectPage(request);
        return Rest.ok(page);
    }

    /**
     * 创建模型
     *
     * @param request 请求体
     *
     * @return ModelCreateResponse
     */
    @PostMapping
    public Rest<ModelCreateResponse> create(@Validated @RequestBody ModelCreateRequest request) {
        ModelCreateResponse response = resModelService.create(request);
        return Rest.ok(response);
    }

    /**
     * 修改模型
     *
     * @param request 请求体
     *
     * @return 是否成功
     */
    @PutMapping
    public Rest<Boolean> update(@Validated @RequestBody ModelUpdateRequest request) {
        Boolean isOk = resModelService.updateModel(request);
        return Rest.ok(isOk);
    }

    /**
     * 删除模型
     *
     * @param request 请求体
     *
     * @return 是否成功
     */
    @DeleteMapping
    public Rest<Boolean> delete(@RequestBody ModelDeleteRequest request) {
        Boolean isOk = resModelService.delete(request);
        return Rest.ok(isOk);
    }

    /**
     * 根据模型id查询版本信息
     *
     * @param request 请求
     *
     * @return ModelVersionDetailResponse
     */
    @GetMapping("/version")
    public Rest<PageResult<ModelVersionDetailResponse>> getVersions(@Validated ModelVersionDetailRequest request) {
        PageResult<ModelVersionDetailResponse> versions = resModelService.getVersions(request);
        return Rest.ok(versions);
    }

    /**
     * 修改模型版本状态
     *
     * @param request 请求体
     *
     * @return 是否成功
     */
    @PutMapping("/version/status")
    public Rest<Boolean> modifyVersionStatus(@Validated @RequestBody ModelVersionStatusRequest request) {
        Boolean isOk = resModelService.modifyVersionStatus(request);
        return Rest.ok(isOk);
    }

    /**
     * 创建模型版本
     *
     * @param request 请求体
     *
     * @return ModelVersionCreateResponse
     */
    @PostMapping("/version")
    public Rest<ModelVersionCreateResponse> createVersion(@Validated @RequestBody ModelVersionCreateRequest request) {
        ModelVersionCreateResponse response = resModelService.createVersion(request);
        return Rest.ok(response);
    }

    /**
     * 修改模型版本
     *
     * @param request 请求体
     *
     * @return 是否成功
     */
    @PutMapping("/version")
    public Rest<Boolean> updateVersion(@Validated @RequestBody ModelVersionUpdateRequest request) {
        Boolean isOk = resModelService.updateVersion(request);
        return Rest.ok(isOk);
    }

    /**
     * 删除模型版本
     *
     * @param request 请求体
     *
     * @return 是否成功
     */
    @DeleteMapping("/version")
    public Rest<Boolean> deleteVersion(@RequestBody ModelVersionDeleteRequest request) {
        Boolean isOk = resModelService.deleteVersion(request);
        return Rest.ok(isOk);
    }

    /**
     * 查询引擎
     *
     * @param clusterId 集群id
     *
     * @return ClusterGroupEngineResp
     */
    @GetMapping("/engine")
    public Rest<List<ClusterGroupEngineResp>> queryClusterGroupEngine(@RequestParam(required = false) Long clusterId) {
        List<ClusterGroupEngineResp> responses = clusterEngineFlavorService.queryClusterGroupEngine(clusterId);
        return Rest.ok(responses);
    }

    /**
     * 查询已完成训练作业
     *
     * @return ClusterGroupEngineResp
     */
    @GetMapping("/job")
    public Rest<List<MetaModelResponse>> getCompletedJob(@RequestParam(required = false) Long clusterId) {
        List<MetaModelResponse> responses = resModelService.getCompletedJob(clusterId);
        return Rest.ok(responses);
    }

    /**
     * 查询可用的模型列表
     *
     * @return ClusterGroupEngineResp
     */
    @GetMapping("/normal/list")
    public Rest<List<NormalModelResponse>> getNormalModel() {
        return Rest.ok(resModelService.getNormalModel());
    }

}
