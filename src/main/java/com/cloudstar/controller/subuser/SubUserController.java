package com.cloudstar.controller.subuser;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.constant.UserAuthStatusEnum;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ResetPwdReq;
import com.cloudstar.service.facade.subuser.UserGroupService;
import com.cloudstar.service.facade.user.UserEntityService;
import com.cloudstar.service.pojo.vo.requestvo.subuser.CreateUserGroupReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.CreateUsersRequest;
import com.cloudstar.service.pojo.vo.requestvo.subuser.MovePolicyToGroupReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.MoveUserToGroupReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.QuerySubUserReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.QueryUserGroupReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.SyncUsersRequest;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserMappingResp;
import com.cloudstar.service.pojo.vo.responsevo.subuser.QueryGroupDetailRes;
import com.cloudstar.service.pojo.vo.responsevo.subuser.QueryGroupInfoRes;
import com.cloudstar.service.pojo.vo.responsevo.subuser.SubUserVO;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 用户组控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/access")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SubUserController {

    UserGroupService userGroupService;

    ServerClient serverClient;

    UserEntityService userEntityService;

    /**
     * 创建用户组
     */
    @PostMapping("/user_group")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_GROUP_ADD)
    public Rest<Boolean> createUserGroup(@RequestBody @Valid CreateUserGroupReq req) {
        return Rest.ok(userGroupService.createUserGroup(req));
    }


    /**
     * 查询用户组
     */
    @GetMapping("/user_group")
    public Rest<PageResult<QueryGroupInfoRes>> queryUserGroup(QueryUserGroupReq req) {
        return Rest.ok(userGroupService.queryUserGroup(req));
    }


    /**
     * 查询用户组详情
     */
    @GetMapping("/user_group/{groupSid}")
    public Rest<QueryGroupDetailRes> queryUserGroupById(@PathVariable("groupSid") Long groupSid) {
        return Rest.ok(userGroupService.queryUserGroupById(groupSid));
    }


    /**
     * 用户组关联用户
     */
    @PostMapping("/user_group/user")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_DISTRIBUTION)
    public Rest<Boolean> modifyUserInGroup(@RequestBody @Valid MoveUserToGroupReq req) {
        userGroupService.multiAddUser(req);
        return Rest.ok(Boolean.TRUE);
    }

    /**
     * 更新用户组权限策略
     */
    @PostMapping("/user_group/policy/update")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_JURISDICTION_DISTRIBUTION)
    public Rest<Boolean> modifyPolicyInGroup(@RequestBody @Valid MovePolicyToGroupReq req) {
        userGroupService.multiAddPolicy(req);
        return Rest.ok(Boolean.TRUE);
    }

    /**
     * 查询子用户
     */
    @GetMapping("/sub_user")
    public Rest<PageResult<SubUserVO>> querySubUser(QuerySubUserReq request) {
        Page<UserEntity> userGroups = userEntityService.selectSubUser(request.pageRequest(), request);
        PageResult<SubUserVO> result = PageResult.of(userGroups, SubUserVO.class);
        for (SubUserVO subUser : result.getList()) {
            subUser.setStatusName(UserStatusEnum.getDesc(subUser.getStatus()));
            subUser.setAuthStatusName(UserAuthStatusEnum.getDesc(subUser.getAuthStatus()));
        }
        return Rest.ok(result);
    }

    /**
     * 批量创建子用户
     */
    @PostMapping("/sub_user")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_ADD)
    public Rest<ClusterUserMappingResp> createUser(@RequestBody @Valid CreateUsersRequest request) {
        return Rest.ok(userEntityService.createUser(request));
    }

    /**
     * 批量创建子用户
     */
    @PostMapping("/sub_user/sync")
    public Rest<Boolean> createUserSync(@RequestBody @Valid SyncUsersRequest request) {
        return Rest.ok(userEntityService.createUserSync(request));
    }

    @PostMapping("/sub_user/bss")
    public Rest<List<String>> createUserSyncBss(@RequestBody @Valid SyncUsersRequest request) {
        return Rest.ok(userEntityService.createSubUserBss(request));
    }

    /**
     * 更改子用户状态
     */
    @PostMapping("/sub_user/status/reset/{userSid}")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_STATUS_RESET)
    public Rest<Boolean> resetStatus(@PathVariable("userSid") Long userSid) {
        userEntityService.resetStatusByUserSid(userSid);
        return Rest.ok(Boolean.TRUE);
    }

    /**
     * 子用户删除
     */
    @PostMapping("/sub_user/delete")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_DELETE)
    public Rest<Boolean> deleteSubUserById(@RequestBody List<Long> userSid) {
        userEntityService.deleteSubUserByIds(userSid);
        return Rest.ok(Boolean.TRUE);
    }


    /**
     * 更改子用户密码
     */
    @PostMapping("/sub_user/password/reset")
    @CustomerActionLog(Type = ActionLogTypeEnum.SUB_USER_EDIT_PWD)
    public Rest<Boolean> resetPassword(@RequestBody ResetPwdReq request) {
        request.setOldPassword(CrytoUtilSimple.asymmetricDecryptWebContent(request.getOldPassword()));
        request.setNewPassword(CrytoUtilSimple.asymmetricDecryptWebContent(request.getNewPassword()));
        return userEntityService.resetPwd(request);
    }

    /**
     * 配置策略组
     */
    @PostMapping("/relate_group")
    @CustomerActionLog(Type = ActionLogTypeEnum.CONFIG_POLICY_GROUP)
    public Rest<Boolean> configPolicyGroup(@Valid @RequestBody MovePolicyToGroupReq req) {
        userGroupService.multiAddPolicy(req);
        return Rest.ok(Boolean.TRUE);
    }


}
