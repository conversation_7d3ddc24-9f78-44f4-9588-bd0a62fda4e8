package com.cloudstar.controller.training;

import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.JobPodInfo;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.sdk.schedule.pojo.TrainingJobLogResp;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.cluster.ClusterEngineFlavorService;
import com.cloudstar.service.facade.cluster.ClusterResourcePoolService;
import com.cloudstar.service.facade.datastorage.DataStorageResourcesService;
import com.cloudstar.service.facade.training.TrainingJobEntityService;
import com.cloudstar.service.pojo.dto.training.QueryTrainingJobEntityDto;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryFlavorReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.QueryDataStorageReq;
import com.cloudstar.service.pojo.vo.requestvo.training.AddTrainingJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.CreateCoordinationJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.DeleteJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.QueryCoordinationJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.QueryTrainingJobEntityReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterEngineResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterFlavorResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterGroupEngineResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.QueryDataStorageResp;
import com.cloudstar.service.pojo.vo.responsevo.training.CoordinationJobResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobDetailResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobEntityResp;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * 作业
 *
 * <AUTHOR>
 * @date 2022/8/19 14:14
 */
@RestController
@RequestMapping("/training")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TrainingJobEntityController {


    TrainingJobEntityService trainingJobEntityService;

    ScheduleClient scheduleClient;

    ServerClient serverClient;

    DataStorageResourcesService dataStorageResourcesService;

    ClusterEngineFlavorService clusterEngineFlavorService;

    ClusterResourcePoolService clusterResourcePoolService;

    /**
     * 查询训练作业列表
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     *
     * @return 查询结果
     */
    @GetMapping
    public Rest<PageResult<TrainingJobEntityResp>> page(QueryTrainingJobEntityReq req, PageForm pageForm) {
        return Rest.ok(PageResult.of(
                trainingJobEntityService.page(BeanUtil.toBean(req, QueryTrainingJobEntityDto.class), pageForm),
                TrainingJobEntityResp.class));
    }

    /**
     * 创建训练作业
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CREAT_JOB)
    @PostMapping
    public Rest addTrainingJob(@Valid @RequestBody AddTrainingJobReq req) {
        return Rest.ok(trainingJobEntityService.addTrainingJob(req));
    }

    /**
     * 获取作业引擎
     *
     * @param clusterId 集群id
     */
    @GetMapping("/engine")
    public Rest<List<ClusterEngineResp>> queryClusterEngine(@RequestParam Long clusterId) {
        return Rest.ok(
                BeanUtil.copyToList(clusterEngineFlavorService.queryClusterEngine(clusterId), ClusterEngineResp.class));
    }

    /**
     * 获取作业引擎组
     *
     * @param clusterId 集群id
     */
    @GetMapping("/group/engine")
    public Rest<List<ClusterGroupEngineResp>> queryClusterGroupEngine(@RequestParam(required = false) Long clusterId) {
        return Rest.ok(clusterEngineFlavorService.queryClusterGroupEngine(clusterId));
    }

    /**
     * 获取作业资源规格
     *
     * @param engineId 引擎id
     */
    @GetMapping("/flavor")
    public Rest<List<ClusterFlavorResp>> queryClusterFlavor(@RequestParam Long engineId) {
        return Rest.ok(
                BeanUtil.copyToList(clusterEngineFlavorService.queryClusterFlavor(engineId), ClusterFlavorResp.class));
    }

    /**
     * 根据引擎版本获取作业资源规格
     *
     * @param engineVersion 引擎版本
     */
    @GetMapping("/flavor/engine_version")
    public Rest<List<ClusterFlavorResp>> queryClusterFlavorByEngineVersion(@RequestParam String engineVersion) {
        return Rest.ok(
                BeanUtil.copyToList(clusterEngineFlavorService.queryClusterFlavorByEngineVersion(engineVersion),
                                    ClusterFlavorResp.class));
    }

    /**
     * 获取ak、sk信息
     */
    @GetMapping("/ak_sk_info")
    public Rest<ClusterSubAccountRespDto> queryAkSkInfo(@RequestParam Long clusterId) {
        ClusterSubAccountReqDto dto = new ClusterSubAccountReqDto();
        dto.setClusterId(clusterId);
        dto.setUserSid(ThreadAuthUserHolder.getAuthUser().getUserSid());
        Rest<ClusterSubAccountRespDto> akSkInfo = serverClient.getClusterSubAccount(dto);
        ClusterSubAccountRespDto data = akSkInfo.getData();
        data.setAccessKey(CrytoUtilSimple.decrypt(data.getAccessKey()));
        data.setSecretKey(CrytoUtilSimple.decrypt(data.getSecretKey()));
        data.setObsUrl(data.getObsUrl().replace("iam-pub", "obs").replace("iam", "obs"));
        return akSkInfo;
    }

    /**
     * 获取作业资源规格
     */
    @GetMapping("/group/flavor")
    public Rest queryClusterGroupFlavor(QueryFlavorReq req) {
        return Rest.ok(clusterEngineFlavorService.queryClusterGroupFlavor(req));
    }

    /**
     * 获取专属资源池作业资源规格
     */
    @GetMapping("/group/exclusive_flavor")
    public Rest queryClusterExclusiveFlavor(QueryFlavorReq req) {
        return Rest.ok(clusterEngineFlavorService.queryClusterExclusiveFlavor(req));
    }

    /**
     * 删除训练作业
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.TRAINING_JOB_DELETE)
    @PostMapping("/delete")
    public Rest<Boolean> deleteTrainingJob(@Valid DeleteJobReq req) {
        return Rest.ok(trainingJobEntityService.delete(req));
    }

    /**
     * 停止训练作业
     *
     * @param jobId 作业id
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.TRAINING_JOB_STOP)
    @PostMapping("/stop")
    public Rest<Boolean> stopTrainingJob(@RequestParam Long jobId) {
        return Rest.ok(trainingJobEntityService.stop(jobId));
    }

    /**
     * 获取训练作业详情
     *
     * @param jobId 作业id
     */
    @GetMapping("/detail")
    public Rest<TrainingJobDetailResp> queryDetail(@RequestParam Long jobId, @RequestParam(required = false) String action) {
        return Rest.ok(trainingJobEntityService.queryDetailById(jobId, action));
    }

    /**
     * 获取训练作业日志
     *
     * @param jobId 作业id
     */
    @GetMapping("/log")
    public Rest<TrainingJobLogResp> queryTrainJobLog(@RequestParam Long jobId, @RequestParam String podName) {
        try {
            return scheduleClient.queryTrainingJobLog(jobId, podName);
        } catch (Exception e) {
            log.info("获取作业日志失败:", e);
            TrainingJobLogResp resp = new TrainingJobLogResp();
            resp.setContent("无法获取作业日志内容");
            resp.setCurrentSize(10);
            resp.setFullSize(10);
            return Rest.ok(resp);
        }
    }

    /**
     * 查询可用调度策略
     *
     * @return {@link Rest}<{@link List}<{@link Object}>>
     */
    @GetMapping("/schedule/policy")
    public Rest<List<Object>> queryStatusIsEnable() {
        return serverClient.queryStatusIsEnable();
    }

    /**
     * 查询集群调度策略
     *
     * @param policyId 政策id
     * @param clusterType 集群类型
     *
     * @return {@link Rest}<{@link List}<{@link Object}>>
     */
    @GetMapping("/schedule/policy/cluster")
    public Rest<List<Object>> queryPolicyCluster(@RequestParam Long policyId, @RequestParam(required = false) String clusterType,
                                                 @RequestParam(required = false, defaultValue = "false") Boolean isHpc) {
        return serverClient.queryPolicyCluster(policyId, clusterType, isHpc);
    }

    /**
     * 获取专属集群信息
     *
     * @return {@link Rest}
     */
    @GetMapping("/exclusive_cluster")
    public Rest getExclusiveCluster() {
        return Rest.ok(clusterResourcePoolService.getExclusiveCluster());
    }

    /**
     * 取消训练作业
     *
     * @param jobId 作业id
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.TRAINING_JOB_CANCEL)
    @PostMapping("/cancel")
    public Rest<Boolean> cancelTrainingJob(@RequestParam Long jobId) {
        return Rest.ok(trainingJobEntityService.cancel(jobId));
    }

    /**
     * 分页查询协同作业
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     *
     * @return 查询结果
     */
    @GetMapping("/coordination")
    public Rest<PageResult<CoordinationJobResp>> getPage(QueryCoordinationJobReq req, PageForm pageForm) {
        return Rest.ok(trainingJobEntityService.getPage(req, pageForm));
    }

    /**
     * 获取协同作业详情
     *
     * @param jobId 作业id
     */
    @GetMapping("/coordination/detail")
    public Rest<TrainingJobDetailResp> queryCoordinationDetail(@RequestParam Long jobId, @RequestParam(required = false) String action) {
        return Rest.ok(trainingJobEntityService.queryDetailById(jobId, action));
    }

    /**
     * 创建协同作业
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CREAT_COORDINATION_JOB)
    @PostMapping("/coordination")
    public Rest addCoordinationJob(@Valid @RequestBody CreateCoordinationJobReq req) {
        return Rest.ok(trainingJobEntityService.createCoordinationJob(req));
    }

    /**
     * 删除协同作业
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.DELETE_COORDINATION_JOB)
    @PostMapping("/coordination/delete")
    public Rest<Boolean> deleteCoordinationJob(@Valid DeleteJobReq req) {
        return Rest.ok(trainingJobEntityService.delete(req));
    }

    /**
     * 获取协同作业日志
     *
     * @param jobId 作业id
     */
    @GetMapping("/coordination/log")
    public Rest<TrainingJobLogResp> queryCoordinationLog(@RequestParam Long jobId, @RequestParam String podName) {
        try {
            return scheduleClient.queryTrainingJobLog(jobId, podName);
        } catch (Exception e) {
            log.info("获取作业日志失败:", e);
            TrainingJobLogResp resp = new TrainingJobLogResp();
            resp.setContent("无法获取作业日志内容");
            resp.setCurrentSize(10);
            resp.setFullSize(10);
            return Rest.ok(resp);
        }
    }

    /**
     * 根据类型查询数据集
     *
     * @return 查询结果
     */
    @GetMapping("/coordination/find_by_category")
    public Rest<List<QueryDataStorageResp>> findByCategory(@Valid QueryDataStorageReq req) {
        return Rest.ok(dataStorageResourcesService.findByCategory(req));
    }

    /**
     * 查询训练作业指定任务的运行指标
     *
     * @param jobId 作业id
     */
    @GetMapping("/metrics/{jobId}")
    public Rest<Map<String, List<Float>>> showTrainingJobMetrics(@PathVariable("jobId") Long jobId) {
        try {
            return scheduleClient.showTrainingJobMetrics(jobId);
        } catch (Exception e) {
            log.error("获取作业监控数据失败，msg：{}:", e.getMessage());
            return Rest.ok(new HashMap<>(1));
        }
    }


    /**
     * 查询作业监控地址url
     *
     * @param jobId 作业id
     */
    @GetMapping("/metrics/url")
    public Rest<String> queryVolcanoJobMetrics(@RequestParam Long jobId, @RequestParam String podName) {
        return Rest.ok(trainingJobEntityService.queryVolcanoJobMetrics(jobId, podName));
    }

    /**
     * 获取作业pod信息
     *
     * @param jobId 作业id
     */
    @GetMapping("/pod")
    public Rest<List<JobPodInfo>> queryPodInfo(@RequestParam Long jobId) {
        return Rest.ok(trainingJobEntityService.queryPodInfo(jobId));
    }

}
