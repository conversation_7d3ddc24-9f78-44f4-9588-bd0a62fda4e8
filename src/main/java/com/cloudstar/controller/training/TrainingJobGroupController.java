package com.cloudstar.controller.training;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.training.TrainingJobGroupService;
import com.cloudstar.service.pojo.vo.requestvo.training.QueryTrainingJobGroupReq;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobGroupDetailResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobGroupResp;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 作业组
 *
 * <AUTHOR>
 * @date 2022/11/12 11:35
 */
@RestController
@RequestMapping("/task_group")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TrainingJobGroupController {

    TrainingJobGroupService trainingJobGroupService;

    /**
     * 分页查询作业组
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     *
     * @return 查询结果
     */
    @GetMapping
    public Rest<PageResult<TrainingJobGroupResp>> page(QueryTrainingJobGroupReq req, PageForm pageForm) {
        return Rest.ok(trainingJobGroupService.selectPageServer(req, pageForm));
    }

    /**
     * 作业组详情
     *
     * @param taskGroupId 作业组id
     *
     * @return 查询结果
     */
    @GetMapping("/{taskGroupId}")
    public Rest<TrainingJobGroupDetailResp> getGroupDetails(@PathVariable String taskGroupId) {
        return Rest.ok(trainingJobGroupService.getGroupDetailsServer(taskGroupId));
    }

}
