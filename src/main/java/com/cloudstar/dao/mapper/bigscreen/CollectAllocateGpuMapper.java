package com.cloudstar.dao.mapper.bigscreen;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cloudstar.dao.model.bigscreen.CollectAllocateGpu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * gpu采集mapper
* <AUTHOR>
* @description 针对表【collect_allocate_gpu(资源池分配率-gpu)】的数据库操作Mapper
* @createDate 2022-10-26 16:43:25
* @Entity com.cloudstar.dao.model.bigscreen.CollectAllocateGpu
*/
@DS("monitor")
public interface CollectAllocateGpuMapper extends BaseMapper<CollectAllocateGpu> {
    CollectCommonResourceDto getPoolGpuResource(@Param("poolId") String poolId, @Param("monitorCollect") Integer monitorCollect);
    CollectCommonResourceDto getExclusiveGpuResource(@Param("clusterId") Long clusterId, @Param("poolId") String poolId,
                                                  @Param("monitorCollect") Integer monitorCollect);
    CollectCommonResourceDto getGpuResource(@Param("clusterId") Long clusterId, @Param("monitorCollect") Integer monitorCollect);

    List<CollectCommonResourceDto> getGpuAllocatedResourceTrend(@Param("clusterId") Long clusterId,
                                                                @Param("monitorCollect") Integer monitorCollect,
                                                                @Param("trendMonitorCollect") Integer trendMonitorCollect,
                                                                @Param("capacity") Long capacity,
                                                                @Param("type") String type,
                                                                @Param("poolId") String poolId,
                                                                @Param("excluseiveNum") Long excluseiveNum);
}




