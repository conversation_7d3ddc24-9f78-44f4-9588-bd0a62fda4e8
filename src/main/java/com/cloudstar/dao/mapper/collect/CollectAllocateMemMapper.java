package com.cloudstar.dao.mapper.collect;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.dao.model.collect.CollectAllocateMem;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 针对表【collect_allocate_mem(资源池分配率-内存)】的数据库操作Mapper
 *
 * <AUTHOR>
 * @createDate 2022-10-14 10:50:33
 * @Entity com.cloudstar.dao.model.collect.CollectAllocateMem
 */
public interface CollectAllocateMemMapper extends BaseMapper<CollectAllocateMem> {

    @Select("select (cpu.pool_capacity - cpu.pool_allocated) as unused\n"
            + "from collect_allocate_mem cpu\n"
            + "         left join (select max(time) as newTime, pool_id as pool_id\n"
            + "                    from collect_allocate_mem\n"
            + "                    group by pool_id) temp\n"
            + "                   on temp.pool_id = cpu.pool_id\n"
            + "where cpu.pool_id = #{clusterId}\n"
            + "  and cpu.cluster_id = #{poolId}\n"
            + "  and cpu.time = temp.newTime")
    Integer selectUnusedMem(@Param("clusterId") Long clusterId, @Param("poolId") String poolId);

    /**
     * 查询七天前的cpu 每日平均值
     */
    @Select("select cluster_id,\n"
            + "       pool_id,\n"
            + "       to_char(time, 'YYYY-MM-DD') as time,\n"
            + "       ceil(avg(pool_allocated)) as pool_allocated,\n"
            + "       ceil(avg(pool_capacity)) as pool_capacity\n"
            + "from collect_allocate_mem\n"
            + "where time < current_date - 7\n"
            + "group by cluster_id, pool_id, to_char(time, 'YYYY-MM-DD');")
    List<CollectAllocateMem> selectDayAvgMem();

    /**
     * 删除一周前的数据
     */
    @Delete("delete from collect_allocate_mem where time < current_date - 7")
    Integer deleteWeekAgoData();
}
