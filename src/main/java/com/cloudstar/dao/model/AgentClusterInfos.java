package com.cloudstar.dao.model;

import lombok.Data;

/**
 * AgentClusterInfos 类表示代理集群的详细信息。
 * 包含集群名称、端口、任务持续时间、独占池数量和集群类型等属性。
 */
@Data
public class AgentClusterInfos {
    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群端口
     */
    private Integer clusterPort;

    /**
     * 任务持续时间
     */
    private String jobDuration;

    /**
     * 独占池数量
     */
    private Integer exclusivePoolNum;

    /**
     * 集群类型
     */
    private String clusterType;
}
