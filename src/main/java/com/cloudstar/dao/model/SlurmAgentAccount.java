package com.cloudstar.dao.model;

import com.cloudstar.service.pojo.dto.ImportValidMessageDto;

public class SlurmAgentAccount extends AgentAccount {


    public ImportValidMessageDto validUser(AgentUser importAgentUser) {
        // 实现 Slurm 集群账号验证逻辑
        return new ImportValidMessageDto(importAgentUser.getUserId(), true, "Slurm 用户验证成功");
    }

    public ImportValidMessageDto verifyUser(AgentUser importAgentUser) {
        // 实现 Slurm 集群账号验证逻辑
        return new ImportValidMessageDto(importAgentUser.getUserId(), true, "Slurm 用户验证成功");
    }
}
