package com.cloudstar.dao.model.access;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户权限策略表
 *
 * @TableName sys_m_policy
 */
@Data
@TableName(value = "sys_m_policy")
public class SysMPolicy implements Serializable {
    /**
     * 策略SID
     */
    @TableId(value = "policy_sid", type = IdType.AUTO)
    private Long policySid;

    /**
     * 策略标识
     */
    @TableField(value = "policy_name")
    private String policyName;

    /**
     * 显示名称
     */
    @TableField(value = "display_name")
    private String displayName;

    /**
     * 策略类型;system[系统内置],custom[自定义]
     */
    @TableField(value = "policy_type")
    private String policyType;

    /**
     * 状态;0[禁用] 1[启用]
     */
    @TableField(value = "policy_status")
    private Integer policyStatus;

    /**
     * 资源类型;具体涉及到的策略资源类型如：["all"],["ecs","ebs"]
     */
    @TableField(value = "resource_types")
    private String resourceTypes;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 组织SID
     */
    @TableField(value = "org_sid")
    private Long orgSid;

    /**
     * 所有者SID
     */
    @TableField(value = "owner_sid")
    private Long ownerSid;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Date authorizedTime;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SysMPolicy other = (SysMPolicy) that;
        return (this.getPolicySid() == null ? other.getPolicySid() == null : this.getPolicySid().equals(other.getPolicySid()))
                && (this.getPolicyName() == null ? other.getPolicyName() == null : this.getPolicyName().equals(other.getPolicyName()))
                && (this.getDisplayName() == null ? other.getDisplayName() == null : this.getDisplayName().equals(other.getDisplayName()))
                && (this.getPolicyType() == null ? other.getPolicyType() == null : this.getPolicyType().equals(other.getPolicyType()))
                && (this.getPolicyStatus() == null ? other.getPolicyStatus() == null : this.getPolicyStatus().equals(other.getPolicyStatus()))
                && (this.getResourceTypes() == null ? other.getResourceTypes() == null : this.getResourceTypes().equals(other.getResourceTypes()))
                && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
                && (this.getOrgSid() == null ? other.getOrgSid() == null : this.getOrgSid().equals(other.getOrgSid()))
                && (this.getOwnerSid() == null ? other.getOwnerSid() == null : this.getOwnerSid().equals(other.getOwnerSid()))
                && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
                && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
                && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
                && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
                && (this.getUpdatedDt() == null ? other.getUpdatedDt() == null : this.getUpdatedDt().equals(other.getUpdatedDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getPolicySid() == null) ? 0 : getPolicySid().hashCode());
        result = prime * result + ((getPolicyName() == null) ? 0 : getPolicyName().hashCode());
        result = prime * result + ((getDisplayName() == null) ? 0 : getDisplayName().hashCode());
        result = prime * result + ((getPolicyType() == null) ? 0 : getPolicyType().hashCode());
        result = prime * result + ((getPolicyStatus() == null) ? 0 : getPolicyStatus().hashCode());
        result = prime * result + ((getResourceTypes() == null) ? 0 : getResourceTypes().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getOrgSid() == null) ? 0 : getOrgSid().hashCode());
        result = prime * result + ((getOwnerSid() == null) ? 0 : getOwnerSid().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedDt() == null) ? 0 : getUpdatedDt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", policySid=").append(policySid);
        sb.append(", policyName=").append(policyName);
        sb.append(", displayName=").append(displayName);
        sb.append(", policyType=").append(policyType);
        sb.append(", policyStatus=").append(policyStatus);
        sb.append(", resourceTypes=").append(resourceTypes);
        sb.append(", description=").append(description);
        sb.append(", orgSid=").append(orgSid);
        sb.append(", ownerSid=").append(ownerSid);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedDt=").append(updatedDt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}