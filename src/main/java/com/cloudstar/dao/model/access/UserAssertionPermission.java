package com.cloudstar.dao.model.access;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户断言权限关联表
 *
 * @TableName user_assertion_permission
 */
@TableName(value = "user_assertion_permission")
public class UserAssertionPermission implements Serializable {
    /**
     * 断言ID
     */
    @TableField(value = "assertion_sid")
    private Long assertionSid;

    /**
     * 权限ID
     */
    @TableField(value = "permission_sid")
    private Long permissionSid;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 断言ID
     */
    public Long getAssertionSid() {
        return assertionSid;
    }

    /**
     * 断言ID
     */
    public void setAssertionSid(Long assertionSid) {
        this.assertionSid = assertionSid;
    }

    /**
     * 权限ID
     */
    public Long getPermissionSid() {
        return permissionSid;
    }

    /**
     * 权限ID
     */
    public void setPermissionSid(Long permissionSid) {
        this.permissionSid = permissionSid;
    }

    /**
     * 乐观锁
     */
    public String getVersion() {
        return version;
    }

    /**
     * 乐观锁
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 创建时间
     */
    public Date getCreatedDt() {
        return createdDt;
    }

    /**
     * 创建时间
     */
    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    /**
     * 更新人
     */
    public String getUpdatedBy() {
        return updatedBy;
    }

    /**
     * 更新人
     */
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * 更新时间
     */
    public Date getUpdatedDt() {
        return updatedDt;
    }

    /**
     * 更新时间
     */
    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserAssertionPermission other = (UserAssertionPermission) that;
        return (this.getAssertionSid() == null ? other.getAssertionSid() == null : this.getAssertionSid().equals(other.getAssertionSid()))
                && (this.getPermissionSid() == null ? other.getPermissionSid() == null : this.getPermissionSid().equals(other.getPermissionSid()))
                && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
                && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
                && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
                && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
                && (this.getUpdatedDt() == null ? other.getUpdatedDt() == null : this.getUpdatedDt().equals(other.getUpdatedDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAssertionSid() == null) ? 0 : getAssertionSid().hashCode());
        result = prime * result + ((getPermissionSid() == null) ? 0 : getPermissionSid().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedDt() == null) ? 0 : getUpdatedDt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", assertionSid=").append(assertionSid);
        sb.append(", permissionSid=").append(permissionSid);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedDt=").append(updatedDt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}