package com.cloudstar.dao.model.access;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 租户权限表
 *
 * @TableName user_permission
 */
@Data
@TableName(value = "user_permission")
public class UserPermission implements Serializable {
    /**
     * 权限id
     */
    @TableField(value = "permission_sid")
    private Long permissionSid;

    /**
     * 功能名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 接口地址
     */
    @TableField(value = "url")
    private String url;

    /**
     * 方法
     */
    @TableField(value = "method")
    private String method;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 所属模块；iam，schdule
     */
    @TableField(value = "module_category")
    private String moduleCategory;

    /**
     * 所属控制层；roleController，accessController
     */
    @TableField(value = "controller_category")
    private String controllerCategory;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private boolean select;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserPermission other = (UserPermission) that;
        return (this.getPermissionSid() == null ? other.getPermissionSid() == null : this.getPermissionSid().equals(other.getPermissionSid()))
                && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
                && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
                && (this.getMethod() == null ? other.getMethod() == null : this.getMethod().equals(other.getMethod()))
                && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getPermissionSid() == null) ? 0 : getPermissionSid().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getMethod() == null) ? 0 : getMethod().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", premissionSid=").append(permissionSid);
        sb.append(", name=").append(name);
        sb.append(", url=").append(url);
        sb.append(", method=").append(method);
        sb.append(", sort=").append(sort);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}