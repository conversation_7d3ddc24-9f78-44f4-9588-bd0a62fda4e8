package com.cloudstar.dao.model.cluster;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;

/**
 * 集群引擎表;
 *
 * <AUTHOR> 沈晨曦
 * @date : 2022-7-14
 */
@Data
@TableName(value = "agent_engine")
public class AgentEngine implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 底层引擎id
     */
    private String engineId;
    /**
     * 引擎名称
     */
    private String engineName;
    /**
     * 引擎版本
     */
    private String engineVersion;

    /**
     * cpu规格下对应镜像
     */
    private String cpuImageUrl;

    /**
     * gpu或者Ascend规格下对应镜像
     */
    private String gpuImageUrl;

    /**
     * 镜像版本
     */
    private String imageVersion;

    /**
     * 资源类型 cpu gpu npu
     */
    private String resourceType;

    /**
     * 架构类型 arm或者x86
     */
    private String archType;
}
