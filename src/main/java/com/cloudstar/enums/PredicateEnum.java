package com.cloudstar.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * API断言枚举
 *
 * <AUTHOR>
 * @date 2022/08/20
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum PredicateEnum {
    SERVER("USER", "/api/v1/server/", "运营运维端"),
    CONSOLE("CONSOLE", "/api/v1/console/", "租户端");

    /**
     * 类型
     */
    private String type;

    /**
     * 类型
     */
    private String path;

    /**
     * 描述
     */
    private String desc;
}
