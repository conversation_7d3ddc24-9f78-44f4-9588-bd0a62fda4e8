package com.cloudstar.integration.bss.pojo.message;

import java.util.Map;

import lombok.Data;

/**
 * bss 消息请求入参
 *
 * <AUTHOR>
 * @date 2023/6/2 10:18
 */
@Data
public class BssMessageReq {
    
    /**
     * 提示方式   0站内消息  1邮件  2短信
     */
    private int[] messageType;

    /**
     * 发送地址
     */
    private Map<String, Object> sendAddress;

    /**
     * messageContent key为对应消息模板的替换key，value为替换后的内容 如：用户【${user}】申请开通云应用【${appName}】成功！
     */
    private Map<String, String> messageContent;


    /**
     * 是否提示管理员
     */
    private boolean notifyAdmin;

    /**
     * 模板id
     */
    private String messageId;

    /**
     * 用户名
     */
    private String account;
}
