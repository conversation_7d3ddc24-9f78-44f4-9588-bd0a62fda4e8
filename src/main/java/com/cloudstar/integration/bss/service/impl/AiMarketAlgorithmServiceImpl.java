package com.cloudstar.integration.bss.service.impl;

import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.integration.bss.pojo.RestResult;
import com.cloudstar.integration.bss.pojo.algorithm.MarketSubPageResp;
import com.cloudstar.integration.bss.service.BssClient;
import com.cloudstar.integration.bss.service.facade.AiMarketAlgorithmService;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 模型市场算法
 *
 * <AUTHOR>
 * @date 2024/8/22 14:21
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AiMarketAlgorithmServiceImpl implements AiMarketAlgorithmService {

    BssClient bssClient;

    @Override
    public RestResult getAlgorithmSubscribePage(String account, String algorithmName, Integer pageNum, Integer pageSize, Integer shopType) {
        RestResult result = bssClient.getAlgorithmSubscribe(pageNum - 1, pageSize, algorithmName, shopType, account);
        log.info("查询运营平台我的订阅返回值:{}", JSONUtil.toJsonStr(result));
        if (!result.isSuccessful()) {
            throw new BizException("获取运营平台我的订阅失败");
        }
        return result;
    }

    @Override
    public List<MarketSubPageResp> getAlgorithmSubscribeList(String account, String algorithmName) {
        RestResult result = bssClient.getAlgorithmSubscribe(0, 10, account, 0, algorithmName);
        int totalRows = result.getTotalRows();
        List<MarketSubPageResp> respList = new ArrayList<>();
        if (totalRows > 10) {
            int pageNum = totalRows / 10;
            for (int i = 0; i <= pageNum; i++) {
                result = bssClient.getAlgorithmSubscribe(i, 10, account, 0, algorithmName);
                if (CollectionUtil.isNotEmpty(result.dataToList(MarketSubPageResp.class))) {
                    respList.addAll(result.dataToList(MarketSubPageResp.class));
                }
            }
        } else {
            respList = result.dataToList(MarketSubPageResp.class);
        }
        return respList;
    }
}
