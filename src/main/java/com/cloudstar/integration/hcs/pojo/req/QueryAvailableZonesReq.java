package com.cloudstar.integration.hcs.pojo.req;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 查询可用区
 *
 * <AUTHOR>
 * @date 2025/3/7 17:04
 */
@Data
public class QueryAvailableZonesReq {

    /**
     * Region ID，长度范围0~32字符。
     */
    @JsonProperty("region_id")
    private String regionId;

    /**
     * 云资源池类型，类型有：NFVI，VMWARE，FUSION_CLOUD，HWS，VRM，Power-VM，Hyper-V和CRS。
     */
    @JsonProperty("cloud_infra_type")
    private String cloudInfraType;

    /**
     * 云资源池 ID，长度范围0~64字符。
     */
    @JsonProperty("cloud_infra_id")
    private String cloudInfraId;

    /**
     * 云资源池接入状态，有以下四种状态：init，connected，deprecated，deleted。
     */
    @JsonProperty("access_status")
    private String accessStatus;
}
