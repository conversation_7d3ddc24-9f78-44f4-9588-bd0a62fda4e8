package com.cloudstar.integration.hcs.pojo.req;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 更改外部网络
 *
 * <AUTHOR>
 * @date 2025/3/7 17:16
 */
@Data
public class UpdateVdcExternalNetworksReq {

    /**
     * 外部网络ID，只能包含小写字母、数字、中划线，长度在1-36之间。
     */
    @JsonProperty("network_id")
    private String networkId;

    /**
     * 1 级 VDC ID，只能包含小写字母、数字、中划线，长度在1-36之间。
     */
    @JsonProperty("resource_id")
    private String resourceId;

    /**
     * 资源池ID，长度在1-64之间。
     */
    @JsonProperty("cloudInfraId")
    private String cloudInfraId;

    /**
     * 分配或取消分配，true表示分配，false表示取消分配。
     */
    @JsonProperty("associateAction")
    private boolean associateAction;

    /**
     * 网络名称，支持中文、英文、数字、中划线、下划线，长度在1-36位之间。
     */
    @JsonProperty("networkName")
    private String networkName;

    /**
     * 是否支持为租户下所有的资源空间分配外部网络，默认为true。
     */
    @JsonProperty("inherit")
    private Boolean inherit;
}
