package com.cloudstar.integration.nexus.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonGetter;

import java.util.HashMap;
import java.util.Map;

public enum BucketType {
    SPACE_REMAINING_QUOTA("spaceRemainingQuota"),
    SPACE_USED_QUOTA("spaceUsedQuota");

    private static final Map<String, BucketType> TYPE_MAP = new HashMap<>();

    static {
        for (BucketType bucketType : BucketType.values()) {
            TYPE_MAP.put(bucketType.getValue(), bucketType);
        }
    }

    private final String value;

    BucketType(String value) {
        this.value = value;
    }

    @JsonCreator
    public static BucketType forValue(String value) {
        return TYPE_MAP.getOrDefault(value, null);
    }

    public String getValue() {
        return value;
    }

    @JsonGetter
    public String toValue() {
        return value;
    }
}
