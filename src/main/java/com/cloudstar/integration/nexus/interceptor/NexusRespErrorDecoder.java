package com.cloudstar.integration.nexus.interceptor;

import com.github.lianjiatech.retrofit.spring.boot.core.ErrorDecoder;

import org.springframework.stereotype.Component;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;


/**
 * nexus resp错误解码器
 *
 * <AUTHOR>
 * @date 2024/06/11
 */
@Component
@Slf4j
public class NexusRespErrorDecoder implements ErrorDecoder {

    @Override
    public RuntimeException invalidRespDecode(Request request, Response response) {
        try {
            if (!response.isSuccessful()) {
                log.error("NEXUS 请求失败,url:[{}],responseCode:{},requestBody:{},errorMsg:{}",
                          request.url(), response.code(), request.body() == null ? "null" : JSONUtil.toJsonStr(request.body()), response.message());
            }
        } catch (Exception e) {
            log.error("读取响应参数失败:{}", e.getMessage());
        }
        return null;
    }
}
