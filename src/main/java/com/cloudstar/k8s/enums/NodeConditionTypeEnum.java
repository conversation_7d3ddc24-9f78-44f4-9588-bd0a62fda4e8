package com.cloudstar.k8s.enums;

/**
 * NodeConditionTypeEnum
 *
 * <AUTHOR>
 * @date 2024/3/27 16:29
 */
public enum NodeConditionTypeEnum {
    /**
     * 网络是否可用
     */
    NETWORKUNAVAILABLE("NetworkUnavailable"),
    /**
     * 内存压力
     */
    MEMORYPRESSURE("MemoryPressure"),
    /**
     * 磁盘压力
     */
    DISKPRESSURE("DiskPressure"),
    /**
     * PID压力
     */
    PIDPRESSURE("PIDPressure"),
    /**
     * node是否准备好
     */
    READY("Ready");

    private String type;

    NodeConditionTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
