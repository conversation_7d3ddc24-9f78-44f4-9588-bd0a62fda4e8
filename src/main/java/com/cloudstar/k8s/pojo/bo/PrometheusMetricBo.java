package com.cloudstar.k8s.pojo.bo;


import com.cloudstar.k8s.annotation.StringFormat;
import com.cloudstar.k8s.pojo.vo.GpuTotalMemResultVo;
import com.cloudstar.k8s.pojo.vo.MetricsDataResultValueVo;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.NumberUtil;
import lombok.Data;


@Data
@SuppressWarnings("checkstyle:all")
public class PrometheusMetricBo {

    private String status;
    private MetricData data;

    /**
     * 获取Gpu 使用率
     */
    public Map<String, Float> getGpuUsage() {
        Map<String, Float> gpuUsageMap = new HashMap<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return gpuUsageMap;
        }
        for (MetricResult result : data.getResult()) {
            gpuUsageMap.put(result.getMetric().getAcc_id(), Float.valueOf(result.getValue().get(1).toString()));
        }
        return gpuUsageMap;
    }

    /**
     * 获取GPU显存使用量
     */
    public Map<String, String> getGpuMemValue() {
        Map<String, String> gpuMemValueMap = new HashMap<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return gpuMemValueMap;
        }
        StringFormat memMetricsFormat = (value) -> {
            return NumberUtil.isNumber(String.valueOf(value)) ? String.valueOf(Long.valueOf(String.valueOf(value)) * 1024)
                    : String.valueOf(0);
        };
        for (MetricResult result : data.getResult()) {
            gpuMemValueMap.put(result.getMetric().getAcc_id(), memMetricsFormat.format(result.getValue().get(1).toString()));
        }
        return gpuMemValueMap;
    }

    /**
     * 获取GPU显存总大小
     */
    public List<GpuTotalMemResultVo> getGpuTotalMemValue() {
        List<GpuTotalMemResultVo> gpuTotalMemValueVOList = new ArrayList<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return gpuTotalMemValueVOList;
        }
        StringFormat memMetricsFormat = (value) -> {
            return NumberUtil.isNumber(String.valueOf(value)) ? String.valueOf(Long.valueOf(String.valueOf(value)) * 1024)
                    : String.valueOf(0);
        };
        for (MetricResult result : data.getResult()) {
            gpuTotalMemValueVOList.add(
                    new GpuTotalMemResultVo(result.getMetric().getAcc_id(), memMetricsFormat.format(result.getValue().get(1).toString())));
        }
        return gpuTotalMemValueVOList;
    }


    /**
     * 获取value 列表
     */
    public List<MetricsDataResultValueVo> getValues(StringFormat stringFormat) {
        List<MetricsDataResultValueVo> list = new ArrayList<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return list;
        }
        for (MetricResult result : data.getResult()) {
            result.getValues().forEach(obj -> {
                list.add(new MetricsDataResultValueVo(obj.get(0).toString(), stringFormat.format(obj.get(1).toString())));
            });
        }
        return list;
    }

    /**
     * 获取value 列表
     */
    public List<MetricsDataResultValueVo> getValues(MetricResult metricResult) {
        List<MetricsDataResultValueVo> list = new ArrayList<>();
        if (metricResult == null || CollectionUtils.isEmpty(metricResult.getValues())) {
            return list;
        }
        metricResult.getValues().forEach(obj -> {
            list.add(new MetricsDataResultValueVo(obj.get(0).toString(), obj.get(1).toString()));
        });
        return list;
    }

    /**
     * 获取 GPU使用率result列表
     */
    public Map<String, List<MetricsDataResultValueVo>> getGpuMetricsResults() {
        Map<String, List<MetricsDataResultValueVo>> map = new HashMap<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return map;
        }
        for (MetricResult result : data.getResult()) {
            map.put(result.getMetric().getAcc_id(), getValues(result));
        }
        return map;
    }

    /**
     * 获取value 列表
     */
    public List<MetricsDataResultValueVo> getFormatValues(MetricResult metricResult, StringFormat stringFormat) {
        List<MetricsDataResultValueVo> list = new ArrayList<>();
        if (metricResult == null || CollectionUtils.isEmpty(metricResult.getValues())) {
            return list;
        }
        metricResult.getValues().forEach(obj -> {
            list.add(new MetricsDataResultValueVo(obj.get(0).toString(), stringFormat.format(obj.get(1).toString())));
        });
        return list;
    }

    /**
     * 获取 GPU显存使用量result列表
     */
    public Map<String, List<MetricsDataResultValueVo>> getGpuMemResults() {
        Map<String, List<MetricsDataResultValueVo>> map = new HashMap<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return map;
        }
        StringFormat memMetricsFormat = (value) -> {
            return NumberUtil.isNumber(String.valueOf(value)) ? String.valueOf(Long.valueOf(String.valueOf(value)) * 1024)
                    : String.valueOf(0);
        };
        for (MetricResult result : data.getResult()) {
            map.put(result.getMetric().getAcc_id(), getFormatValues(result, memMetricsFormat));
        }
        return map;
    }

    /**
     * 获取value 列表
     */
    public String getGpuTotalValues(MetricResult metricResult, StringFormat stringFormat) {
        List<String> strings = new ArrayList<>();
        if (metricResult == null || CollectionUtils.isEmpty(metricResult.getValues())) {
            return "";
        }
        metricResult.getValues().forEach(obj -> {
            strings.add(stringFormat.format(obj.get(1).toString()));
        });
        return strings.get(0);
    }

    /**
     * 获取 GPU显存总量result列表
     */
    public List<GpuTotalMemResultVo> getGpuTotalMemResults() {
        List<GpuTotalMemResultVo> list = new ArrayList<>();
        if (data == null || CollectionUtils.isEmpty(data.getResult())) {
            return list;
        }
        StringFormat memMetricsFormat = (value) -> {
            return NumberUtil.isNumber(String.valueOf(value)) ? String.valueOf(Long.valueOf(String.valueOf(value)) * 1024)
                    : String.valueOf(0);
        };
        for (MetricResult result : data.getResult()) {
            list.add(new GpuTotalMemResultVo(result.getMetric().getAcc_id(), getGpuTotalValues(result, memMetricsFormat)));
        }
        return list;
    }

}

@Data
@SuppressWarnings("checkstyle:all")
class MetricData {

    private String resultType;
    private List<MetricResult> result;
}

@Data
@SuppressWarnings("checkstyle:all")
class MetricResult {

    private Metric metric;
    List<Object> value;
    List<List<Object>> values;
}

@Data
@SuppressWarnings("checkstyle:all")
class Metric {

    private String UUID;
    private String acc_id;
    private String pod;

    public String getAcc_id() {
        if (!StringUtils.isEmpty(acc_id)) {
            return acc_id;
        } else {
            return UUID;
        }
    }
}

