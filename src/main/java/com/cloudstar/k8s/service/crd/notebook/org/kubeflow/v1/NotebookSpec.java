package com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"template"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
@javax.annotation.processing.Generated("io.fabric8.java.generator.CRGeneratorRunner")
public class NotebookSpec implements io.fabric8.kubernetes.api.model.KubernetesResource {

    @com.fasterxml.jackson.annotation.JsonProperty("template")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.Template template;

    public com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.Template getTemplate() {
        return template;
    }

    public void setTemplate(com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.Template template) {
        this.template = template;
    }
}

