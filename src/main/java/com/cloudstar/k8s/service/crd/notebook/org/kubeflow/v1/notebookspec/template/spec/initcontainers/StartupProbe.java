package com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"exec","failureThreshold","grpc","httpGet","initialDelaySeconds","periodSeconds","successThreshold","tcpSocket","terminationGracePeriodSeconds","timeoutSeconds"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
@javax.annotation.processing.Generated("io.fabric8.java.generator.CRGeneratorRunner")
public class StartupProbe implements io.fabric8.kubernetes.api.model.KubernetesResource {

    @com.fasterxml.jackson.annotation.JsonProperty("exec")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.Exec exec;

    public com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.Exec getExec() {
        return exec;
    }

    public void setExec(com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.Exec exec) {
        this.exec = exec;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("failureThreshold")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer failureThreshold;

    public Integer getFailureThreshold() {
        return failureThreshold;
    }

    public void setFailureThreshold(Integer failureThreshold) {
        this.failureThreshold = failureThreshold;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("grpc")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.Grpc grpc;

    public com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.Grpc getGrpc() {
        return grpc;
    }

    public void setGrpc(com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.Grpc grpc) {
        this.grpc = grpc;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("httpGet")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.HttpGet httpGet;

    public com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.HttpGet getHttpGet() {
        return httpGet;
    }

    public void setHttpGet(com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.HttpGet httpGet) {
        this.httpGet = httpGet;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("initialDelaySeconds")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer initialDelaySeconds;

    public Integer getInitialDelaySeconds() {
        return initialDelaySeconds;
    }

    public void setInitialDelaySeconds(Integer initialDelaySeconds) {
        this.initialDelaySeconds = initialDelaySeconds;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("periodSeconds")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer periodSeconds;

    public Integer getPeriodSeconds() {
        return periodSeconds;
    }

    public void setPeriodSeconds(Integer periodSeconds) {
        this.periodSeconds = periodSeconds;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("successThreshold")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer successThreshold;

    public Integer getSuccessThreshold() {
        return successThreshold;
    }

    public void setSuccessThreshold(Integer successThreshold) {
        this.successThreshold = successThreshold;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("tcpSocket")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.TcpSocket tcpSocket;

    public com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.TcpSocket getTcpSocket() {
        return tcpSocket;
    }

    public void setTcpSocket(com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.initcontainers.startupprobe.TcpSocket tcpSocket) {
        this.tcpSocket = tcpSocket;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("terminationGracePeriodSeconds")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Long terminationGracePeriodSeconds;

    public Long getTerminationGracePeriodSeconds() {
        return terminationGracePeriodSeconds;
    }

    public void setTerminationGracePeriodSeconds(Long terminationGracePeriodSeconds) {
        this.terminationGracePeriodSeconds = terminationGracePeriodSeconds;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("timeoutSeconds")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer timeoutSeconds;

    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }

    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
}

