package com.cloudstar.k8s.service.impl;


import cn.hutool.core.util.StrUtil;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.config.HcsClientConfig;
import com.cloudstar.k8s.pojo.vo.requestvo.CreatePvcReq;
import com.cloudstar.k8s.service.facade.PersistentVolumeClaimService;

import org.springframework.stereotype.Service;

import java.util.HashMap;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import io.fabric8.kubernetes.api.model.PersistentVolumeClaim;
import io.fabric8.kubernetes.api.model.PersistentVolumeClaimBuilder;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.api.model.ResourceRequirements;
import io.fabric8.kubernetes.api.model.ResourceRequirementsBuilder;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;


@Service
@Slf4j
public class PersistentVolumeClaimServiceImpl implements PersistentVolumeClaimService {

    @Resource
    private ConfigService configService;

    @Resource
    private KubernetesClient kubernetesClient;


    private static final String STORAGE = "storage";

    private static final String VOLUME_TYPE = "SATA";

    private static final String READ_WRITE_ONCE = "ReadWriteOnce";

    private static final String READ_WRITE_MANY = "ReadWriteMany";

    private static String storageClassName = "csi-disk";

    static {
        if (StrUtil.isNotEmpty(System.getenv("STORAGE_CLASS_NAME"))) {
            storageClassName = System.getenv("STORAGE_CLASS_NAME");
        }
    }
    @Override
    public boolean createPvc(String namespace, CreatePvcReq req) {
        try {
            if (isExistPvc(namespace, req.getName())) {
                throw new BizException("pvc已存在");
            }
            final HashMap<String, Quantity> requestMap = new HashMap<>();
            requestMap.put(STORAGE, new Quantity(req.getSize() + "Gi"));
            ResourceRequirements resourceRequirements = new ResourceRequirementsBuilder()
                    .withRequests(requestMap).build();
            PersistentVolumeClaim pvc = new PersistentVolumeClaim();
            String clusterType = System.getenv("CLUSTER_TYPE");
            if (ClusterTypeEnum.HCSO.getType().equals(clusterType)) {
                pvc = new PersistentVolumeClaimBuilder()
                        .withNewMetadata()
                        .withName(req.getName())
                        .withNamespace(namespace)
                        .withAnnotations(new HashMap<>()).addToAnnotations("everest.io/disk-volume-type", VOLUME_TYPE)
                        .addToAnnotations("failure-domain.beta.kubernetes.io/region", getSysRegion())
                        .addToAnnotations("failure-domain.beta.kubernetes.io/zone", getSysZone())
                        .endMetadata()
                        .withNewSpec()
                        .withAccessModes(new String[]{req.getAccessMode()})
                        .withResources(resourceRequirements)
                        .withStorageClassName(storageClassName)
                        .endSpec()
                        .build();
            }
            if (ClusterTypeEnum.BMS.getType().equals(clusterType)) {
                pvc = new PersistentVolumeClaimBuilder()
                        .withNewMetadata()
                        .withName(req.getName())
                        .withNamespace(namespace)
                        .endMetadata()
                        .withNewSpec()
                        .withAccessModes(new String[]{req.getAccessMode()})
                        .withResources(resourceRequirements)
                        .withStorageClassName(storageClassName)
                        .endSpec()
                        .build();
            }
            PersistentVolumeClaim createPvc = kubernetesClient.persistentVolumeClaims().inNamespace(namespace).create(pvc);
            log.info("创建pvc成功:{}", JSONUtil.toJsonStr(createPvc));
            return true;
        } catch (Exception e) {
            log.error("创建pvc报错:{}", e);
        }
        return false;
    }

    @Override
    public boolean deletePvc(String namespace, String name) {
        try {
            return kubernetesClient.persistentVolumeClaims().inNamespace(namespace).withName(name).delete();
        } catch (Exception e) {
            log.error("删除pvc报错:{}", e);
        }
        return false;
    }

    @Override
    public boolean isExistPvc(String namespace, String name) {
        try {
            PersistentVolumeClaim createPvc = kubernetesClient.persistentVolumeClaims().inNamespace(namespace).withName(name).get();
            if (ObjectUtil.isNotEmpty(createPvc)) {
                log.info("pvc:{},已存在", name);
                return true;
            }
        } catch (Exception e) {
            log.error("判断pvc是否存在报错:{}", e);
        }
        return false;
    }

    @Override
    public boolean dilatationPvc(String namespace, CreatePvcReq req) {
        try {
            final PersistentVolumeClaim persistentVolumeClaim = kubernetesClient.persistentVolumeClaims()
                    .inNamespace(namespace)
                    .withName(req.getName())
                    .get();
            if (ObjectUtil.isEmpty(persistentVolumeClaim)) {
                throw new BizException("pvc不存在,扩容失败");
            }
            final HashMap<String, Quantity> requestMap = new HashMap<>();
            requestMap.put(STORAGE, new Quantity(req.getSize() + "Gi"));

            log.info("当前PVC原始storage大小: {}", persistentVolumeClaim.getSpec().getResources().getRequests().get("storage"));
            log.info("准备扩容至: {}", requestMap);

            // 使用JSON Patch方式只更新resources.requests字段
            String patchJson = String.format(
                "[{\"op\": \"replace\", \"path\": \"/spec/resources/requests/storage\", \"value\": \"%sGi\"}]",
                req.getSize()
            );

            PersistentVolumeClaim updatedPvc = kubernetesClient.persistentVolumeClaims()
                    .inNamespace(namespace)
                    .withName(req.getName())
                    .patch(PatchContext.of(PatchType.JSON), patchJson);

            log.info("pvc扩容成功:{}", JSONUtil.toJsonStr(updatedPvc));
            return true;
        } catch (Exception e) {
            log.error("pvc扩容报错:{}", e);
        }
        return false;
    }


    private String getSysZone() {
        HcsClientConfig config = configService.getConfig(ConfigType.HCS_CLIENT_CONFIG);
        if (ObjectUtil.isEmpty(config) || ObjectUtil.isEmpty(config.getZone().stringValue())) {
            throw new BizException("系统未配置zone");
        }
        return config.getZone().stringValue();
    }

    private String getSysRegion() {
        HcsClientConfig config = configService.getConfig(ConfigType.HCS_CLIENT_CONFIG);
        if (ObjectUtil.isEmpty(config) || ObjectUtil.isEmpty(config.getRegion().stringValue())) {
            throw new BizException("系统未配置region");
        }
        return config.getRegion().stringValue();
    }
}
