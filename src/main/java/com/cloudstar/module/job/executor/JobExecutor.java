package com.cloudstar.module.job.executor;

import com.cloudstar.common.base.enums.TrainingJobCmdEnum;
import com.cloudstar.common.base.enums.TrainingJobParamsEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.dao.mapper.cluster.ClusterEngineMapper;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.cluster.ClusterFlavorMapper;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.mapper.datastorage.DataStorageResourcesMapper;
import com.cloudstar.dao.mapper.tenantmapping.ClusterSubAccountMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.mapper.training.TrainingJobParamsMapper;
import com.cloudstar.dao.model.datastorage.DataStorageResources;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.dao.model.training.TrainingJobParams;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.grpc.AgentTrainingJobProto;
import com.cloudstar.service.grpc.AgentTrainingJobProto.dataResource.Builder;
import com.cloudstar.service.pojo.dto.trainingjob.ExecuteJobEntity;
import com.cloudstar.service.pojo.dto.trainingjob.TrainingJobMsgDto;
import com.cloudstar.service.utils.RabbitMqMsgUtil;
import com.cloudstar.service.utils.TrainingJobMsgUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public abstract class JobExecutor {

    @Autowired
    protected ServerClient serverClient;
    @Autowired
    protected DataStorageResourcesMapper dataStorageResourcesMapper;
    @Autowired
    protected TrainingJobEntityMapper trainingJobEntityMapper;
    @Autowired
    protected TrainingJobMsgUtil trainingJobMsgUtil;
    @Autowired
    protected RabbitMqMsgUtil rabbitMqMsgUtil;
    @Autowired
    protected ClusterEngineMapper clusterEngineMapper;
    @Autowired
    protected ClusterResourcePoolMapper clusterResourcePoolMapper;
    @Autowired
    protected ClusterFlavorMapper clusterFlavorMapper;
    @Autowired
    protected ClusterEntityMapper clusterEntityMapper;
    @Autowired
    protected ClusterSubAccountMapper subAccountMapper;
    @Autowired
    protected TrainingJobParamsMapper trainingJobParamsMapper;

    protected static final String COORDINATION = "synergy";

    protected static final String CPU = "CPU";

    /**
     * 执行
     *
     * @param executeJob 执行工作
     *
     * @return {@link AgentTrainingJobProto.AddTrainingJobRequest.Builder}
     */
    public abstract AgentTrainingJobProto.AddTrainingJobRequest.Builder execute(ExecuteJobEntity executeJob);

    public abstract List<AgentTrainingJobProto.ParamsGroup.Builder> extParams();

    /**
     * 查询子账户用户id
     *
     * @param trainingJob 培训工作
     */
    public void selectSubAccountUserId(TrainingJobEntity trainingJob) {
        //创建的作业子用户id
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        clusterSubAccountReqDto.setClusterId(trainingJob.getClusterId());
        clusterSubAccountReqDto.setUserSid(trainingJob.getUserSid());
        final Rest<ClusterSubAccountRespDto> createResponse = serverClient.getClusterSubAccount(
                clusterSubAccountReqDto);
        //上传数据集的子用户id
        DataStorageResources dataStorageDataSet = dataStorageResourcesMapper.selectById(
                trainingJob.getInputDataResource());
        clusterSubAccountReqDto.setClusterId(dataStorageDataSet.getClusterId());
        clusterSubAccountReqDto.setUserSid(dataStorageDataSet.getOwnerId());
        final Rest<ClusterSubAccountRespDto> updDataResponse = serverClient.getClusterSubAccount(
                clusterSubAccountReqDto);
        //上传算法文件的子用户id
        DataStorageResources dataStorageAlgorithm = dataStorageResourcesMapper.selectById(
                Objects.nonNull(trainingJob.getInputAlgorithmResource()) ? trainingJob.getInputAlgorithmResource()
                        : trainingJob.getInputImageId());
        if (ObjectUtil.isNotEmpty(dataStorageAlgorithm)) {
            clusterSubAccountReqDto.setClusterId(dataStorageAlgorithm.getClusterId());
            clusterSubAccountReqDto.setUserSid(dataStorageAlgorithm.getOwnerId());
            final Rest<ClusterSubAccountRespDto> updAlgorithmResponse = serverClient.getClusterSubAccount(
                    clusterSubAccountReqDto);
            trainingJob.setUpdAlgorithmUserId(updAlgorithmResponse.getData().getUserId());
            log.info("创建作业-上传算法文件用户id:{}", updAlgorithmResponse.getData().getUserId());
        } else {
            trainingJob.setUpdAlgorithmUserId(updDataResponse.getData().getUserId());
        }

        log.info("创建作业-创建作业用户id:{}", createResponse.getData().getUserId());
        log.info("创建作业-上传数据用户id:{}", updDataResponse.getData().getUserId());
        trainingJob.setCreateJobUserId(createResponse.getData().getUserId());
        trainingJob.setUpdDataUserId(updDataResponse.getData().getUserId());
    }

    /**
     * 获取bms账号信息
     *
     * @param trainingJob job
     */
    public void selectBmsSubAccountUserId(TrainingJobEntity trainingJob) {
        //创建的作业子用户id
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        clusterSubAccountReqDto.setClusterId(trainingJob.getClusterId());
        clusterSubAccountReqDto.setUserSid(trainingJob.getUserSid());
        final Rest<ClusterSubAccountRespDto> createResponse = serverClient.getClusterSubAccount(
                clusterSubAccountReqDto);
        log.info("创建作业-创建作业用户id:{}", createResponse.getData().getUserId());
        trainingJob.setCreateJobUserId(createResponse.getData().getUserId());
        trainingJob.setUpdDataUserId(createResponse.getData().getUserId());
        trainingJob.setUpdAlgorithmUserId(createResponse.getData().getUserId());
    }

    /**
     * 选择cmd
     *
     * @param trainingJob 培训工作
     *
     * @return {@link String}
     */
    public String selectCmd(TrainingJobEntity trainingJob) {
        if (Objects.nonNull(trainingJob.getJobType()) && Objects.isNull(trainingJob.getCoordinationJob())) {
            return TrainingJobCmdEnum.PRE_START.getType();
        } else {
            return TrainingJobCmdEnum.START.getType();
        }
    }

    /**
     * 更新错误工作
     *
     * @param trainingJob 培训工作
     * @param errorMsg 错误味精
     */
    public void updateErrorJob(TrainingJobEntity trainingJob, String errorMsg) {
        trainingJob.setStatus(TrainingJobStatusEnum.FAILED.getType());
        trainingJob.setErrorMsg(errorMsg);
        trainingJobEntityMapper.updateById(trainingJob);
        //发送消息
        TrainingJobMsgDto dto = new TrainingJobMsgDto();
        dto.setJobId(trainingJob.getId().toString());
        dto.setJobName(trainingJob.getName());
        dto.setJobStatus("调度失败");
        dto.setSendAccount(trainingJob.getCreatedBy());
        dto.setSendUserSid(trainingJob.getUserSid());
        trainingJobMsgUtil.sendMsg(dto);

        rabbitMqMsgUtil.recordJobEvent(trainingJob.getId(), TrainingJobStatusEnum.FAILED.getType(), new Date());
    }

    /**
     * 解析参数
     *
     * @param trainingJobParams 培训工作参数
     * @param builder 构建器
     * @param specBuilder 规范施工
     */
    public void parseParams(List<TrainingJobParams> trainingJobParams, AgentTrainingJobProto.AddTrainingJobRequest.Builder builder,
                            AgentTrainingJobProto.SpecObj.Builder specBuilder, TrainingJobEntity trainingJob) {
        AgentTrainingJobProto.ParamsGroup.Builder paramsGroupBuilder = AgentTrainingJobProto.ParamsGroup.newBuilder();
        for (int i = 0; i < trainingJobParams.size(); i++) {
            if (TrainingJobParamsEnum.DATA_URL.getType().equalsIgnoreCase(trainingJobParams.get(i).getName())) {
                Builder inputBuilder = AgentTrainingJobProto.dataResource.newBuilder()
                                                                         .setName(trainingJobParams.get(i).getName())
                                                                         .setDescription(TrainingJobParamsEnum.DATA_URL.getDesc())
                                                                         .setObsUrl(trainingJobParams.get(i).getValue())
                                                                         .setDataId(trainingJob.getInputDataResource());
                builder.addInputs(inputBuilder);
                // 数据集目录
                paramsGroupBuilder.setName("DATA_DIR");
                paramsGroupBuilder.setValue(trainingJobParams.get(i).getValue());
                paramsGroupBuilder.setType(TrainingJobParamsEnum.ENV_PARAMS.getType());
                builder.addParamsGroup(paramsGroupBuilder);

            } else if (TrainingJobParamsEnum.TRAIN_URL.getType().equalsIgnoreCase(trainingJobParams.get(i).getName())) {
                Builder inputBuilder = AgentTrainingJobProto.dataResource.newBuilder()
                                                                         .setName(trainingJobParams.get(i).getName())
                                                                         .setDescription(TrainingJobParamsEnum.DATA_URL.getDesc())
                                                                         .setObsUrl(trainingJobParams.get(i).getValue());
                builder.addOutputs(inputBuilder);
            } else if (TrainingJobParamsEnum.LOG_URL.getType().equalsIgnoreCase(trainingJobParams.get(i).getName())) {
                specBuilder.setLogExportPathObj(trainingJobParams.get(i).getValue());
                builder.setSpec(specBuilder);
            } else {
                paramsGroupBuilder.setName(trainingJobParams.get(i).getName());
                paramsGroupBuilder.setValue(trainingJobParams.get(i).getValue());
                paramsGroupBuilder.setType(trainingJobParams.get(i).getType());
                builder.addParamsGroup(paramsGroupBuilder);
            }
        }
        if (ObjectUtil.isNotEmpty(trainingJob.getCodeDir())) {
            paramsGroupBuilder.setName("SCRIPT_DIR");
            paramsGroupBuilder.setValue(trainingJob.getCodeDir());
            paramsGroupBuilder.setType(TrainingJobParamsEnum.ENV_PARAMS.getType());
            builder.addParamsGroup(paramsGroupBuilder);
        }

        // 设置扩展参数
        List<AgentTrainingJobProto.ParamsGroup.Builder> extParams = extParams();
        if (CollectionUtil.isNotEmpty(extParams)) {
            for (AgentTrainingJobProto.ParamsGroup.Builder param : extParams) {
                builder.addParamsGroup(param);
            }
        }
        //协同作业启动添加ip、port 环境
        if (Objects.nonNull(trainingJob.getCoordinationJob())
                && TrainingJobCmdEnum.START.getType().equals(trainingJob.getCoordinationJob().getCmd())) {
            paramsGroupBuilder.setName("IP");
            paramsGroupBuilder.setValue(trainingJob.getCoordinationJob().getIp());
            paramsGroupBuilder.setType(TrainingJobParamsEnum.ENV_PARAMS.getType());
            builder.addParamsGroup(paramsGroupBuilder);

            paramsGroupBuilder.setName("PORT");
            paramsGroupBuilder.setValue(trainingJob.getCoordinationJob().getPort());
            paramsGroupBuilder.setType(TrainingJobParamsEnum.ENV_PARAMS.getType());
            builder.addParamsGroup(paramsGroupBuilder);
        }

    }

}
