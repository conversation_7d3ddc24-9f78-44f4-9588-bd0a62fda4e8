package com.cloudstar.mq;

import com.cloudstar.common.base.constant.ClusterStatusEnum;
import com.cloudstar.common.base.constant.ClusterSyncStatusEnum;
import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.pojo.mq.ClusterSyncMessage;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.grpcservice.facade.GrpcSyncService;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 集群消费者
 *
 * <AUTHOR>
 * @date 2022/8/11 15:10
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class ClusterConsumer {

    ClusterEntityMapper clusterEntityMapper;

    /**
     * 集群同步消息监听
     *
     * @param clusterSyncMessage 消息
     */
    @RabbitListener(queues = RabbitMqConstants.SYNC_QUEUE)
    public void consumeMessage(ClusterSyncMessage clusterSyncMessage) {
        try {
            log.info("debug监听消息：{}", JSONUtil.toJsonStr(clusterSyncMessage));
            ClusterEntity entity = clusterEntityMapper.selectById(clusterSyncMessage.getClusterId());
            if (Objects.isNull(entity)) {
                log.error("集群不存在，同步失败");
            } else {
                GrpcSyncService grpcSyncService = GrpcSyncService.build(entity.getAdapterUuid());
                switch (clusterSyncMessage.getEvent()) {
                    case "cluster":
                        grpcSyncService.clusterSync(entity);
                        break;
                    case "clusterAccount":
                        grpcSyncService.clusterAccountSync(entity);
                        break;
                    case "clusterEngine":
                        grpcSyncService.clusterEngineSync(entity);
                        break;
                    case "clusterFlavor":
                        grpcSyncService.clusterFlavorSync(entity);
                        break;
                    default:
                        break;
                }
            }
            log.info("集群同步消息：集群id【{}】，操作类型【{}】", clusterSyncMessage.getClusterId(), clusterSyncMessage.getEvent());
        } catch (Exception e) {
            e.printStackTrace();
            if ("cluster".equals(clusterSyncMessage.getEvent())) {
                ClusterEntity entity = clusterEntityMapper.selectById(clusterSyncMessage.getClusterId());
                entity.setSyncStatus(ClusterSyncStatusEnum.FAIL.getStatus());
                entity.setStatus(ClusterStatusEnum.ABNORMAL.getStatus());
                clusterEntityMapper.updateById(entity);
            }
            log.error("集群同步消息处理失败：【{}】", e.getMessage());
            //手动抛出异常，触发重试机制
            throw e;
        }

    }
}
