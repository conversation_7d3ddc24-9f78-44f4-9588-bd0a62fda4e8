package com.cloudstar.mq;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.pojo.mq.MirrorEventMessage;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.grpcservice.facade.AgentMirrorService;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 镜像事件消费者
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class MirrorEventConsumer {

    ClusterEntityMapper clusterEntityMapper;

    /**
     * 数据存储资源
     *
     * @param message 消息
     */
    @RabbitListener(queues = RabbitMqConstants.MIRROR_EVENT_QUEUE)
    @RabbitHandler
    @Transactional(rollbackFor = Exception.class)
    public void mirrorEventConsumer(MirrorEventMessage message) throws Exception {
        log.info("接收到镜像事件消息：{}", JSONUtil.toJsonStr(message));
        try {
            ClusterEntity entity =
                    Optional.ofNullable(clusterEntityMapper.selectList(
                                    new LambdaQueryWrapper<ClusterEntity>()
                                            .eq(ClusterEntity::getClusterType, ClusterTypeEnum.BMS.getType()))).orElse(List.of())
                            .stream().findFirst().orElseThrow(() -> new Exception("未找到集群类型为BMS的集群"));
            AgentMirrorService agentMirrorService = AgentMirrorService.build(entity.getAdapterUuid());
            switch (message.getEvent()) {
                case SYNC:
                    agentMirrorService.syncImageList(message, entity.getId());
                    break;
                case DELETE:
                    agentMirrorService.deleteImage(message);
                    break;
                case CREATE:
                    break;
                case UPDATE:
                    break;
                default:
                    log.error("未知的镜像事件类型：{}", message.getEvent());
                    break;
            }

        } catch (Exception e) {
            log.error("处理镜像事件消息失败, 抛出异常重试：", e);
            //手动抛出异常，触发重试机制
            throw e;
        }
    }


}
