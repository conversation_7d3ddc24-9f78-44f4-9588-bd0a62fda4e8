package com.cloudstar.mq;

import cn.hutool.json.JSONUtil;
import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.VisualizedEventMessage;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.grpcservice.facade.AgentVisualizedService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 可视化事件消费者
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class VisualizedConsumer {

    ClusterEntityMapper clusterEntityMapper;

    /**
     * 数据存储资源
     *
     * @param message 消息
     */
    @RabbitListener(queues = RabbitMqConstants.VISUALIZATION_JOB_QUEUE)
    @RabbitHandler
    @Transactional(rollbackFor = Exception.class)
    public void handle(VisualizedEventMessage message) throws Exception {
        log.info("接收到可视化事件消息：{}", JSONUtil.toJsonStr(message));
        try {
            ClusterEntity entity =
                    Optional.ofNullable(clusterEntityMapper.selectById(message.getClusterId()))
                            .orElseThrow(() -> new BizException("未找到集群类型为BMS的集群"));
            AgentVisualizedService agentVisualizedService = AgentVisualizedService.build(entity.getAdapterUuid());
            switch (message.getEvent()) {
                case CREATE:
                    agentVisualizedService.create(message);
                    break;
                case START:
                    agentVisualizedService.start(message);
                    break;
                case STOP:
                    agentVisualizedService.stop(message);
                    break;
                case DELETE:
                    agentVisualizedService.delete(message);
                    break;
                default:
                    log.error("未知的可视化事件类型：{}", message.getEvent());
                    break;
            }

        } catch (Exception e) {
            log.error("处理可视化事件消息失败, 抛出异常重试：", e);
            throw e;
        }
    }


}
