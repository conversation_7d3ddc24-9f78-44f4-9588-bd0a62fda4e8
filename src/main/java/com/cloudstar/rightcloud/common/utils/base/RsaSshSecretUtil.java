package com.cloudstar.rightcloud.common.utils.base;

import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.secret.SecretKeyPair;
import com.google.common.base.Charsets;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import com.google.common.io.BaseEncoding;
import com.google.common.io.ByteSource;
import com.google.common.io.ByteStreams;
import com.google.common.io.CharStreams;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.KeyPair;
import com.jcraft.jsch.KeyPairRSA;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.security.spec.RSAPublicKeySpec;


/**
 * RSA工具类
 *
 * @author: zhangqiang
 * @date: 2023/8/7 16:29
 */
@Slf4j
public class RsaSshSecretUtil {

    /**
     * 生成Rsa SSH 密钥
     *
     * @param keySize 密钥长度
     */
    public static SecretKeyPair generateKey(int keySize) {
        int type = KeyPair.RSA;
        JSch jsch = new JSch();
        KeyPair keyPair = null;
        try {
            keyPair = KeyPairRSA.genKeyPair(jsch, type, keySize);
        } catch (JSchException e) {
            throw new BizException(e);
        }
        //私钥
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        keyPair.writePrivateKey(baos);
        String privateKeyString = baos.toString();
        SecretKeyPair secretKeyPair = new SecretKeyPair();
        secretKeyPair.setPrivateKey(privateKeyString);

        //公钥
        baos = new ByteArrayOutputStream();
        keyPair.writePublicKey(baos, keyPair.getPublicKeyComment());
        String publicKeyString = baos.toString();
        secretKeyPair.setPublicKey(publicKeyString);
        return secretKeyPair;
    }

    /**
     * 生成公钥指纹
     *
     * @param publicKeyOpenSsh 公钥
     * @return 指纹
     */
    public static String fingerprintPublicKey(String publicKeyOpenSsh) {
        RSAPublicKeySpec publicKeySpec = publicKeySpecFromOpenSsh(publicKeyOpenSsh);
        return fingerprint(publicKeySpec.getPublicExponent(), publicKeySpec.getModulus());
    }

    /**
     * publicKeySpecFromOpenSSH
     *
     * @param idRsaPub String
     * @return RSAPublicKeySpec
     */
    public static RSAPublicKeySpec publicKeySpecFromOpenSsh(String idRsaPub) {
        try {
            return publicKeySpecFromOpenSsh(ByteSource.wrap(idRsaPub.getBytes(Charsets.UTF_8)));
        } catch (IOException varTwo) {
            throw Throwables.propagate(varTwo);
        }
    }

    /**
     * publicKeySpecFromOpenSSH
     *
     * @param supplier ByteSource
     * @return RSAPublicKeySpec
     */
    public static RSAPublicKeySpec publicKeySpecFromOpenSsh(ByteSource supplier) throws IOException {
        InputStream stream = supplier.openStream();
        Iterable<String> parts = Splitter.on(' ').split(toStringAndClose(stream).trim());
        Preconditions.checkArgument(Iterables.size(parts) >= 2
                && "ssh-rsa".equals(Iterables.get(parts, 0)), "bad format, should be: ssh-rsa AAAAB3...");
        stream = new ByteArrayInputStream(BaseEncoding.base64().decode((CharSequence) Iterables.get(parts, 1)));
        String marker = new String(readLengthFirst(stream));
        Preconditions.checkArgument("ssh-rsa".equals(marker), "looking for marker ssh-rsa but got %s", marker);
        BigInteger publicExponent = new BigInteger(readLengthFirst(stream));
        BigInteger modulus = new BigInteger(readLengthFirst(stream));
        return new RSAPublicKeySpec(modulus, publicExponent);
    }

    /**
     * toStringAndClose
     *
     * @param input InputStream
     * @return String
     */
    public static String toStringAndClose(InputStream input) {
        Preconditions.checkNotNull(input, "input");
        String var1 = "";
        try {
            var1 = CharStreams.toString(new InputStreamReader(input, Charsets.UTF_8));
        } catch (IOException e) {
            log.error("error: ", e);
        } finally {
            closeQuietly(input);
        }
        return var1;
    }

    private static byte[] readLengthFirst(InputStream in) throws IOException {
        int byte1 = in.read();
        int byte2 = in.read();
        int byte3 = in.read();
        int byte4 = in.read();
        int length = (byte1 << 24) + (byte2 << 16) + (byte3 << 8) + (byte4 << 0);
        byte[] val = new byte[length];
        ByteStreams.readFully(in, val);
        return val;
    }

    private static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException ignored) {
            }

        }
    }

    public static String fingerprint(BigInteger publicExponent, BigInteger modulus) {
        byte[] keyBlob = keyBlob(publicExponent, modulus);
        return hexColonDelimited(Hashing.md5().hashBytes(keyBlob));
    }

    private static String hexColonDelimited(HashCode hc) {
        return Joiner.on(':').join(Splitter.fixedLength(2).split(BaseEncoding.base16().lowerCase().encode(hc.asBytes())));
    }

    private static byte[] keyBlob(BigInteger publicExponent, BigInteger modulus) {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            writeLengthFirst("ssh-rsa".getBytes(), out);
            writeLengthFirst(publicExponent.toByteArray(), out);
            writeLengthFirst(modulus.toByteArray(), out);
            return out.toByteArray();
        } catch (IOException e) {
            throw Throwables.propagate(e);
        }
    }

    private static void writeLengthFirst(byte[] array, ByteArrayOutputStream out) throws IOException {
        out.write(array.length >>> 24 & 255);
        out.write(array.length >>> 16 & 255);
        out.write(array.length >>> 8 & 255);
        out.write(array.length >>> 0 & 255);
        if (array.length == 1 && array[0] == 0) {
            out.write(new byte[0]);
        } else {
            out.write(array);
        }

    }

    public static void main(String[] args) {
        generateKey(2048);
    }

}
