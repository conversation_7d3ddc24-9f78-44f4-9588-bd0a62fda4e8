package com.cloudstar.rightcloud.common.utils.db;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

import lombok.extern.slf4j.Slf4j;

/**
 * db 工具类
 *
 * @author: zhangqiang
 * @date: 2023/5/29 13:41
 */
@ConditionalOnProperty(name = {"spring.datasource.dynamic.primary"})
@Configuration
@Slf4j
public class DbUtil {


    private static JdbcTemplate template = new JdbcTemplate();

    private static AtomicBoolean initialized = new AtomicBoolean(false);

    private final DataSource dataSource;

    public DbUtil(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * 初始化工具类
     */
    @PostConstruct
    public void init() {
        if (!initialized.get()) {
            initialized.set(true);
            template.setDataSource(dataSource);
            template.afterPropertiesSet();
        }
    }

    /**
     * 查询表.
     *
     * @param <T> the type parameter
     * @param sql 查询SQL
     * @param beanClazz the bean clazz
     * @param param SQL参数
     *
     * @return 结果 list
     */
    public static <T> List<T> queryBeanList(String sql, Class<T> beanClazz, Object... param) {
        return template.query(sql, BeanPropertyRowMapper.newInstance(beanClazz), param);
    }

    /**
     * 更新、删除DB数据. * @param sql SQL
     *
     * @param param 参数
     *
     * @return 影响条数 int
     */
    public static int update(String sql, Object... param) {
        return template.update(sql, param);
    }

    /**
     * 查询表.
     *
     * @param sql 查询SQL
     * @param param SQL参数
     *
     * @return 结果 map
     */
    public static Map queryMap(String sql, Object... param) {
        try {
            return template.queryForMap(sql, param);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查询表. 结果为出入列的List
     *
     * @param sql 查询SQL
     * @param col 查询列
     * @param param SQL参数
     *
     * @return 结果 list
     */
    public static List<String> queryColumnList(String sql, String col, Object... param) {
        SqlRowSet queryForRowSet = template.queryForRowSet(sql, param);
        final List<String> result = new ArrayList<>();
        while (queryForRowSet.next()) {
            result.add(queryForRowSet.getString(col));
        }
        return result;
    }

}
