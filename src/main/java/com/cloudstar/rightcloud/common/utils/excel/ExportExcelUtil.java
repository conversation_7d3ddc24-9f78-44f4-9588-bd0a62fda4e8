package com.cloudstar.rightcloud.common.utils.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.properties.CommonProperties;
import com.cloudstar.rightcloud.common.utils.dict.DictCode;
import com.cloudstar.rightcloud.common.utils.dict.DictCodeUtil;
import com.cloudstar.rightcloud.common.utils.message.PropertyMessageUtil;
import com.cloudstar.rightcloud.common.utils.spring.SpringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFDataValidationConstraint;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.RandomAccess;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * excel导出工具类
 *
 * <AUTHOR>
 * @date 2023/5/5
 **/
@Slf4j
public class ExportExcelUtil {

    private static Logger logger = LoggerFactory.getLogger(ExportExcelUtil.class);

    /**
     * 导出文件类型阈值
     */
    private static final Long EXPORT_MAX_SIZE = SpringUtils.getBean(CommonProperties.class).getMaxDataSize();

    /**
     * 默认格式化时间规则
     */
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 生成excel文件后缀
     */
    private static final String DEFAULT_EXCEL_SUFFIX = ".xlsx";

    /**
     * 压缩文件后缀
     */
    private static final String ZIP_SUFFIX = ".zip";


    /**
     * 根据传入字段，解析@ExcelField，生成列字段和列名映射
     *
     * @param fieldList 字段列表
     * @param tClass    @ExcelField所有在类字节码对象
     * @return 列字段和列名映射关系
     */
    public static Map<String, String> createColumnName(List<String> fieldList, Class<?> tClass, Locale locale) throws BizException {
        try {
            if (CollectionUtil.isEmpty(fieldList)) {
                return new HashMap<>();
            }
            return findExcelTitle(fieldList, tClass, locale);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
    }


    /**
     * 根据传入字段，解析@ExcelField，生成列字段和列名映射
     *
     * @param fieldList 字段列表
     * @param tClass    @ExcelField所有在类字节码对象
     * @return 列字段和列名映射关系
     */
    public static Map<String, String> createColumnName(List<String> fieldList, Class<?> tClass) throws BizException {
        try {
            Locale currentLocaleLanguage = LocaleContextHolder.getLocale();
            return findExcelTitle(fieldList, tClass, currentLocaleLanguage);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 解析@ExcelField，生成列字段和列名映射
     *
     * @param tClass @ExcelField所有在类字节码对象
     * @return 列字段和列名映射关系
     */
    public static Map<String, String> createColumnName(Class<?> tClass) throws BizException {
        try {
            Locale currentLocaleLanguage = LocaleContextHolder.getLocale();
            return createColumnName(tClass, currentLocaleLanguage);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
    }


    /**
     * 解析@ExcelField，生成列字段和列名映射
     *
     * @param tClass @ExcelField所有在类字节码对象
     * @return 列字段和列名映射关系
     */
    public static Map<String, String> createColumnName(Class<?> tClass, Locale locale) throws BizException {
        try {
            List<String> fieldList = new ArrayList<>();
            Field[] fields = tClass.getDeclaredFields();
            for (Field field : fields) {
                ExcelField annotation = field.getAnnotation(ExcelField.class);
                if (Objects.nonNull(annotation)) {
                    fieldList.add(field.getName());
                }
            }
            return createColumnName(fieldList, tClass, locale);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
    }


    /**
     * 导出数据列表
     *
     * @param dataList 数据列表
     * @param titleMap 字段名和列名映射
     * @param fileName 文件名，不需要后缀
     * @return 字节数组
     */
    public static byte[] expExcel(List<?> dataList, Map<String, String> titleMap, String fileName) throws BizException {
        try {
            checkData(dataList);
            if (StringUtils.isBlank(fileName)) {
                throw new BizException("The file name cannot be empty");
            }
            return exportSimpleExcelByMethod(dataList, new ArrayList<>(titleMap.keySet()), titleMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }


    /**
     * 导出数据列表且写入到response
     *
     * @param dataList 数据列表
     * @param titleMap 字段名和列名映射
     * @param fileName 文件名，不需要后缀
     * @param response 响应体
     */
    public static void expExcelAndWrite(List<?> dataList, Map<String, String> titleMap, String fileName,
                                        HttpServletResponse response) throws BizException {
        try {
            checkData(dataList);
            if (StringUtils.isBlank(fileName)) {
                throw new BizException("The file name cannot be empty");
            }
            byte[] bytes = exportSimpleExcelByMethod(dataList, new ArrayList<>(titleMap.keySet()), titleMap);
            write(bytes, rename(fileName), response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }

    /**
     * 根据模板文件导出excel（根据键值对导出数据）
     *
     * @param tplFileName classpath下模板文件路径，如/resource/export/template.xls
     * @param beans       数据对象，不能为空
     * @return 文件byte数组
     * @throws BizException 异常
     */
    public static byte[] expExcelByTemp(String tplFileName, Map<String, Object> beans) throws BizException {
        try {
            return exportExcel(tplFileName, beans);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }

    /**
     * 根据模板文件导出excel且写入到response
     *
     * @param tplFileName classpath下模板文件路径，如/resource/export/template.xls
     * @param beans       数据对象，不能为空
     * @param response    响应体
     * @throws BizException 异常
     */
    public static void expExcelByTempAndWrite(String tplFileName, String fileName, Map<String, Object> beans,
                                              HttpServletResponse response) throws BizException {
        if (StringUtils.isBlank(tplFileName)) {
            throw new BizException("The template file path cannot be empty");
        }
        if (MapUtil.isEmpty(beans)) {
            throw new BizException("Data cannot be empty");
        }
        byte[] bytes = expExcelByTemp(tplFileName, beans);
        write(bytes, rename(fileName), response);
    }

    /**
     * 根据动态指定列名生成 动态导入模板
     */
    public static byte[] expExcelImpTemps(Map<String, Map<String, ExcelHeader>> titleNameMaps) throws BizException {
        try {
            return exportImpTemplateByTitleNames(titleNameMaps);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }

    /**
     * 根据动态指定列名生成 动态导入模板
     *
     * @param titleNames 列名映射map（key为字段名，value为字段描述）
     * @return 字节数组
     * @throws BizException 导出异常
     */
    public static byte[] expExcelImpTemp(LinkedHashMap<String, ExcelHeader> titleNames) throws BizException {
        try {
            return exportImpTemplateByTitleName(titleNames);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }


    /**
     * 根据动态指定列名生成 动态导入模板
     *
     * @param sheetDataModelList 要导出的sheet数据模型（包含表头和表体）
     * @return 字节数组
     * @throws BizException 导出异常
     */
    public static byte[] expExcelImpTemp(List<SheetDataModel<Map<String, Object>>> sheetDataModelList) throws BizException {
        return expExcelImpTemp(sheetDataModelList, Locale.CHINESE);
    }

    /**
     * 根据动态指定列名生成 动态导入模板
     *
     * @param sheetDataModelList 要导出的sheet数据模型（包含表头和表体）
     * @return 字节数组
     * @throws BizException 导出异常
     */
    public static byte[] expExcelImpTemp(List<SheetDataModel<Map<String, Object>>> sheetDataModelList, Locale locale) throws BizException {
        LocaleContextHolder.setDefaultLocale(locale);
        try {
            return exportImpTemplateBySheetData(sheetDataModelList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }

    /**
     * 根据动态指定列名生成 导入模板
     *
     * @param titleNames 列名映射map（key为字段名，value为字段描述）
     * @param fileName   文件名
     * @param response   响应体
     * @throws BizException 导出异常
     */
    public static void expExcelImpTempAndWrite(LinkedHashMap<String, ExcelHeader> titleNames, String fileName,
                                               HttpServletResponse response) throws BizException {
        write(expExcelImpTemp(titleNames), rename(fileName), response);
    }

    /**
     * 根据多sheet模型数据动态生成 导入模板（适用需导出编辑后导入场景）
     *
     * @param sheetDataModelList 要导出的sheet数据模型（包含表头和表体）
     * @param fileName           文件名
     * @param response           响应体
     * @throws BizException 导出异常
     */
    public static void expExcelImpTempAndWrite(List<SheetDataModel<Map<String, Object>>> sheetDataModelList, String fileName,
                                               HttpServletResponse response) throws BizException {

        write(expExcelImpTemp(sheetDataModelList), rename(fileName), response);
    }

    /**
     * 根据多sheet模型数据动态生成 导入模板（适用需导出编辑后导入场景）
     */
    public static void expExcelImpTempAndWrites(Map<String, Map<String, ExcelHeader>> titleNames, String fileName,
                                                HttpServletResponse response) throws BizException {
        write(expExcelImpTemps(titleNames), rename(fileName), response);
    }


    /**
     * 将字节写到response中去
     *
     * @param bytes    字节
     * @param fileName 文件名称
     * @param response 响应体
     * @throws BizException 异常
     */
    public static void write(byte[] bytes, String fileName, HttpServletResponse response) throws BizException {
        try {
            setResponse(response, fileName);
            ServletOutputStream out = response.getOutputStream();
            out.write(bytes);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException("Exporting Excel failed: " + e.getMessage());
        }
    }

    /**
     * 设置响应头
     *
     * @param response     响应头
     * @param destFileName excel名称
     */
    private static void setResponse(HttpServletResponse response, String destFileName) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(destFileName, UTF_8));
        response.setContentType("application/octet-stream");
    }


    /**
     * 导出简单excel列表
     *
     * @param dataList        数据列表
     * @param keyList         json值的key
     * @param customTitleName 自定义列名
     * @return excel byte数组
     */
    private static byte[] exportSimpleExcelByMethod(List<?> dataList, List<String> keyList, Map<String, String> customTitleName) throws Exception {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        getWorkbookByMethod(dataList, keyList, customTitleName).write(bos);
        return bos.toByteArray();
    }


    /**
     * 获取POI主对象
     *
     * @param dataList        数据列表
     * @param keyList         json值的key
     * @param customTitleName 自定义列名
     * @return workbook对象
     */
    private static Workbook getWorkbookByMethod(List<?> dataList, List<String> keyList, Map<String, String> customTitleName) throws Exception {
        if (!(keyList instanceof RandomAccess)) {
            keyList = new ArrayList<>(keyList);
        }
        final List<String> finalList = keyList;
        // 构建excel表头
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("sheet1");
        Row titleRow = sheet.createRow(0);
        CellStyle titleCellStyle = getTitleCellStyle(workbook);
        for (int i = 0; i < keyList.size(); i++) {
            Cell titleRowCell = titleRow.createCell(i);
            titleRowCell.setCellStyle(titleCellStyle);
            String titleName = null;
            if (customTitleName != null) {
                titleName = customTitleName.get(keyList.get(i));
            }
            if (titleName == null) {
                throw new BizException("field[" + keyList.get(i) + "]corresponding title is not set");
            }
            // if (titleName == null) {
            //     titleName = findExcelTitle(methodMap, fieldMap, keyList.get(i));
            // }
            titleRowCell.setCellValue(titleName);
            sheet.setColumnWidth(i, titleName.getBytes().length * 512);
        }
        if (CollectionUtil.isEmpty(dataList)) {
            return workbook;
        }
        Object o = dataList.get(0);
        // 判断是否为数据入参map类型
        boolean isMap = o instanceof Map;
        // 获取class信息
        Map<String, Method> methodMap = null;
        Map<String, Field> fieldMap = null;
        List<DictCode> codes = null;
        if (!isMap) {
            Class<?> clazz = dataList.get(0).getClass();
            methodMap = getClassMethodMap(clazz);
            fieldMap = getClassFieldMap(clazz);
            // 获取字典数据
            List<String> codeList = fieldMap.values().stream().filter(a -> finalList.contains(a.getName())).map(a -> {
                ExcelField annotation = a.getAnnotation(ExcelField.class);
                return (annotation == null || StrUtil.isBlank(annotation.dictCode())) ? null : annotation.dictCode();
            }).filter(Objects::nonNull).collect(Collectors.toList());
            codes = DictCodeUtil.getCodes(codeList);
        }
        Map<Integer, DateTimeFormatter> temporalFormatMap = new HashMap<>(16);
        Map<Integer, SimpleDateFormat> dateFormatMap = new HashMap<>(16);
        ListIterator<?> dataIterator = dataList.listIterator();
        while (dataIterator.hasNext()) {
            int rowNum = dataIterator.nextIndex() + 1;
            Object data = dataIterator.next();
            Row row = sheet.createRow(rowNum);
            for (int columnNum = 0; columnNum < keyList.size(); columnNum++) {
                Object value;
                String name = keyList.get(columnNum);
                if (isMap) {
                    Map map = (Map) data;
                    value = map.get(name);
                } else {
                    Method method = methodMap.get(name);
                    value = method.invoke(data);
                    Field field = fieldMap.get(name);
                    // 转换字典描述值
                    ExcelField annotation = field.getAnnotation(ExcelField.class);
                    if (value != null && annotation != null && StrUtil.isNotBlank(annotation.dictCode())) {
                        String codeDesc = DictCodeUtil.getDesc(codes, annotation.dictCode(), String.valueOf(value));
                        if (StrUtil.isNotBlank(codeDesc)) {
                            value = codeDesc;
                        }
                    }
                }
                if (value == null) {
                    continue;
                }
                // 自动换行
                Cell cell = row.createCell(columnNum);
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cell.setCellStyle(cellStyle);
                if (value instanceof CharSequence) {
                    cell.setCellValue(value.toString());
                } else if (value instanceof Number) {
                    if (value instanceof Long) {
                        // long类型避免科学计数法，转换成string
                        DecimalFormat df = new DecimalFormat("0");
                        cell.setCellValue(df.format(value));
                    } else {
                        cell.setCellValue(((Number) value).doubleValue());
                    }
                } else if (value instanceof TemporalAccessor) {
                    cell.setCellValue(getColumnTemporalFormat(temporalFormatMap, fieldMap, keyList, columnNum)
                            .format((TemporalAccessor) value));
                } else if (value instanceof Date) {
                    cell.setCellValue(getColumnDateFormat(dateFormatMap, fieldMap, keyList, columnNum).format(value));
                } else {
                    cell.setCellValue(value.toString());
                }
            }

        }
        return workbook;
    }


    /**
     * 当前title样式
     */
    private static CellStyle getTitleCellStyle(Workbook workbook) {
        Font font = workbook.createFont();
        CellStyle cellStyle = workbook.createCellStyle();
        //font.setBold(true);
        cellStyle.setFont(font);
        return cellStyle;
    }

    /**
     * 获取excel标题
     *
     * @param fieldList 字段列表
     * @param tClass    字节码对象
     * @return 映射关系
     */
    private static Map<String, String> findExcelTitle(List<String> fieldList, Class<?> tClass, Locale locale) throws Exception {
        Map<String, Field> fieldMap = getClassFieldMap(tClass);
        Map<String, String> fieldNameMap = new LinkedHashMap<>();
        for (String key : fieldList) {
            Field field = fieldMap.get(key);
            if (field == null) {
                throw new BizException("field[" + key + "]not found");
            }
            ExcelField titleName = field.getAnnotation(ExcelField.class);
            // 默认获取英文描述，如果没有声明注解则取字段名
            if (titleName == null || StringUtils.isBlank(titleName.propertyKey())) {
                throw new BizException("field[" + key + "] annotation not found or propertyKey value is null");
            }
            String annotationValue = PropertyMessageUtil.getProperty(titleName.propertyKey(), locale);
            if (annotationValue == null) {
                throw new BizException("The import field [" + field.getName() + "] has not been configured with "
                        + "internationalized data");
            }
            fieldNameMap.put(key, annotationValue);
        }
        return fieldNameMap;
    }

    /**
     * 获取当前class所有的字段名
     *
     * @param clazz 字节码对象
     */
    private static Map<String, Field> getClassFieldMap(Class<?> clazz) {
        List<Field> fieldList = new LinkedList<>();
        while (clazz != null) {
            Field[] declaredFields = clazz.getDeclaredFields();
            if (declaredFields.length != 0) {
                fieldList.addAll(Arrays.asList(declaredFields));
            }
            clazz = clazz.getSuperclass();
        }
        Map<String, Field> fieldMap = new HashMap<>();
        for (Field field : fieldList) {
            fieldMap.putIfAbsent(field.getName(), field);
        }
        return fieldMap;
    }

    /**
     * 获取字节码对象中的方法列表
     *
     * @param clazz 字节码对象
     */
    private static Map<String, Method> getClassMethodMap(Class<?> clazz) {
        Method[] methods = clazz.getMethods();
        Map<String, Method> methodMap = new HashMap<>();
        for (Method method : methods) {
            String methodName = method.getName();
            if (methodName.startsWith("get") && methodName.length() > 3) {
                methodMap.putIfAbsent(methodName.substring(3, 4).toLowerCase().concat(methodName.substring(4)), method);
            }
        }
        return methodMap;
    }

    /**
     * 获取列格式
     *
     * @param temporalFormatMap 时间格式
     * @param fieldMap          字段map
     * @param methodKey         方法名
     * @param columnNum         列名
     */
    private static DateTimeFormatter getColumnTemporalFormat(Map<Integer, DateTimeFormatter> temporalFormatMap, Map<String, Field> fieldMap,
                                                             List<String> methodKey, int columnNum) {
        DateTimeFormatter dtf = temporalFormatMap.get(columnNum);
        if (dtf == null) {
            Field field = fieldMap.get(methodKey.get(columnNum));
            if (field != null) {
                JsonFormat jsonFormat = field.getAnnotation(JsonFormat.class);
                if (jsonFormat != null) {
                    dtf = DateTimeFormatter.ofPattern(jsonFormat.pattern());
                }
            }
            if (dtf == null) {
                dtf = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
            }
            temporalFormatMap.put(columnNum, dtf);
        }
        return dtf;
    }


    /**
     * 日期格式转换
     */
    private static SimpleDateFormat getColumnDateFormat(Map<Integer, SimpleDateFormat> dateFormatMap, Map<String, Field> fieldMap,
                                                        List<String> methodKey, int columnNum) {
        SimpleDateFormat sdf = dateFormatMap.get(columnNum);
        if (sdf == null) {
            Field field = fieldMap.get(methodKey.get(columnNum));
            if (field != null) {
                JsonFormat jsonFormat = field.getAnnotation(JsonFormat.class);
                if (jsonFormat != null) {
                    sdf = new SimpleDateFormat(jsonFormat.pattern());
                }
            }
            if (sdf == null) {
                sdf = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
            }
            dateFormatMap.put(columnNum, sdf);
        }
        return sdf;
    }


    /**
     * 根据指定列名生成 动态导入模板
     *
     * @param titleNames 字段和字段描述映射（key是字段名，值为描述字段）
     * @return 文件byte数组
     * @throws BizException 异常
     */
    private static byte[] exportImpTemplateByTitleName(Map<String, ExcelHeader> titleNames) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("sheet1");
        Row titleRow = sheet.createRow(0);
        // 列下标
        int cell = 0;
        for (Map.Entry<String, ExcelHeader> entry : titleNames.entrySet()) {
            String key = entry.getKey();
            ExcelHeader header = entry.getValue();
            Cell titleRowCell = titleRow.createCell(cell);

            CellStyle titleCellStyle = getTitleCellStyle(workbook);

            // 设置下拉框数据
            if (CollectionUtil.isNotEmpty(header.getDropDownBox())) {
                setDropDownBox(sheet, cell, header.getDropDownBox());
            }

            // 是否必填标红显示
            if (header.getRequired()) {
                XSSFFont font = workbook.createFont();
                font.setColor(Font.COLOR_RED);
                titleCellStyle.setFont(font);
            }

            // 设置批注
            titleRowCell.setCellComment(createCellComment(sheet, cell, key, titleRowCell));
            titleRowCell.setCellValue(header.getColumnName());

            // 设置样式
            sheet.setColumnWidth(cell, header.getColumnName().getBytes().length * 512);
            titleRowCell.setCellStyle(titleCellStyle);
            cell++;
        }

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        return bos.toByteArray();
    }


    /**
     * 根据指定列名生成 动态导入模板
     *
     * @param titleNameMaps 字段和字段描述映射（key是字段名，值为描述字段）
     * @return 文件byte数组
     * @throws BizException 异常
     */
    private static byte[] exportImpTemplateByTitleNames(Map<String, Map<String, ExcelHeader>> titleNameMaps) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        for (Map.Entry<String, Map<String, ExcelHeader>> item : titleNameMaps.entrySet()) {
            XSSFSheet sheet = workbook.createSheet(item.getKey());
            Row titleRow = sheet.createRow(0);
            // 列下标
            int cell = 0;
            for (Map.Entry<String, ExcelHeader> entry : item.getValue().entrySet()) {
                String key = entry.getKey();
                ExcelHeader header = entry.getValue();
                Cell titleRowCell = titleRow.createCell(cell);

                CellStyle titleCellStyle = getTitleCellStyle(workbook);

                // 设置下拉框数据
                if (CollectionUtil.isNotEmpty(header.getDropDownBox())) {
                    setDropDownBox(sheet, cell, header.getDropDownBox());
                }

                // 是否必填标红显示
                if (header.getRequired()) {
                    XSSFFont font = workbook.createFont();
                    font.setColor(Font.COLOR_RED);
                    titleCellStyle.setFont(font);
                }

                // 设置批注
                titleRowCell.setCellComment(createCellComment(sheet, cell, key, titleRowCell));
                titleRowCell.setCellValue(header.getColumnName());

                // 设置样式
                sheet.setColumnWidth(cell, header.getColumnName().getBytes().length * 512);
                titleRowCell.setCellStyle(titleCellStyle);
                cell++;
            }

        }
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        return bos.toByteArray();
    }

    /**
     * 根据指定sheet集合数据生成带数据的模板
     *
     * @param sheetDataModelList 字段和字段描述映射（key是字段名，值为描述字段）
     * @return 文件byte数组
     * @throws BizException 异常
     */
    private static byte[] exportImpTemplateBySheetData(List<SheetDataModel<Map<String, Object>>> sheetDataModelList) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        // sheet data
        if (null == sheetDataModelList || sheetDataModelList.isEmpty()) {
            throw new BizException("Exported Excel data cannot be empty！");
        }
        ExcelHeader header;
        for (SheetDataModel<Map<String, Object>> sheetDataModel : sheetDataModelList) {
            // 创建工作簿
            Sheet sheet = workbook.createSheet(sheetDataModel.getSheelName());
            // 行号
            int rowNum = 0;
            // 创建行头
            Row titleRow = sheet.createRow(rowNum);
            for (int i = 0; i < sheetDataModel.getHeaders().size(); i++) {
                header = sheetDataModel.getHeaders().get(i);
                createHeader(header, workbook, sheet, titleRow, i);
            }
            List<String> collect = sheetDataModel.getHeaders().stream().map(ExcelHeader::getColumnKey).collect(Collectors.toList());
            // 写入业务数据
            for (Map<String, Object> map : sheetDataModel.getDataList()) {
                Row row = sheet.createRow(++rowNum);
                createContent(workbook, row, map, collect);
            }
        }
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        return bos.toByteArray();
    }

    /**
     * 创建表头
     *
     * @param header   ExcelHeader
     * @param workbook HSSFWorkbook
     * @param sheet    Sheet
     * @param titleRow Row
     * @param i        int
     */
    private static void createHeader(ExcelHeader header, Workbook workbook, Sheet sheet, Row titleRow, int i) {
        Cell titleRowCell = titleRow.createCell(i);
        CellStyle titleCellStyle = getTitleCellStyle(workbook);
        // 设置下拉框数据
        if (CollectionUtil.isNotEmpty(header.getDropDownBox())) {
            setDropDownBox(sheet, i, header.getDropDownBox());
        }
        // 是否必填标红显示
        if (header.getRequired()) {
            XSSFFont font = (XSSFFont) workbook.createFont();
            font.setColor(Font.COLOR_RED);
            titleCellStyle.setFont(font);
        }
        final Cell cell = titleRow.getCell(i);
        // 设置批注
        titleRowCell.setCellComment(createCellComment(sheet, i, header.getColumnKey(), cell));
        if (StrUtil.isBlank(header.getColumnName())) {
            String columnName = PropertyMessageUtil.getProperty(header.getColumnKey());
            // 读取国际化配置文件
            titleRowCell.setCellValue(columnName);
            // 设置样式
            sheet.setColumnWidth(i, columnName.getBytes().length * 512);
        } else {
            // 动态做国际化
            Locale currentLocaleLanguage = LocaleContextHolder.getLocale();
            if ("zh".equals(currentLocaleLanguage.getLanguage())) {
                titleRowCell.setCellValue(header.getColumnName());
                // 设置样式
                sheet.setColumnWidth(i, header.getColumnName().getBytes().length * 512);
            } else {
                titleRowCell.setCellValue(header.getColumnNameEn());
                // 设置样式
                sheet.setColumnWidth(i, header.getColumnNameEn().getBytes().length * 512);
            }
        }
        titleRowCell.setCellStyle(titleCellStyle);
    }

    /**
     * 创建正文
     *
     * @param row        Row
     * @param data       Map
     * @param keysEnList List
     */
    private static void createContent(XSSFWorkbook workbook,
                                      Row row,
                                      Map<String, Object> data,
                                      List<String> keysEnList) {
        Object valueObj = null;
        String value = null;
        for (int j = 0; j < keysEnList.size(); j++) {
            Cell cell = row.createCell(j);
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setWrapText(true);
            cell.setCellStyle(cellStyle);
            if (Objects.isNull(data.get(keysEnList.get(j)))) {
                //如果是null的话设置为空
                cell.setCellValue("");
                continue;
            }
            if (Objects.nonNull(data.get(keysEnList.get(j)))) {
                cell.setCellValue(data.get(keysEnList.get(j)).toString());
                continue;
            }
            // 值按对应类型转换处理
            valueObj = data.get(keysEnList.get(j));
            if (valueObj instanceof String) {
                value = (String) valueObj;
            } else if (valueObj instanceof BigDecimal) {
                value = valueObj.toString();
            } else if (valueObj instanceof Date) {
                value = DateUtil.format((Date) valueObj, DatePattern.NORM_DATETIME_PATTERN);
            } else if (valueObj instanceof Double) {
                DecimalFormat df = new DecimalFormat("##0.000");
                value = df.format(valueObj);
            } else {
                value = valueObj != null ? valueObj.toString() : "";
            }
            // 如果格式化Map为空，默认为字符串格式
            cell.setCellValue(value);
        }
    }


    /**
     * 创建批注
     *
     * @param sheet 页
     * @param cell  列
     * @param name  批注值
     * @return 批注对象
     */
    private static Comment createCellComment(Sheet sheet, int cellIndex, String name, Cell cell) {
        if (sheet instanceof HSSFSheet) {
            // 开始创建批注
            Drawing<?> drawing = sheet.createDrawingPatriarch();
            Comment comment = drawing.createCellComment(new HSSFClientAnchor(0, 0, 0, 0,
                    (short) cellIndex, 0, (short) cellIndex, 0));
            //设置批注默认不显示
            comment.setVisible(false);
            // 输入批注信息（批注就是字段名）
            comment.setString(new HSSFRichTextString(name));
            return comment;
        }
        // 开始创建批注
        Drawing<?> drawing = sheet.createDrawingPatriarch();
        Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0,
                cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex() + 2,
                cell.getRowIndex() + 2));
        //设置批注默认不显示
        comment.setVisible(false);
        // 输入批注信息（批注就是字段名）
        comment.setString(new XSSFRichTextString(name));
        return comment;
    }


    /**
     * 根据模板文件导出excel
     */
    private static byte[] exportExcel(String tplFileName, Map<String, Object> beans) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            // 获取模板文件流
            inputStream = Objects.requireNonNull(ExportExcelUtil.class.getClassLoader().getResourceAsStream(tplFileName));
            out = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(inputStream).excelType(ExcelTypeEnum.XLSX).build();
            // 开始写sheet，easyExcel针对list类型的数据填充必须要单独设置值，在这里先拆分原先的数据bean
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 填充配置，该配置是避免list渲染的数据覆盖模版里面的数据，而是把模版的数据往下面移
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 普通对象填充
            Map<String, Object> map = new HashMap<>();
            for (Map.Entry<String, Object> entry : beans.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof List) {
                    // 官方强制要求list类型数据填充必须要单独声明一次
                    excelWriter.fill(new FillWrapper(entry.getKey(), (List<?>) value), fillConfig, writeSheet);
                } else {
                    map.put(entry.getKey(), entry.getValue());
                }
            }
            excelWriter.fill(map, writeSheet);
            excelWriter.finish();
            byteArrayInputStream = new ByteArrayInputStream(out.toByteArray());
            return getBytesByInput(byteArrayInputStream);
        } catch (Throwable e) {
            throw new BizException("An error occurred while generating the Excel file！", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(byteArrayInputStream);
            IOUtils.closeQuietly(out);
        }
    }


    /**
     * 将输出流转为输入流
     *
     * @param out 流
     * @return 输入流
     */
    private static ByteArrayInputStream parseOutputToInput(OutputStream out) throws Exception {
        ByteArrayOutputStream baos = (ByteArrayOutputStream) out;
        return new ByteArrayInputStream(baos.toByteArray());
    }

    /**
     * 通过输入流获取字节数组
     *
     * @param inputStream 流
     * @return 字节
     */
    private static byte[] getBytesByInput(InputStream inputStream) throws IOException {
        BufferedInputStream bufin = new BufferedInputStream(inputStream);
        int buffSize = 1024;
        ByteArrayOutputStream out = new ByteArrayOutputStream(buffSize);
        byte[] temp = new byte[buffSize];
        int size = 0;
        while ((size = bufin.read(temp)) != -1) {
            out.write(temp, 0, size);
        }
        bufin.close();
        return out.toByteArray();
    }

    /**
     * 设置下拉框
     *
     * @param sheet 页
     * @param cell  列
     * @param list  下拉框列表
     */
    private static void setDropDownBox(Sheet sheet, int cell, List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        if (sheet instanceof XSSFSheet) {
            XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);
            // 加载下拉列表内容
            XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
                    .createExplicitListConstraint(list.toArray(new String[]{}));
            // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10000, cell, cell);
            // 数据有效性对象
            XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
            return;
        }
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(1, 10000, cell, cell);
        // 加载下拉列表内容
        DVConstraint constraint = DVConstraint.createExplicitListConstraint(list.toArray(new String[]{}));
        // 数据有效性对象
        HSSFDataValidation dataValidationList = new HSSFDataValidation(regions, constraint);
        sheet.addValidationData(dataValidationList);
    }

    /**
     * 设置下拉框
     *
     * @param sheet 页
     * @param cell  列
     * @param list  下拉框列表
     */
    private static void setDropDownBox(XSSFSheet sheet, int cell, List<String> list) {
        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
        XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
                .createExplicitListConstraint(list.toArray(new String[]{}));
        CellRangeAddressList addressList = new CellRangeAddressList(1, 10000, cell, cell); //下拉选框作用范围,从第2行到第101行，从第1列到第1列
        XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(
                dvConstraint, addressList);
        validation.setShowErrorBox(true);        //输入非法内容弹窗提示
        sheet.addValidationData(validation);
    }


    /**
     * 文件重命名
     *
     * @param fileName 文件名
     * @return 文件名称
     */
    public static String rename(String fileName) {
        return fileName.concat("_")
                .concat(String.valueOf(System.currentTimeMillis()))
                .concat(RandomUtil.randomString(5))
                .concat(DEFAULT_EXCEL_SUFFIX);
    }

    /**
     * 文件重命名为zip后缀
     *
     * @param fileName 文件名
     * @return 文件名称
     */
    public static String renameCompress(String fileName) {
        return fileName.concat("_")
                .concat(String.valueOf(System.currentTimeMillis()))
                .concat(RandomUtil.randomString(5))
                .concat(ZIP_SUFFIX);
    }


    /**
     * 检查待导出数据
     *
     * @param dataList 待导出数据
     */
    private static void checkData(List<?> dataList) {
        if (dataList != null && dataList.size() > EXPORT_MAX_SIZE) {
            throw new BizException("The current export data exceeds the maximum value [" + EXPORT_MAX_SIZE + "], "
                    + "please narrow the export scope");
        }
    }

}
