package com.cloudstar.rightcloud.common.utils.tree;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.cloudstar.rightcloud.common.pojo.tree.TreeNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 树结构逻辑： 第一步：查找父节点,当父节点为空采用默认父节点,
 * 循环查找当前父节点名没有上级结点为止，
 * 认为当前节点为根级父节点
 *
 * <AUTHOR>
 * @date 2018-11-09
 */
public class TreeBuilder {

    private static final String DEFAULT_ROOT_ID = "root";

    /**
     * 获取树对象的根节点以及节点初始树结构
     **/
    public static List<TreeNode> buildTreeNode(List<?> datas, String nodeIdProperty,
                                               String nodeNameProperty, String parentNodeIdProperty,
                                               String iconNameProperty, String specialProperty) {
        return buildTreeNode(datas,
                nodeIdProperty,
                nodeNameProperty,
                parentNodeIdProperty,
                iconNameProperty,
                specialProperty, null);
    }

    /**
     * 获取树对象的根节点以及节点初始树结构
     *
     * @param datas                原始记录集
     * @param nodeIdProperty       子节点标识属性名称
     * @param nodeNameProperty     子节点名称属性名称
     * @param parentNodeIdProperty 父节点标识属性名称
     * @param iconNameProperty     主键属性名称
     * @param specialProperty      需要记录的特殊属性
     */
    @SuppressWarnings("unchecked")
    public static <T> List<TreeNode> buildTreeNode(List<T> datas, String nodeIdProperty,
                                                   String nodeNameProperty, String parentNodeIdProperty,
                                                   String iconNameProperty, String specialProperty,
                                                   Function<T, ?> valueProperty) {
        List<TreeNode> nodes = new ArrayList<>();

        Map<Object, List<Object>> parentChildMap = new HashMap<>();
        Map<Object, Object> parentNodeIdMap = new HashMap<>();
        Map<Object, Object> childNodeIdMap = new HashMap<>();
        for (Iterator it = datas.iterator(); it.hasNext(); ) {
            Object target = it.next();
            final Object nodeId = BeanUtil.getFieldValue(target, nodeIdProperty);
            Object parentNodeId = BeanUtil.getFieldValue(target, parentNodeIdProperty);
            if (null == parentNodeId || StringUtils.isBlank(String.valueOf(parentNodeId))) {
                //父节点为空默认为父节点
                parentNodeId = DEFAULT_ROOT_ID;
            }

            if (parentChildMap.containsKey(parentNodeId)) {
                parentChildMap.get(parentNodeId).add(target);
            } else {
                List<Object> childList = new ArrayList<>();
                childList.add(target);
                parentChildMap.put(parentNodeId, childList);
            }
            parentNodeIdMap.put(parentNodeId, parentNodeId);
            childNodeIdMap.put(nodeId, nodeId);
        }
        for (Map.Entry<Object, Object> entry : childNodeIdMap.entrySet()) {
            Object key = entry.getKey();
            parentNodeIdMap.remove(key);
        }
        List<Object> rootIdList = new ArrayList<>();
        rootIdList.addAll(parentNodeIdMap.values());

        if (CollectionUtil.isNotEmpty(rootIdList)) {
            for (Object rootId : rootIdList) {
                List<Object> childList = parentChildMap.get(rootId);
                if (CollectionUtil.isNotEmpty(childList)) {
                    for (Object target : childList) {
                        TreeNode treeNode = makeTreeNode(nodeIdProperty, nodeNameProperty, iconNameProperty,
                                specialProperty, valueProperty, parentChildMap, target);
                        nodes.add(treeNode);
                    }
                }
            }
        }

        return nodes;

    }

    /**
     * parentChildMap为二层结构
     *
     * @param treeNode treeNode
     * @param childList childList
     * @param parentChildMap parentChildMap
     * @param nodeIdProperty nodeIdProperty
     * @param nodeNameProperty nodeNameProperty
     * @param iconNameProperty iconNameProperty
     * @param specialProperty specialProperty
     */
    @SuppressWarnings("unchecked")
    private static void buildTreeNode(TreeNode treeNode, List<Object> childList,
                                      Map<Object, List<Object>> parentChildMap, String nodeIdProperty,
                                      String nodeNameProperty, String iconNameProperty, String specialProperty,
                                      Function valueProperty) {

        for (Object target : childList) {

            TreeNode childNode = makeTreeNode(nodeIdProperty, nodeNameProperty, iconNameProperty, specialProperty,
                    valueProperty,
                    parentChildMap, target);

            treeNode.getChildren().add(childNode);
        }

    }

    private static TreeNode makeTreeNode(String nodeIdProperty, String nodeNameProperty, String iconNameProperty,
                                         String specialProperty, Function valueProperty,
                                         Map<Object, List<Object>> parentChildMap, Object target) {
        TreeNode treeNode = new TreeNode();
        Object targetId = BeanUtil.getFieldValue(target, nodeIdProperty);
        Object targetName = BeanUtil.getFieldValue(target, nodeNameProperty);

        treeNode.setId(String.valueOf(targetId));
        treeNode.setName(String.valueOf(targetName));
        if (StringUtils.isNotBlank(specialProperty)) {
            Object special = BeanUtil.getFieldValue(target, specialProperty);
            treeNode.setSpecial(Objects.toString(special, ""));

        }
        if (StringUtils.isNotBlank(iconNameProperty)) {
            Object icon = BeanUtil.getFieldValue(target, iconNameProperty);
            treeNode.setIcon(Objects.toString(icon, ""));
        }
        if (valueProperty != null) {
            Object v = valueProperty.apply(target);
            treeNode.setValue(v);
        }
        if (parentChildMap.containsKey(targetId)) {
            List<Object> clist = parentChildMap.get(targetId);
            buildTreeNode(treeNode, clist, parentChildMap, nodeIdProperty, nodeNameProperty,
                    iconNameProperty, specialProperty, valueProperty);
        }
        return treeNode;
    }

    /**
     * 使用原有对象中的 children生成树形结构
     *
     * @param list        数据
     * @param primaryKey  获取主id
     * @param parentKey   获取上一级id, 上一级id为0 或者 null 则判断是顶级目录
     * @param setChildren 设置chidren的方法
     */
    public static <T, R> List<T> buildTree(List<T> list, Function<T, R> primaryKey, Function<T, R> parentKey,
                                           BiConsumer<T, List<T>> setChildren) {
        return buildTree(list, primaryKey, parentKey, setChildren, null);
    }

    /**
     * 使用原有对象中的 children生成树形结构
     *
     * @param list        数据
     * @param primaryKey  获取主id
     * @param parentKey   获取上一级id, 上一级id为0 或者 null 则判断是顶级目录
     * @param setChildren 设置chidren的方法
     * @param consumer    装饰对象
     */
    public static <T, R> List<T> buildTree(List<T> list, Function<T, R> primaryKey, Function<T, R> parentKey,
                                           BiConsumer<T, List<T>> setChildren,
                                           Consumer<T> consumer) {
        List<T> result = new ArrayList<>();
        // 生成第一层
        list.stream().filter(t -> {
            R v = parentKey.apply(t);
            return isTopNode(v);
        }).forEach(result::add);

        // 遍历剩余节点
        result.forEach(tn -> handleNextNode(tn, list, primaryKey, parentKey, setChildren, consumer));
        return result;
    }


    /**
     * 是否是顶级节点的数据
     *
     * @param v v
     */
    private static <R> boolean isTopNode(R v) {
        return Objects.isNull(v) || Objects.equals(v, "") || "0".equals(v.toString()) || "-1".equals(v.toString());
    }

    private static <T, R> void handleNextNode(T node, List<T> datas, Function<T, R> primaryKey,
                                              Function<T, R> parentKey, BiConsumer<T, List<T>> setChildren,
                                              Consumer<T> consumer) {
        if (consumer != null) {
            consumer.accept(node);
        }
        // 上一级节点的子节点
        List<T> childs = new ArrayList<>();
        datas.stream().filter(n -> {
            // 当前值 和上一级值对比
            // 父级 主id
            R pt = primaryKey.apply(node);
            // 子级父id
            R ft = parentKey.apply(n);

            return pt.equals(ft);

        }).forEach(childs::add);
        if (childs.isEmpty()) {
            return;
        }
        setChildren.accept(node, childs);
        childs.forEach(n -> handleNextNode(n, datas, primaryKey, parentKey, setChildren, consumer));
    }

}
