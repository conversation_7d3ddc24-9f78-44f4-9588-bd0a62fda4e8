package com.cloudstar.rightcloud.data.datascope.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据过滤
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataFilter {

    /**
     * 业务表别名
     */
    String tableAlias() default "";

    /**
     * 业务表组织ID
     */
    String orgId() default "org_id";

    /**
     * 所有者
     */
    String ownerAccount() default "created_by";

    /**
     * 项目ID
     */
    String projectId() default "project_id";

    /**
     * 忽略项目权限过滤, 当过滤表中无项目字段时，该值需设置为true
     * 支持项目过滤权限时，设置为false
     */
    boolean ignoreProjectFilter() default false;

    /**
     * 当前用户只拥有项目角色时，设置true 包含上级组织数据
     */
    boolean containParentOrg() default false;

    /**
     * 大表数据权限过滤
     */
    boolean bigDatafilter() default false;

    /**
     * 云环境ID
     */
    String cloudEnvId() default "";

    /**
     * userId对应字段当作userSid使用，而不是account
     **/
    boolean userIdAsValue() default false;

    /**
     * 忽略仅个人数据权限
     */
    boolean ignoreUserScope() default false;

    /**
     * or 第二组织id列
     */
    String orOrgId() default "";

    /**
     * or 第二项目id列
     */
    String orProjectId() default "";

    /**
     * 所有者id列
     */
    String orOwnerAccount() default "";
}
