package com.cloudstar.rightcloud.log.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cloudstar.rightcloud.common.constant.base.HeaderConstant;
import com.cloudstar.rightcloud.common.pojo.user.AuthUser;
import com.cloudstar.rightcloud.common.utils.LocaleLanguageContextUtil;
import com.cloudstar.rightcloud.common.utils.auth.AuthUserHolderUtil;
import com.cloudstar.rightcloud.common.utils.base.IpAddressUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;
import com.cloudstar.rightcloud.common.utils.spring.SpringUtils;
import com.cloudstar.rightcloud.common.utils.web.WebUtil;
import com.cloudstar.rightcloud.log.component.context.OperationLogRecordContext;
import com.cloudstar.rightcloud.log.component.processor.HandleOperationLogService;
import com.cloudstar.rightcloud.log.component.processor.dto.OperationLogBaseDto;
import com.cloudstar.rightcloud.log.data.dao.ActionLogDao;
import com.cloudstar.rightcloud.log.data.mapper.OperationLogUtilMapper;
import com.cloudstar.rightcloud.log.pojo.SysUser;
import com.cloudstar.rightcloud.log.service.param.ActionLogParam;
import com.cloudstar.rightcloud.log.service.param.LogChangeInfo;
import com.cloudstar.rightcloud.log.service.service.ActionLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 操作日志工具类
 *
 * <AUTHOR> Lesao
 * @date : 2023/4/7
 */
@Slf4j
public class ActionLogUtil {


    private static final Logger LOGGER = LoggerFactory.getLogger(ActionLogUtil.class);

    /**
     * 初始化启动的线程
     */
    @Value("${cloudstar.handleLog.poolSize:2}")
    private static int handleLogPoolSize;
    /**
     * 日志处理线程池
     */
    @Resource
    private ThreadPoolTaskExecutor handleLogExecutor;
    /**
     * 批量存储日志阈值
     */
    @Value("${cloudstar.handleLog.handleThreshold:10}")
    private int handleThreshold;
    /**
     * 获取日志数据最长等待时间
     */
    @Value("${cloudstar.handleLog.waitingTime:5}")
    private int waitingTime;
    /**
     * 线程是否运行
     */
    private final boolean threadRun = true;

    /**
     * 操作日志ip地址key
     */
    private static final String REMOTE_IP_KEY = "REMOTE_IP_KEY";

    /**
     * 记录当前操作日志（默认获取当前登陆人信息）
     *
     * @param objectName 日志操作对象
     * @param objectId   日志操作对象Id
     * @param type       日志类型
     * @param source     日志来源
     * @param detail     日志详情
     */
    public static void log(String objectName, String objectId, String type, String source, String detail, Object... propertyKeys) {
        try {
            if (isFeign()) {
                return;
            }
            ActionLogParam actionLogParam = buildLog(objectName, type, source);
            nullToEmpty(propertyKeys);
            actionLogParam.setDetail(MessageUtil.getMessage(detail, Locale.CHINA, propertyKeys));
            actionLogParam.setDetailEn(MessageUtil.getMessage(detail, Locale.US, propertyKeys));
            actionLogParam.setObjectId(objectId);
            actionLogParam.setSuccess(Boolean.TRUE);
            OperationLogBaseDto logBaseDto = BeanUtil.copyProperties(actionLogParam, OperationLogBaseDto.class);
            HandleOperationLogService handleOperationLogService = SpringUtils.getBean("handleOperationLogService");
            handleOperationLogService.handleLog(logBaseDto);
        } catch (Exception e) {
            LOGGER.error("Logging failed, errorMessage: {}", e.getMessage());
        }
    }

    /**
     * 根据日志对象记录
     *
     * @param actionLogParam 日志操作对象
     */
    @Deprecated
    public static void log(ActionLogParam actionLogParam) {
        try {
            assertLogParam(actionLogParam.getObject(), actionLogParam.getObjectId(), actionLogParam.getType(),
                    actionLogParam.getSource(), actionLogParam.getDetail());
            SpringUtils.getBean(ActionLogService.class).save(actionLogParam);
        } catch (Exception e) {
            LOGGER.error("Logging failed", e);
        }
    }


    /**
     * 在线程执行过程中，从线程上下文中获取操作日志对象，往里面填充参数
     * 注意：此方法必须配合@OperationLog注解一起使用，否则无法生效
     *
     * @param objectName 日志操作对象
     * @param objectId   日志操作对象Id
     */
    public static void logParam(String objectName, String objectId) {
        try {
            logParam(objectName, objectId, null);
        } catch (Exception e) {
            LOGGER.error("Logging failed, errorMessage: {}", e.getMessage());
        }
    }

    /**
     * 在线程执行过程中，从线程上下文中获取操作日志对象，往里面填充参数
     * 注意：此方法必须配合@OperationLog注解一起使用，否则无法生效
     *
     * @param objectName   日志操作对象
     * @param objectId     日志操作对象Id
     * @param detail       日志详情对应消息常量
     * @param propertyKeys 详情信息参数
     */
    public static void logParam(String objectName, String objectId, String detail, Object... propertyKeys) {
        try {
            OperationLogBaseDto operationLogBaseDto = OperationLogRecordContext.getLocalOperationLogContext();
            if (operationLogBaseDto == null) {
                LOGGER.warn("The log parameter body does not exist");
                return;
            }
            operationLogBaseDto.setObjectName(objectName);
            operationLogBaseDto.setObjectId(objectId);
            if (StrUtil.isNotBlank(detail)) {
                nullToEmpty(propertyKeys);
                operationLogBaseDto.setMsg(MessageUtil.getMessage(detail, Locale.CHINA, propertyKeys));
                operationLogBaseDto.setMsgEn(MessageUtil.getMessage(detail, Locale.US, propertyKeys));
            }
        } catch (Exception e) {
            LOGGER.error("Logging failed, errorMessage: {}", e.getMessage());
        }
    }

    /**
     * 根据入参删除日志
     *
     * @param ids ids
     */
    public static void deleteLog(List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return;
            }
            SpringUtils.getBean(ActionLogDao.class).deleteByIds(ids);
        } catch (Exception e) {
            LOGGER.error("delete log failed, errorMessage: {}", e.getMessage());
        }
    }

    private static void nullToEmpty(Object[] propertyKeys) {
        for (int i = 0; i < propertyKeys.length; i++) {
            if (ObjUtil.isNull(propertyKeys[i])) {
                propertyKeys[i] = LocaleLanguageContextUtil.isEn() ? "not have" : "无";
            }
        }
    }


    /**
     * 自定义操作对象
     * 注意：当前方法是取的上下文缓存中的log对象，如果当前线程执行入口（controller）尚未声明@OperationLog注解，则不会生效
     *
     * @param objectId   日志操作对象id
     * @param objectName 日志操作对象名称
     */
    public static void setObject(String objectId, String objectName) {
        try {
            OperationLogBaseDto localOperationLogContext = OperationLogRecordContext.getLocalOperationLogContext();
            if (localOperationLogContext == null) {
                return;
            }
            if (StrUtil.isNotBlank(objectId)) {
                localOperationLogContext.setObjectId(objectId);
            }
            if (StrUtil.isNotBlank(objectName)) {
                localOperationLogContext.setObjectName(objectName);
            }
        } catch (Exception e) {
            LOGGER.error("action log set object failed", e);
        }
    }


    /**
     * 记录当前操作异常日志
     *
     * @param objectName 日志操作对象
     * @param objectId   日志操作对象Id
     * @param type       日志类型
     * @param source     日志来源
     * @param errorMsg   错误消息
     * @param detail     日志详情
     */
    public static void error(String objectName, String objectId, String type, String source,
                             String errorMsg, String detail, Object... propertyKeys) {
        try {
            if (isFeign()) {
                return;
            }
            ActionLogParam actionLogParam = buildLog(objectName, type, source);
            actionLogParam.setErrorMsg(StrUtil.isNotBlank(errorMsg) ? errorMsg : MessageUtil.getMessage(detail, Locale.US, propertyKeys));
            nullToEmpty(propertyKeys);
            actionLogParam.setDetail(MessageUtil.getMessage(detail, Locale.CHINA, propertyKeys));
            actionLogParam.setDetailEn(MessageUtil.getMessage(detail, Locale.US, propertyKeys));
            actionLogParam.setObjectId(objectId);
            actionLogParam.setSuccess(Boolean.FALSE);
            OperationLogBaseDto logBaseDto = BeanUtil.copyProperties(actionLogParam, OperationLogBaseDto.class);
            HandleOperationLogService handleOperationLogService = SpringUtils.getBean("handleOperationLogService");
            handleOperationLogService.handleLog(logBaseDto);
        } catch (Exception e) {
            LOGGER.error("Logging failed, errorMessage: {}", e.getMessage());
        }
    }

    /**
     * 记录当前操作异常日志
     *
     * @param logChangeInfo 日志记录变更信息
     */
    @Deprecated
    public static <T> void logChangeInfo(LogChangeInfo<T> logChangeInfo) {
        try {
            assertLogParam(logChangeInfo.getObject(), logChangeInfo.getObjectId(),
                    logChangeInfo.getType(), logChangeInfo.getSource(), logChangeInfo.getDetail());
            ActionLogParam actionLogParam = buildLog(logChangeInfo.getObject(), logChangeInfo.getType(), logChangeInfo.getSource());
            actionLogParam.setChangeBefore(JSONObject.toJSONString(logChangeInfo.getChangeBefore()));
            actionLogParam.setChangeAfter(JSONObject.toJSONString(logChangeInfo.getChangeAfter()));
            actionLogParam.setDetail(logChangeInfo.getDetail());
            actionLogParam.setSuccess(Boolean.TRUE);
            SpringUtils.getBean(ActionLogService.class).save(actionLogParam);
        } catch (Exception e) {
            LOGGER.error("Logging failed", e);
        }
    }

    /**
     * 设置当前登录用户, 资源创建回调handler专用
     *
     * @param operator 操作人
     */
    public static void setAuthUser(String operator) {
        if (StrUtil.isBlank(operator)) {
            return;
        }
        OperationLogUtilMapper logUtilMapper = SpringUtils.getBean(OperationLogUtilMapper.class);
        SysUser userByAccount = logUtilMapper.findUserByAccount(operator);
        AuthUser authUser = BeanUtil.copyProperties(userByAccount, AuthUser.class);
        authUser.setCurrentOrgId(userByAccount.getCompanyId());
        AuthUserHolderUtil.setAuthUser(authUser);
    }


    /**
     * 设置当前登录用户, 资源创建回调handler专用
     *
     * @param operator 操作人
     */
    public static void setAuthUser(String operator, Long orgId) {
        if (StrUtil.isBlank(operator)) {
            return;
        }
        OperationLogUtilMapper logUtilMapper = SpringUtils.getBean(OperationLogUtilMapper.class);
        SysUser userByAccount = logUtilMapper.findUserByAccount(operator);
        AuthUser authUser = BeanUtil.copyProperties(userByAccount, AuthUser.class);
        authUser.setCurrentOrgId(orgId);
        AuthUserHolderUtil.setAuthUser(authUser);
    }

    /**
     * 构建日志对象
     *
     * @param objectName 日志操作对象
     * @param type       日志类型
     * @param source     日志来源
     */
    private static ActionLogParam buildLog(String objectName, String type, String source) {
        ActionLogParam actionLog = buildLogBaseInfo(objectName, source);
        actionLog.setObject(MessageUtil.getMessage(objectName));
        actionLog.setType(MessageUtil.getMessage(type, Locale.CHINA));
        actionLog.setTypeEn(MessageUtil.getMessage(type, Locale.US));
        actionLog.setSource(MessageUtil.getMessage(source, Locale.CHINA));
        actionLog.setSourceEn(MessageUtil.getMessage(source, Locale.US));
        return actionLog;
    }

    /**
     * 构建日志对象基本信息
     *
     * @return 日志对象
     */
    private static ActionLogParam buildLogBaseInfo(String objectName, String source) {
        AuthUser authUser = AuthUserHolderUtil.getAuthUser();
        // 封装组织信息
        OperationLogUtilMapper operationLogUtilMapper = SpringUtils.getBean(OperationLogUtilMapper.class);
        // 第一次登陆无用户信息
        if (ObjectUtil.isNull(authUser) && "actionlog.source.login".equals(source)) {
            SysUser userByAccount = operationLogUtilMapper.findUserByAccount(objectName);
            authUser = BeanUtil.copyProperties(userByAccount, AuthUser.class);
        } else if (ObjUtil.isNull(authUser)) {
            LOGGER.info("Current User is null");
            return new ActionLogParam();
        }
        HttpServletRequest request = WebUtil.getRequest();
        String remoteIp = MDC.get(REMOTE_IP_KEY);
        String urlPath = "";
        String httpMethod = "";
        if (ObjUtil.isNotNull(request)) {
            remoteIp = IpAddressUtil.getRemoteHostIp(request);
            urlPath = request.getRequestURI();
            httpMethod = request.getMethod();
        }
        ActionLogParam actionLog =
                ActionLogParam.builder()
                        .orgId(ObjectUtils.isNotEmpty(authUser.getCurrentOrgId()) ? authUser.getCurrentOrgId() : null)
                        .userId(ObjectUtils.isNotEmpty(authUser.getUserId()) ? authUser.getUserId() : null)
                        .username(ObjectUtils.isNotEmpty(authUser.getAccount()) ? authUser.getAccount() : "User")
                        .sourceIp(remoteIp)
                        .lbIp(remoteIp)
                        //请求的路径
                        .urlPath(urlPath)
                        //默认操作成功
                        .httpMethod(httpMethod)
                        .createdBy(ObjectUtils.isNotEmpty(authUser.getAccount()) ? authUser.getAccount() : null)
                        .updatedBy(ObjectUtils.isNotEmpty(authUser.getAccount()) ? authUser.getAccount() : null)
                        .createdDt(new Date())
                        .updatedDt(new Date())
                        .build();

        SysUser user = operationLogUtilMapper.getUserById(authUser.getUserId());
        if (user != null) {
            Long orgId = actionLog.getOrgId() != null ? actionLog.getOrgId() : user.getCompanyId();
            String orgName = operationLogUtilMapper.selectOrgNameByOrgId(orgId);
            actionLog.setOrgId(orgId);
            actionLog.setOrgName(orgName);
        }
        actionLog.setCreatedBy(authUser.getAccount());
        actionLog.setUpdatedBy(authUser.getAccount());
        actionLog.setCreatedDt(new Date());
        actionLog.setUpdatedDt(new Date());
        return actionLog;
    }

    /**
     * 检查日志参数
     *
     * @param objectName 日志操作对象
     * @param objectId   日志操作对象Id
     * @param type       日志类型
     * @param source     日志来源
     * @param detail     日志详情
     */
    private static void assertLogParam(String objectName, String objectId, String type, String source, String detail) {
        BizAssertUtils.notBlank(objectName, "The log operation objectName cannot be empty");
        BizAssertUtils.notBlank(objectId, "The log operation objectId cannot be empty");
        BizAssertUtils.notNull(type, "The log type cannot be empty");
        BizAssertUtils.notNull(source, "The log source cannot be empty");
        BizAssertUtils.notNull(detail, "The log detail cannot be empty");
    }

    /**
     * 判断是否是feign调用
     *
     * @return {@link Boolean }
     */
    private static Boolean isFeign() {
        // 来源是feign的接口不处理
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        if (ObjectUtil.isNull(sra)) {
            return true;
        }
        HttpServletRequest request = sra.getRequest();
        String feignFlagHeader = request.getHeader(HeaderConstant.USE_FEIGN);
        return BooleanUtil.toBoolean(feignFlagHeader);
    }

}
