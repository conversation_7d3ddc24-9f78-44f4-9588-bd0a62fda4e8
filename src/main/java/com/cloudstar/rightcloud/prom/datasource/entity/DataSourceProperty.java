package com.cloudstar.rightcloud.prom.datasource.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 数据源
 *
 * @author: wanglang
 * @date: 2024/4/11 18:31
 */
@Data
public class DataSourceProperty {

    /**
     * 名称
     */
    private String name;

    /**
     * URL
     */
    private String url;

    /**
     * 写入接口
     */
    @JsonProperty("write_addr")
    private String writeAddr;

    /**
     * 超时(单位:ms)
     */
    private int timeout;

    /**
     * Basic Auth用户名
     */
    private String user;

    /**
     * Basic Auth密码
     */
    private String password;

    /**
     * 是否默认
     */
    private boolean defaults;

    /**
     * http配置
     */
    private TlsConfig tls;


    @Data
    public static class TlsConfig {
        /**
         * 跳过ssl验证
         */
        @JsonProperty("skip_tls_verify")
        private boolean skipSslVerify = true;
    }
}
