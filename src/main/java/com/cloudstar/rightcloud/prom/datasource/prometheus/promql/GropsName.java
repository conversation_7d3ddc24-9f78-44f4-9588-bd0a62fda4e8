package com.cloudstar.rightcloud.prom.datasource.prometheus.promql;

import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.annotation.PromqlName;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.annotation.Tag;
import lombok.Data;

import java.util.Date;

/**
 * 测试
 *
 * @author: wanglang
 * @date: 2023/4/17 2:12 PM
 */
@PromqlName("node_cpu_info")
@Data
public class GropsName {
    private String instance;
    private String name;
    private String age;
    private String number;

    /**
     * 采集指标值
     */
    private Object value;

    /**
     * 资源实例id
     */
    @Tag
    private String resourceInstanceId;

    /**
     * 云环境ID
     */
    @Tag
    private String envId;

    private Date time;

    /**
     * 资源uuid
     */
    @Tag
    private String resourceId;

    /**
     * 指标id
     */
    @Tag
    private String metricId;

    /**
     * 组织id
     */
    @Tag
    private String orgId;

    /**
     * 资源类型
     */
    @Tag
    private String resourceType;

    @Tag
    private String job;

    /**
     * 策略
     */
    private String policy;

    /**
     * 类型
     */
    private String type;
}
