package com.cloudstar.rightcloud.prom.datasource.prometheus.promql.fc;


import com.cloudstar.rightcloud.prom.datasource.prometheus.enums.PrometheusExpr;

/**
 * avg函数
 *
 * @author: wanglang
 * @date: 2023/4/19 3:07 PM
 */
public interface AvgFunction<C, R> {
    /**
     * 平均值
     */
    C avg(C c);

    /**
     * 平均值
     */
    C avg(C c, String value);

    /**
     * 平均值
     */
    C avg(C c, String value, PrometheusExpr expr);

    /**
     * 平均值
     */
    C avg(String name);


    /**
     * 平均值
     */
    C avg(String name, String value, C c);

    /**
     * 平均值
     */
    C avg(String name, C c);

    /**
     * 平均值
     */
    C avg(String name, PrometheusExpr expr, String value);

    /**
     * 平均值
     */
    C avg(String name, PrometheusExpr expr, String value, C c);


    /**
     * 平均值
     */
    C avgOverTime(C c);

    /**
     * 平均值
     */
    C avgOverTime(String name);

    /**
     * 平均值
     */
    C avgOverTime(C c, String value);

    /**
     * 平均值
     */
    C avgOverTime(C c, String value, PrometheusExpr expr);


    /**
     * 平均值
     */
    C avgOverTime(String name, String value, C c);

    /**
     * 平均值
     */
    C avgOverTime(String name, C c);

    /**
     * 平均值
     */
    C avgOverTime(String name, PrometheusExpr expr, String value);

    /**
     * 平均值
     */
    C avgOverTime(String name, PrometheusExpr expr, String value, C c);


}
