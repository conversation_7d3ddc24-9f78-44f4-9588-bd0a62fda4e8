package com.cloudstar.rightcloud.prom.util;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * list utils
 *
 * @author: wanglang
 * @date: 2023/1/12 3:05 PM
 */
public class CollectionUtils {
    private static final int MAX_POWER_OF_TWO = 1 << (Integer.SIZE - 2);


    /**
     * 合并map
     */
    public static <K, V> V computeIfAbsent(Map<K, V> concurrentHashMap, K key, Function<? super K, ? extends V> mappingFunction) {
        V v = concurrentHashMap.get(key);
        if (v != null) {
            return v;
        }
        return concurrentHashMap.computeIfAbsent(key, mappingFunction);
    }

    /**
     * 根据预期大小创建HashMap.
     *
     * @param expectedSize 预期大小
     * @param <K>          K
     * @param <V>          V
     * @return HashMap
     * @see com.google.common.collect.Maps#newHashMapWithExpectedSize
     * @since 3.4.0
     */
    public static <K, V> HashMap<K, V> newHashMapWithExpectedSize(int expectedSize) {
        return new HashMap<>(capacity(expectedSize));
    }

    private static int capacity(int expectedSize) {
        if (expectedSize < 3) {
            if (expectedSize < 0) {
                throw new IllegalArgumentException("expectedSize cannot be negative but was: " + expectedSize);
            }
            return expectedSize + 1;
        }
        if (expectedSize < MAX_POWER_OF_TWO) {
            // This is the calculation used in JDK8 to resize when a putAll
            // happens; it seems to be the most conservative calculation we
            // can make.  0.75 is the default load factor.
            return (int) ((float) expectedSize / 0.75F + 1.0F);
        }
        return Integer.MAX_VALUE; // any large value
    }
}
