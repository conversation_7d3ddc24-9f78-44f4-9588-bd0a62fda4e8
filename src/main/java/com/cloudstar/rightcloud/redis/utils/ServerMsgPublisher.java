package com.cloudstar.rightcloud.redis.utils;


import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.RandomUtil;
import com.cloudstar.rightcloud.common.utils.json.JacksonUtils;
import com.cloudstar.rightcloud.redis.websoket.pojo.ServerMsg;
import com.cloudstar.rightcloud.redis.websoket.support.ServerMsgType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * ServerMsgPublisher
 *
 * @author: zhangqiang
 * @date: 2022/11/21 11:53
 */

@Component
@Slf4j
public class ServerMsgPublisher {

    public static final String WEBSOCKET_CHANNEL = "push-message-channel";
    private static final String BIZ_BROKER = "/topic/";
    private static final String USER_BROKER = "/msg";

    private static final Snowflake SNOW_FLAKE;


    static {
        SNOW_FLAKE = new Snowflake(RandomUtil.randomInt(1, 30), RandomUtil.randomInt(1, 30), false);
    }

    /**
     * 发送消息给用户
     *
     * @param userid 用户id
     * @param serverMsg 消息内容
     */
    public static void sendMsgToUser(String userid, ServerMsg serverMsg) {
        if (Objects.isNull(userid)) {
            log.warn("UserID is null. Message is {}", serverMsg.toString());
            return;
        }
        sendMsg("/user/" + userid + USER_BROKER, serverMsg);
    }

    /**
     * 发送消息给对应类型
     */
    public static void sendMsgToResourceType(String resType, String refId) {
        if (Objects.isNull(resType)) {
            log.warn("ResType is null. Message is {}", refId);
            return;
        }
        sendMsg(BIZ_BROKER + resType, refId);
    }

    /**
     * 发送消息给对应类型
     */
    public static void sendMsgToDetail(ServerMsgType resType, String refId, Object message) {
        if (Objects.isNull(resType)) {
            log.warn("ServerMsgType is null. Message is {}", JacksonUtils.toJsonString(message));
            return;
        }
        String destination = BIZ_BROKER + resType.getTypeFamily() + "/" + refId;
        sendMsg(destination, message);
    }

    /**
     * 发送消息
     */
    public static void sendMsg(String destination, Object serverMsg) {
        sendBroadcast(destination, serverMsg);
    }

    /**
     * 发送消息
     */
    private static void sendBroadcast(String destination, Object serverMsg) {
        //ConsumerMsg consumerMsg = ConsumerMsg.builder()
        //                                     .destination(destination)
        //                                     .serverMsg(serverMsg)
        //                                     .msgId(SNOW_FLAKE.nextId())
        //                                     .build();
        //JedisUtil.instance().publish(WEBSOCKET_CHANNEL, JacksonUtils.toJsonString(consumerMsg));
    }

}
