package com.cloudstar.rightcloud.schedule.facade.executor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cloudstar.rightcloud.common.constant.cache.CacheConstant;
import com.cloudstar.rightcloud.common.utils.spring.SpringUtils;
import com.cloudstar.rightcloud.utils.tasklog.TaskHelper;
import com.cloudstar.rightcloud.redis.utils.JedisUtil;
import com.cloudstar.rightcloud.schedule.facade.exception.TaskExecuteException;
import com.cloudstar.rightcloud.schedule.facade.executor.controller.form.TaskExecuteParam;
import com.cloudstar.rightcloud.schedule.facade.executor.service.TaskExecuteService;
import com.cloudstar.rightcloud.schedule.facade.pojo.TaskRegisterParam;
import com.cloudstar.rightcloud.schedule.facade.utils.TaskInvokeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 执行任务
 *
 * <AUTHOR> Lesao
 */
@Service
@Slf4j
public class TaskExecuteServiceImpl implements TaskExecuteService {

    @Resource
    private ThreadPoolTaskExecutor executeScheduleExecutor;

    @Override
    public void execute(TaskExecuteParam taskExecuteParam) {
        // 1.根据jobKey从缓存中获取待执行方法信息
        String jobKey = taskExecuteParam.getJobKey();
        String s = JedisUtil.instance().get(CacheConstant.TASK_KEY_PREFIX + jobKey);
        if (s == null) {
            throw new TaskExecuteException(jobKey, "The task registration information cannot be found for the "
                    + "current job key【"
                    + taskExecuteParam.getJobKey()
                    + "】");
        }
        TaskRegisterParam registerParam = JSONObject.parseObject(s, TaskRegisterParam.class);
        //获取执行的taskBean
        Object invokeTaskBean = SpringUtils.getBean(registerParam.getBeanName());
        //获取执行的taskMethodName
        String methodName = registerParam.getMethodName();
        //获取执行的方法参数
        List<String> methodParam = taskExecuteParam.getMethodParam();
        // 避免feign连接超时，异步执行
        executeScheduleExecutor.submit(() -> {
            try {
                //异步需要重新开启链路追踪
                TaskHelper.startTrace(taskExecuteParam.getSerialNumber());
                long l = System.currentTimeMillis();
                TaskInvokeUtil.invokeMethod(invokeTaskBean, methodName, methodParam);
                log.info("[Scheduled Task] The task 【"
                                + taskExecuteParam.getJobKey()
                                + "】 is executed successfully：Total time "
                                + "consumed：{}",
                        System.currentTimeMillis() - l + "ms");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                TaskHelper.fail(e.getMessage());
            } finally {
                TaskHelper.clear();
            }
        });
    }


    /**
     * 执行前
     */
    @Override
    public void executeBefore(TaskExecuteParam taskExecuteParam) {
        // 变更状态
        TaskHelper.startTrace(taskExecuteParam.getSerialNumber());
        // do something...
    }


    /**
     * 执行后
     */
    @Override
    public void executeAfter(TaskExecuteParam taskExecuteParam) {
        // do something...
    }


}
