package com.cloudstar.rightcloud.schedule.facade.pojo;

/**
 * 触发器类型枚举
 *
 * <AUTHOR>
 */
public enum TriggerTypeEnum {


    CRON("cron", "表达式类型"),

    SIMPLE("simple", "普通类型");

    /**
     * 状态
     */
    private final String value;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 枚举类构造方法
     *
     * @param value 编码
     * @param desc  描述
     */
    TriggerTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
