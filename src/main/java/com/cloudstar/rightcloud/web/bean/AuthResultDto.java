package com.cloudstar.rightcloud.web.bean;

import java.util.HashSet;
import java.util.Set;

import lombok.Data;

/**
 * 接口权限校验 响应
 */
@Data
public class AuthResultDto {

    /**
     * 权限是否校验通过
     */
    private Boolean isPassAuth;

    /**
     * 判断是否通过敏感数据校验
     */
    private Boolean isPassSensitive;


    /**
     * 判断是否为通过 重要操作认证
     */
    private Boolean isPassCritical;

    /**
     * 判断是否通过 黑名单校验
     */
    private Boolean isPassBlack;

    /**
     * 当前用户拥有的操作权限码
     */
    private Set<String> resActionSet = new HashSet<>();

}
