package com.cloudstar.rightcloud.web.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 白名单工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "web.around.aop")
public class SkipPathUtil {
    public List<String> interfaceSkip;



    /**
     * 校验白名单
     */
    public boolean skipFilter(String url) {
        if (ObjectUtil.isEmpty(interfaceSkip)) {
            return Boolean.FALSE;
        }
        return interfaceSkip.contains(url);
    }
}
