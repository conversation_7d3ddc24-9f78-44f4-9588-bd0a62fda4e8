/*
 * Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved..
 */

package com.cloudstar.sdk.iam.pojo;


import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * The type Auth user.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuthUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Long userSid;

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 账号
     */
    private String account;

    /**
     * 真实名称
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话号
     */
    private String mobile;

    /**
     * 用户类型 account:租户账号  subAccount:租户子账户
     */
    private String userType;

    /**
     * 父id
     */
    private Long parentSid;

    /**
     * 认证状态
     */
    private String authStatus;

    /**
     * 状态
     */
    private String status;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 最后登录ip
     */
    private String lastLoginIp;

    /**
     * 账号有效期开始时间
     */
    private Date startTime;

    /**
     * 账号有效期结束时间
     */
    private Date endTime;

    /**
     * 密码过期时间
     */
    private Date passwordExpiresAt;

    /**
     * 是否重置密码
     */
    private String isResetPassword;

    /**
     * 组织id
     */
    private Long orgSid;


    /**
     * 账号类型 USER:租户 MANAGER:管理员
     */
    private String accountType;

    /**
     * 统一入口用，判断是否为运营接口调用
     */
    private boolean isRightCloud;

    /**
     * cluster id 集群ID
     */
    private String clusterId;
}
