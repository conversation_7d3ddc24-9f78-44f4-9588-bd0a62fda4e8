package com.cloudstar.sdk.monitor.form;

import com.cloudstar.common.util.page.PageForm;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 通用指标
 *
 * @author: wanglang
 * @date: 2023/11/22 17:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MorCommonMetricGetListForm extends PageForm implements Serializable {
    /**
     * 通用指标名称
     */
    private String name;

    /**
     * 通用指标编码
     */
    private String code;
    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 状态
     */
    private String status;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 描述
     */
    private String description;

    /**
     * 云平台编码
     */
    private String envTypeCode;

    /**
     * 云平台版本
     */
    private String envTypeVersion;
}
