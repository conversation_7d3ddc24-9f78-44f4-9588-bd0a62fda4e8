package com.cloudstar.sdk.monitor.form;

import com.cloudstar.sdk.monitor.annotations.EnumValue;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 原始告警数据创建参数
 *
 * @author: wanglang
 * @date: 2023/8/10 14:28
 */
@Data
public class OpsAlarmDataOriginCreateForm {

    /**
     * 告警id
     */
    @Length(max = 255)
    private String alarmId;

    /**
     * 告警名称
     */
    @NotBlank
    @Length(max = 255)
    private String name;

    /**
     * 告警编码
     */
    private String code;
    /**
     * 告警状态;resolved 已恢复 not_resolved 未恢复 delete 已删除
     */
    @EnumValue(strValues = {"resolved", "not_resolved", "delete"})
    private String status;
    /**
     * 告警内容中文
     */
    private String contentEn;
    /**
     * 告警内容中文
     */
    private String contentCn;
    /**
     * 告警级别状态
     */
    private String levelStatus;
    /**
     * 所属云环境
     */
    private String envCode;
    /**
     * 所属云环境名称
     */
    private String envVersion;
    /**
     * 所属云环境
     */
    private Long envId;
    /**
     * 告警对象实例id
     */
    private String objectInstanceId;
    /**
     * 告警原始数据
     */
    private String rawData;
    /**
     * 资源类型
     */

    private String resTypeCode;
    /**
     * 告警恢复时间
     */
    private Date recoveredTime;
    /**
     * 告警开始时间
     */
    private Date startTime;
    /**
     * 最后告警时间
     */
    private Date lastTime;

    /**
     * 组装id
     */
    private Long orgId;
}
