package com.cloudstar.sdk.monitor.form;

import lombok.Data;

import java.io.Serializable;

/**
 * 告警规则指定告警对象创建参数
 *
 * @author: huang<PERSON><PERSON>
 * @date: 2025/6/05 16:39
 */
@Data
public class OpsAlarmRuleTargetCreateForm implements Serializable {

    /**
     * 指定告警对象id
     */
    private String instanceId;
    /**
     * 指定告警对象名称
     */
    private String instanceName;

    private String status;
}
