package com.cloudstar.sdk.monitor.result;

import lombok.Data;

import java.util.Date;

/**
 * 原生告警列表返回数据
 *
 * @author: wanglang
 * @date: 2024/4/3 18:33
 */
@Data
public class MorAlarmDataOriginGetListResult {

    /**
     * id
     */
    private Long id;

    /**
     * 产品组件
     */
    private String productComponents;

    /**
     * 告警名称
     */
    private String name;


    /**
     * 云环境id
     */
    private String envId;

    /**
     * 云环境名称
     */
    private String envName;

    /**
     * 告警级别状态
     */
    private String levelStatus;

    /**
     * 告警状态;resolved 已恢复 not_resolved 未恢复 delete 已删除
     */
    private String status;


    /**
     * 告警发生时间
     */
    private Date lastTime;


    /**
     * 持续时长
     */
    private String duration;

    /**
     * 告警编码
     */
    private String code;

    /**
     * 转换状态
     */
    private String convertState;

    /**
     * 告警来源
     */
    private String source;

    /**
     * 告警原始数据
     */
    private String rawData;
    
}
