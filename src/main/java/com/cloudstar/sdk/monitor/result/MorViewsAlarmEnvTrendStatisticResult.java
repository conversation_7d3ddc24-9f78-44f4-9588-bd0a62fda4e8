package com.cloudstar.sdk.monitor.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 告警报表云环境告警趋势
 *
 * @author: wanglang
 * @date: 2023/8/21 15:24
 */
@Data
public class MorViewsAlarmEnvTrendStatisticResult implements Serializable {

    /**
     * 告警时间
     */
    private String time;

    /**
     * 统计数据
     */
    private List<MorViewsAlarmEnvTrendStatisticInfo> countAlarmData;

    @Data
    public static class MorViewsAlarmEnvTrendStatisticInfo implements Serializable {

        /**
         * 云环境名称
         */
        private String envName;


        /**
         * 统计数量
         */
        private Integer total;
    }


}
