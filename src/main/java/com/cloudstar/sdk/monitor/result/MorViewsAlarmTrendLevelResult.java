package com.cloudstar.sdk.monitor.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 告警报表告警级别趋势
 *
 * @author: wanglang
 * @date: 2023/8/21 15:24
 */
@Data
public class MorViewsAlarmTrendLevelResult implements Serializable {

    /**
     * 告警时间
     */
    private String time;

    /**
     * 级别数据
     */
    private List<MorViewsAlarmTrendLevelInfo> countAlarmData;


    @Data
    public static class MorViewsAlarmTrendLevelInfo implements Serializable {
        /**
         * 告警等级
         */
        private String level;

        /**
         * 告警条数
         */
        private Integer total;
    }



}
