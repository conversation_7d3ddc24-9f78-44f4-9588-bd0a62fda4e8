package com.cloudstar.sdk.monitor.result;

import com.cloudstar.sdk.monitor.annotations.BeanHelperField;
import lombok.Data;

import java.util.Date;

/**
 * 历史告警数据
 *
 * @author: wanglang
 * @date: 2023/6/5 8:59 PM
 */
@Data
public class OpsHistoricalAlarmResult {
    /**
     * id
     */
    private Long id;
    /**
     * 告警状态
     */
    private String status;

    /**
     * 告警名称
     */
    private String name;

    /**
     * 对象类型
     */
    @BeanHelperField(columnName = "objectType")
    private String targetType;

    /**
     * 告警对象
     */
    private String target;

    /**
     * 告警对象实例id
     */
    private String objectInstanceId;

    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    @BeanHelperField(columnName = "recoveredTime")
    private Date endTime;
    /**
     * 告警处理状态
     */
    private String processingStatus;

    /**
     * 处理人
     */
    private String processingUser;

    /**
     * 持续时长
     */
    private String duration;

    /**
     * 解决人（处理人）
     */
    private String resolveUser;

    /**
     * 告警等级
     */
    @BeanHelperField(columnName = "alarmLevelStatus")
    private String level;

    /**
     * 发生时间
     */
    @BeanHelperField(columnName = "lastTime")
    private Date occurTime;

    /**
     * 恢复时间
     */
    private Date recoveredTime;

    /**
     * 告警来源
     */
    private String source;


    /**
     * 云环境id
     */
    private String envId;

    /**
     * 云环境名称
     */
    private String envName;
}
