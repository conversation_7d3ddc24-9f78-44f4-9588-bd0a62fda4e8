package com.cloudstar.service.facade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.dao.model.UserEntity;
import com.cloudstar.pojo.user.req.UserRegisterReq;

/**
 * 租户service
 *
 * <AUTHOR>
 * @date 2022/7/20 17:39
 */
public interface UserService extends IService<UserEntity> {

    boolean register(UserRegisterReq req);
    boolean sync(UserRegisterReq req);

    void checkPassword(String password, String account);

    boolean deleteKcUser(Long userSid);
}
