package com.cloudstar.service.facade.bigscreen;

import com.cloudstar.dao.model.bigscreen.CollectAllocateCpu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto;

import java.util.List;

/**
 * cpu采集service
* <AUTHOR>
* @description 针对表【collect_allocate_cpu(资源池分配率-cpu)】的数据库操作Service
* @createDate 2022-10-26 16:37:43
*/
public interface CollectAllocateCpuService extends IService<CollectAllocateCpu> {

    CollectCommonResourceDto getCpuResource(Long clusterId, Integer monitorCollect);

    List<CollectCommonResourceDto> getCpuAllocatedResourceTrend(Long clusterId, Integer monitorCollect, Integer trendMonitorCollect, String type);
}
