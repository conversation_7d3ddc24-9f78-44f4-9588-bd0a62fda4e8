package com.cloudstar.service.facade.bill;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.bean.req.BizDownloadReq;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.bill.BizBillUsageItem;
import com.cloudstar.service.pojo.vo.requestvo.bill.BizBillUsageItemExportReq;
import com.cloudstar.service.pojo.vo.requestvo.bill.BizBillUsageItemPageReq;
import com.cloudstar.service.pojo.vo.responsevo.bill.BillDownloadResp;
import com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemPageResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUsageResp;
import com.cloudstar.service.pojo.vo.responsevo.overview.MeasurementDataResp;

import java.util.List;

import javax.servlet.http.HttpServletResponse;


/**
 * 作业计量明细
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
public interface BizBillUsageItemService extends IService<BizBillUsageItem> {

    /**
     * 作业计量明细列表
     *
     * @param req 要求事情
     *
     * @return {@link PageResult}<{@link BizBillUsageItemPageResp}>
     */
    PageResult<BizBillUsageItemPageResp> getBillUsageItemList(BizBillUsageItemPageReq req);

    /**
     * 导出作业计量明细
     *
     * @param req 要求事情
     */
    void exportBillItem(BizBillUsageItemExportReq req);

    /**
     * 下载列表
     *
     * @param req 要求事情
     *
     * @return {@link PageResult}<{@link BillDownloadResp}>
     */
    PageResult<BillDownloadResp> downloadList(BizDownloadReq req);

    /**
     * 下载
     *
     * @param downloadId 下载id
     * @param response 响应
     */
    void download(Long downloadId, HttpServletResponse response);

    /**
     *获得集群使用量数据
     *
     * @param dayNum 统计近dayNum天的数据
     *
     * @return {@link List}<{@link ClusterUsageResp}>
     */
    List<ClusterUsageResp> getClusterUsageList(Integer dayNum);

    MeasurementDataResp getMeasurementData();
}
