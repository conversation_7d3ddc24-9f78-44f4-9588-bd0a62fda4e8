package com.cloudstar.service.facade.collect;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.dao.model.collect.CollectAllocateMem;

/**
 * 针对表【collect_allocate_mem(资源池分配率-内存)】的数据库操作Service
 *
 * <AUTHOR>
 * @createDate 2022-10-14 11:26:26
 */
public interface CollectAllocateMemService extends IService<CollectAllocateMem> {

    Integer selectUnusedMem(Long clusterId, String poolId);

    void dataCleanFromWeekAgo();
}
