package com.cloudstar.service.facade.higress;


import cn.hutool.json.JSONUtil;
import com.cloudstar.ConfigService;
import com.cloudstar.common.base.exception.BizException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.cloudstar.bean.enums.ConfigType;

import java.util.Map;

@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@Slf4j
public class HigressService {

    RestTemplate restTemplate;

    ConfigService configService;


    // 通用GET请求
    public <T> T get(String subPath, Class<T> responseType, Object... uriVariables) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + subPath;
        return restTemplate.getForObject(url, responseType, uriVariables);
    }

    // 通用POST请求封装
    public String post(String url, Map<String, Object> uriVariables) {
        // 发起请求
        HttpEntity<String> requestEntity = new HttpEntity<>(JSONObject.toJSONString(uriVariables), tokenHeader());
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new BizException("【RestTemplate call】error");
        }
        return response.getBody();
    }

    public static HttpHeaders tokenHeader() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", "application/json");
        headers.add("Content-Encoding", "UTF-8");
        headers.add("Content-Type", "application/json; charset=UTF-8");
        return headers;
    }


}
