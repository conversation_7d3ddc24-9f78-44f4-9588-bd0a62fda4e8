package com.cloudstar.service.facade.higress;


import com.cloudstar.ConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.cloudstar.bean.enums.ConfigType;

@Service
@RequiredArgsConstructor
@Slf4j
public class HigressService {

    private final RestTemplate restTemplate;

    private final ConfigService configService;


    // 通用GET请求
    public <T> T get(String subPath, Class<T> responseType, Object... uriVariables) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + subPath;
        return restTemplate.getForObject(url, responseType, uriVariables);
    }

    // 通用POST请求封装
    public <T> T post(String subPath, Object request, Class<T> responseType, Object... uriVariables) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + subPath;
        T t = restTemplate.postForObject(url, request, responseType, uriVariables);
        log.info("POST请求成功，返回结果为：{}", t);
        return t;
    }

}
