package com.cloudstar.service.facade.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.base.pojo.ValidationResult;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.manager.ManagerEntity;
import com.cloudstar.service.pojo.vo.requestvo.ResetPwdReq;
import com.cloudstar.service.pojo.vo.requestvo.UpdatePwdReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.BizManagerReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.CreateManagerReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.DescribeUsersByRoleIdRequest;
import com.cloudstar.service.pojo.vo.requestvo.manager.ManagerListSelectReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.UpdateManagerEmailReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.UpdateManagerMobileReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.UpdateManagerReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.ValidateManagerPwdReq;
import com.cloudstar.service.pojo.vo.requestvo.tenant.FindPwdReq;
import com.cloudstar.service.pojo.vo.requestvo.tenant.ModifyStatusReq;
import com.cloudstar.service.pojo.vo.requestvo.tenant.ValidateUserExitsReq;
import com.cloudstar.service.pojo.vo.responsevo.ManagerDetailResponse;
import com.cloudstar.service.pojo.vo.responsevo.ManagerListResponse;
import com.cloudstar.service.pojo.vo.responsevo.manager.BizManagerResponse;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 管理员实体服务
 *
 * <AUTHOR>
 * @description 针对表【manager_entity(运营运维管理员账号表)】的数据库操作Service
 * @createDate 2022-08-10 10:48:14
 * @date 2022/08/10
 */
public interface ManagerEntityService extends IService<ManagerEntity> {

    Boolean insert(CreateManagerReq createManagerReq);

    /**
     * 新增bss管理员
     *
     * @param createManagerReq 请求
     */
    Boolean insertBssManager(CreateManagerReq createManagerReq);

    ManagerDetailResponse getManagerDetail(Long id);

    Page<ManagerEntity> getManagerList(ManagerListSelectReq req);

    PageResult<ManagerDetailResponse> getManagerListResponse(ManagerListSelectReq req);

    List<ManagerListResponse> getManagers(ManagerListSelectReq req);

    void updateManagerByManagerId(UpdateManagerReq rq);

    ValidationResult validateUser(ValidateUserExitsReq request);

    Boolean resetPassword(@Valid ResetPwdReq req);

    ManagerDetailResponse modifyStatus(ModifyStatusReq req);


    String getManagerMobile(Long userSid);

    String getManagerEmail(Long userSid);

    Boolean validateManagerPwd(ValidateManagerPwdReq request);

    Boolean unlockUser(String userSid);

    Boolean updateManagerPwd(@Valid UpdatePwdReq request);

    List<ManagerEntity> findUsersByRoleId(DescribeUsersByRoleIdRequest request);

    Boolean findPwd(FindPwdReq req);

    /**
     * 异步导出户信息
     */
    void exportUser(HttpServletResponse response, ManagerListSelectReq managerListSelectReq);

    /**
     * 下载指定用户信息
     */
    void downloadUser(Long downloadId, HttpServletResponse response);

    /**
     * 下载文件记录列表
     */
    PageResult<BizManagerResponse> downloadUserList(BizManagerReq req);

    Boolean updateManagerMobile(UpdateManagerMobileReq updateManagerMobileReq);

    Boolean updateManagerEmail(UpdateManagerEmailReq updateManagerEmailReq);

    Boolean updateManagerStatus(String account, String status);
}
