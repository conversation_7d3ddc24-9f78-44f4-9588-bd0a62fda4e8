package com.cloudstar.service.facade.notice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.notice.SysMNotice;
import com.cloudstar.service.pojo.vo.requestvo.notice.NoticePageRequest;
import com.cloudstar.service.pojo.vo.responsevo.notice.NoticeListResponse;

/**
 * 公告表service
 *
 * <AUTHOR>
 * @description 针对表【sys_m_notice(公告表)】的数据库操作Service
 * @createDate 2022-09-20 11:31:14
 */
public interface SysMNoticeService extends IService<SysMNotice> {

    PageResult<NoticeListResponse> getNoticeInfoPage(NoticePageRequest noticePageRequest);

    SysMNotice getNotice(String id);
}
