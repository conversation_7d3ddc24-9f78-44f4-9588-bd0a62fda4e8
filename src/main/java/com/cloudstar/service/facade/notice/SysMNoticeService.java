package com.cloudstar.service.facade.notice;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.notice.SysMNotice;
import com.cloudstar.service.pojo.vo.requestvo.notice.NoticePageListRequest;
import com.cloudstar.service.pojo.vo.requestvo.notice.NoticeUpdateRequest;
import com.cloudstar.service.pojo.vo.responsevo.notice.NoticeDetailResponse;
import com.cloudstar.service.pojo.vo.responsevo.notice.NoticeListResponse;

import java.util.Set;

/**
 * 公告管理
 * <AUTHOR>
 * @description 针对表【sys_m_notice(公告表)】的数据库操作Service
 * @createDate 2022-11-03 15:36:22
 */
public interface SysMNoticeService extends IService<SysMNotice> {

    /**
     * 获取公告列表
     *
     * @param noticePageRequest 请求入参
     *
     * @return <{@link PageResult}<{@link NoticeListResponse}>
     */
    PageResult<NoticeListResponse> page(NoticePageListRequest noticePageRequest);

    /**
     * 公告详情
     *
     * @param id 公告id
     *
     * @return {@link NoticeDetailResponse}
     */
    NoticeDetailResponse getNoticeDetail(String id);

    /**
     * 保存公告
     *
     * @param noticeUpdateRequest 公告保存内容
     *
     * @return {@link Rest}<{@link String}>
     */
    Rest<String> saveNotice(NoticeUpdateRequest noticeUpdateRequest);

    /**
     * 删除公告
     *
     * @param noticeId 公告id
     */
    void remove(String noticeId);

    /**
     * 检测公告中的敏感词
     *
     * @param sysMNotice 公告对象
     *
     * @return {@link Set}<{@link String}>
     */
    Set<String> checkSensitiveWord(SysMNotice sysMNotice);

    /**
     * 修改公告状态
     *
     * @param noticeId 公告对象
     * @param status 公告状态
     */
    void updateStatusById(String noticeId, String status);
}
