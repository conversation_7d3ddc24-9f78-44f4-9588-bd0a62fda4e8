package com.cloudstar.service.facade.system;

import com.cloudstar.bean.req.UpdateConfigReq;
import com.cloudstar.bean.res.SysMConfigRes;

import java.util.List;

/**
 * 基础配置
 */
public interface SystemConfigService {


    /**
     * 获取基础配置
     */
    List<SysMConfigRes> getBaseConfig();

    /**
     * 修改基础配置
     */
    void updateBaseConfig(List<UpdateConfigReq> reqs);

    /**
     * 获取安全设置
     */
    List<SysMConfigRes> getSafetyConfig();

    /**
     * 修改安全设置
     */
    void updateSafetyConfig(List<UpdateConfigReq> reqs);


    /**
     * 获取消息网关
     */
    List<SysMConfigRes> getMessageConfig();


    /**
     * 修改消息网关
     */
    void updateMessageConfig(List<UpdateConfigReq> reqs);


    /**
     * 获取业务配置
     */
    List<SysMConfigRes> getBusinessConfig();


    /**
     * 修改业务配置
     */
    void updateBusinessConfig(List<UpdateConfigReq> reqs);


    /**
     * 获取管理端业务配置
     */
    List<SysMConfigRes> getManagerConfig();


    /**
     * 获取业务配置
     */
    List<SysMConfigRes> getSelfMonitorConfig();


    /**
     * 修改业务配置
     */
    void updateSelfMonitorConfig(List<UpdateConfigReq> reqs);

}
