package com.cloudstar.service.facade.user;

import com.cloudstar.dao.model.user.UserEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户service
* <AUTHOR>
* @description 针对表【user_entity(租户及租户子用户表)】的数据库操作Service
* @createDate 2022-10-18 15:03:26
*/
public interface UserEntityService extends IService<UserEntity> {

    List<UserEntity> selectExpiredUser();

    List<UserEntity> selectUserByThresholdValue(long thresholdValue);
}
