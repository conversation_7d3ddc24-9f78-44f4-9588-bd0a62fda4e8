package com.cloudstar.service.grpcservice.facade;

import com.cloudstar.sdk.schedule.pojo.QueryQuotaUsedReq;
import com.cloudstar.sdk.schedule.pojo.QueryQuotaUsedResp;
import com.cloudstar.service.grpc.AgentQuotaServiceGrpc;
import com.cloudstar.service.grpcservice.AgentQuotaServiceImpl;
import com.cloudstar.service.pojo.dto.quota.CreateQuotaDto;

/**
 * 算法
 *
 * <AUTHOR>
 * @date 2024/8/20 16:51
 */
public interface AgentQuotaService extends GrpcServiceBase {


    /**
     * 构建
     *
     * @param targetServer 目标服务器
     *
     * @return {@link AgentPermissionService}
     */
    static AgentQuotaService build(String targetServer) {
        AgentQuotaServiceImpl service = new AgentQuotaServiceImpl();
        service.build(targetServer, AgentQuotaServiceGrpc.AgentQuotaServiceBlockingStub.class);
        return service;
    }

    boolean createQuota(Boolean activateRefinedQuota, CreateQuotaDto req);

    QueryQuotaUsedResp queryQuotaUsed(QueryQuotaUsedReq req);

}
