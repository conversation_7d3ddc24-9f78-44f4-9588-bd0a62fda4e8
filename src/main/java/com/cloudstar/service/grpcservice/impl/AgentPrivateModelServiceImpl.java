package com.cloudstar.service.grpcservice.impl;

import com.cloudstar.service.grpc.AgentPrivateModelGrpc;
import com.cloudstar.service.grpc.AgentPrivateModelProto.syncPrivateModelStatus;
import com.cloudstar.service.grpc.AgentPrivateModelProto.syncPrivateModelStatusReq;
import com.cloudstar.service.grpc.AgentPrivateModelProto.syncPrivateModelStatusResp;
import com.cloudstar.service.grpcservice.facade.AgentPrivateModelService;
import com.cloudstar.utils.ProtoJsonUtils;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * notebook
 *
 * <AUTHOR>
 * @date 2024/7/3 14:34
 */
@Service
@Slf4j
public class AgentPrivateModelServiceImpl implements AgentPrivateModelService {

    private AgentPrivateModelGrpc.AgentPrivateModelBlockingStub blockingStub;

    @Resource
    private ManagedChannel managedChannel;

    @PostConstruct
    public void init() {
        blockingStub = AgentPrivateModelGrpc.newBlockingStub(managedChannel);
    }


    /**
     * 同步notebook状态
     *
     * @param list list
     */
    public boolean syncPrivateModelStatus(List<syncPrivateModelStatus> list) {
        log.info("推送私有模型状态请求参数:{}", ProtoJsonUtils.toJson(list.get(0)));
        syncPrivateModelStatusReq request = syncPrivateModelStatusReq.newBuilder().addAllResult(list).build();
        final syncPrivateModelStatusResp syncPrivateModelStatusResp = blockingStub.syncPrivateModelStatus(request);
        log.info("推送私有模型状态请求返回值:{}", ProtoJsonUtils.toJson(syncPrivateModelStatusResp));
        return true;
    }
}
