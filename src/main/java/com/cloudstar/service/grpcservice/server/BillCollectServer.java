package com.cloudstar.service.grpcservice.server;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.BillCollectRedisDto;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.service.config.ServerTokenGrpcInterceptor;
import com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq;
import com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp;
import com.cloudstar.service.grpc.GetAgentBillGRPCServiceGrpc;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;

import java.util.Date;
import java.util.Objects;

/**
 * 话单采集server
 *
 * <AUTHOR>
 * @date 2024/7/8 11:19
 */
@GrpcService
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class BillCollectServer extends GetAgentBillGRPCServiceGrpc.GetAgentBillGRPCServiceImplBase {

    RedisUtil redisUtil;
    private final ServerTokenGrpcInterceptor serverTokenGrpcInterceptor;

    /**
     * <pre>
     * 裸金属话单采集
     * </pre>
     */
    public void bmsBillCollect(BmsBillCollectReq request, io.grpc.stub.StreamObserver<BmsBillCollectResp> responseObserver) {
        BillCollectRedisDto dto = new BillCollectRedisDto();
        dto.setNamespace(request.getNamespace());
        dto.setName(request.getName());
        dto.setType(request.getType());
        if (ObjectUtil.isNotEmpty(request.getId())) {
            dto.setId(request.getId());
        }
        Object clusterId = ServerTokenGrpcInterceptor.CLUSTER_ID.get();
        if (ObjectUtil.isEmpty(clusterId)) {
            throw new BizException(BizErrorEnum.DEFAULT_EXCEPTION_MSG);
        }
        dto.setClusterId(clusterId.toString());
        StringBuilder sb = new StringBuilder(RedisCacheKeyEnum.TICKET_COLLECT.getKey());
        sb.append(":").append(clusterId)
                .append(":").append(dto.getNamespace())
                .append(":").append(dto.getType())
                .append(":").append(dto.getId());
        //从redis里面拿旧的数据
        final String cacheStr = redisUtil.get(sb.toString());
        if (ObjectUtil.isNotEmpty(cacheStr)) {
            BillCollectRedisDto cacheDto = JSONUtil.toBean(cacheStr, BillCollectRedisDto.class);
            if (ObjectUtil.isNotEmpty(cacheDto)) {
                if (ObjectUtil.isNotEmpty(cacheDto.getCreateTime()) && !Objects.equals(cacheDto.getCreateTime(), 0L)) {
                    dto.setCreateTime(cacheDto.getCreateTime());
                }
                if (ObjectUtil.isNotEmpty(cacheDto.getWaitTime()) && !Objects.equals(cacheDto.getWaitTime(), 0L)) {
                    dto.setWaitTime(cacheDto.getWaitTime());
                }
                if (ObjectUtil.isNotEmpty(cacheDto.getStartTime()) && !Objects.equals(cacheDto.getStartTime(), 0L)) {
                    dto.setStartTime(cacheDto.getStartTime());
                }
                if (ObjectUtil.isNotEmpty(cacheDto.getCompleteTime()) && !Objects.equals(cacheDto.getCompleteTime(), 0L)) {
                    dto.setCompleteTime(cacheDto.getCompleteTime());
                }
            }
        }
        //从agent同步更新最新的数据
        if (ObjectUtil.isNotEmpty(request.getCreateTime()) && !Objects.equals(request.getCreateTime(), 0L)) {
            dto.setCreateTime(new Date(request.getCreateTime()));
        }
        if (ObjectUtil.isNotEmpty(request.getWaitTime()) && !Objects.equals(request.getWaitTime(), 0L)) {
            dto.setWaitTime(new Date(request.getWaitTime()));
        }
        if (ObjectUtil.isNotEmpty(request.getStartTime()) && !Objects.equals(request.getStartTime(), 0L)) {
            dto.setStartTime(new Date(request.getStartTime()));
        }
        if (ObjectUtil.isNotEmpty(request.getCompleteTime()) && !Objects.equals(request.getCompleteTime(), 0L)) {
            dto.setCompleteTime(new Date(request.getCompleteTime()));
        }
        if (ObjectUtil.isNotEmpty(dto.getStartTime()) && !Objects.equals(request.getStartTime(), 0L)) {
            redisUtil.set(sb.toString(), JSONUtil.toJsonStr(dto));
        }
        BmsBillCollectResp response = BmsBillCollectResp.newBuilder().setStatus(true).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
