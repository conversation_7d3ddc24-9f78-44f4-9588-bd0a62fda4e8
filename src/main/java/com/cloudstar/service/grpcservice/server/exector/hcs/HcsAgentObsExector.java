package com.cloudstar.service.grpcservice.server.exector.hcs;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.common.base.enums.ObsResourceTypeEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.integration.hcs.pojo.HcsSdkParamBase;
import com.cloudstar.integration.hcs.util.EndpointUtil;
import com.cloudstar.service.grpc.AgentObsProto;
import com.cloudstar.service.grpcservice.server.exector.AgentObsExector;
import com.cloudstar.service.pojo.entity.obs.ObsEntityRep;
import com.cloudstar.service.pojo.entity.obs.ObsFileDto;
import com.obs.services.ObsClient;
import com.obs.services.model.ListObjectsRequest;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.ObsObject;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * hcs agent obs exector
 */
@Slf4j
@Component
public class HcsAgentObsExector extends AgentObsExector {

    private static ObsClient obsClient;

    private static final int MAX_DEPTH = 10;

    private static int maxKey = 1000;


    /**
     * 初始化obs服务
     */

    @Override
    public void init() {
    }


    private ObsClient getObsClient(HcsSdkParamBase base) {
        if (ObjectUtil.isEmpty(base.getAk()) || ObjectUtil.isEmpty(base.getSk())) {
            throw new BizException("AK, SK, and Endpoint cannot be null or empty.");
        }
        String endpoint = base.getEndpoint();
        if (ObjectUtil.isEmpty(endpoint)) {
            endpoint = EndpointUtil.getObsEndpoint();
        }
        return new ObsClient(base.getAk(), base.getSk(), endpoint);
    }

    @Override
    public void existsFileOrFileDir(AgentObsProto.existsFileOrFileDirRequest request,
                                    StreamObserver<AgentObsProto.existsFileOrFileDirResponse> responseObserver) {
        try {
            String userId = request.getUserId().split(STRING)[0];
            if (ObjectUtil.isEmpty(userId)) {
                throw new BizException("userId is null");
            }
            log.info("existsFileOrFileDir: {}", request);
            AgentUsers agentUsers = agentUsersMapper.selectOne(new LambdaQueryWrapper<AgentUsers>()
                    .eq(AgentUsers::getUserId, userId));
            if (ObjectUtil.isEmpty(agentUsers)) {
                throw new BizException("agentUsers is null");
            }
            HcsSdkParamBase base = new HcsSdkParamBase();
            base.setAk(agentUsers.getUserAk());
            base.setSk(agentUsers.getUserSk());
            obsClient = getObsClient(base);
            String objectKey = request.getObjectKey().substring(1);
            log.info("objectKey: {}", objectKey);
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(request.getUserId());
            listObjectsRequest.setMaxKeys(1);
            listObjectsRequest.setPrefix(objectKey);
            listObjectsRequest.setDelimiter("/");
            log.info("listObjectsRequest: {}", listObjectsRequest);
            ObjectListing objectListing = obsClient.listObjects(listObjectsRequest);
            log.info("objectListing: {}", objectListing);

            String prefix = "";
            if (!ObjectUtil.isEmpty(objectListing.getCommonPrefixes())) {
                prefix = objectListing.getCommonPrefixes().get(0).split("/")[0];
            }
            log.info("prefix: {}", prefix);

            String object = "";
            if (!ObjectUtil.isEmpty(objectListing.getObjects())) {
                object = objectListing.getObjects().get(0).getObjectKey().split("/")[0];
            }
            log.info("object: {}", object);

            // 文件夹或者文件存在
            boolean exists = !objectListing.getObjects().isEmpty()
                    || !objectListing.getCommonPrefixes().isEmpty()
                    && (prefix.equals(objectKey.split("/")[0])
                    || object.contains(objectKey.split("/")[0]));
            log.info("exists: {}", exists);

            responseObserver.onNext(AgentObsProto.existsFileOrFileDirResponse.newBuilder()
                    .setIsExists(exists)
                    .build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("检查文件存在性失败: {}/{}", request.getUserId(), request.getObjectKey(), e);
            responseObserver.onNext(AgentObsProto.existsFileOrFileDirResponse.newBuilder()
                    .setIsExists(false)
                    .build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void syncDataStorage(AgentObsProto.SyncDataStorageProtoRequest request,
                                StreamObserver<AgentObsProto.SyncDataStorageProtoResponse> responseObserver) {
        log.info("syncDataStorage: {}", request);

        ObsEntityRep obsEntityRep = new ObsEntityRep();
        List<ObsFileDto> obsFileDtos = new LinkedList<>();
        AtomicLong totalSize = new AtomicLong(0);
        AtomicLong fileCount = new AtomicLong(0);

        int currentDepth = request.getFileDir().split("/").length - 1;
        if (currentDepth > MAX_DEPTH) {
            throw new BizException("文件夹层级过深，最大支持" + MAX_DEPTH + "层");
        }

        try {
            String userId = request.getUserId().split("-")[0];
            AgentUsers agentUsers = agentUsersMapper.selectOne(new LambdaQueryWrapper<AgentUsers>()
                    .eq(AgentUsers::getUserId, userId));
            HcsSdkParamBase base = new HcsSdkParamBase();
            base.setSk(agentUsers.getUserSk());
            base.setAk(agentUsers.getUserAk());
            obsClient = getObsClient(base);

            String bucketName = request.getUserId();
            String objectKey = request.getFileDir().substring(1);
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
            listObjectsRequest.setMaxKeys(maxKey);
            listObjectsRequest.setPrefix(objectKey);
            listObjectsRequest.setDelimiter("/");
            log.info("sync listObjectsRequest: {}", listObjectsRequest);

            listObjects(listObjectsRequest, obsFileDtos, bucketName);

            for (ObsFileDto obsFileDto : obsFileDtos) {
                if (ObsResourceTypeEnum.FILE.getType().equals(obsFileDto.getResourceType())) {
                    totalSize.addAndGet(obsFileDto.getFileSize());
                    fileCount.incrementAndGet();
                }
            }
            obsEntityRep.setFileList(obsFileDtos);
            obsEntityRep.setTotalFileSize(totalSize.get());
            obsEntityRep.setTotalFileCount(fileCount.get());
            log.info("obsEntityRep: {}", obsEntityRep);

            responseObserver.onNext(AgentObsProto.SyncDataStorageProtoResponse.newBuilder()
                    .setStorageUsage(obsEntityRep.getTotalFileSize())
                    .setFileNum(obsEntityRep.getTotalFileCount())
                    .build());

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("同步数据失败: {}/{}", request.getUserId(), request.getFileDir(), e);
        } finally {
            responseObserver.onCompleted();
        }

    }

    private void listObjects(ListObjectsRequest listObjectsRequest, List<ObsFileDto> obsFileDtos, String bucketName) {
        ObjectListing objectListing;
        log.info("listObjects Params: {}, {}, {}", listObjectsRequest, obsFileDtos, bucketName);
        do {
            objectListing = obsClient.listObjects(listObjectsRequest);
            log.info("sync objectListing: {}", objectListing);

            for (ObsObject object : objectListing.getObjects()) {
                ObsFileDto obsFileDto = new ObsFileDto();
                String[] objectSplit = object.getObjectKey().split("/");
                obsFileDto.setFileName(objectSplit[objectSplit.length - 1]);
                obsFileDto.setResourceType(ObsResourceTypeEnum.FILE.getType());
                obsFileDto.setFileSize(object.getMetadata().getContentLength());
                obsFileDto.setModifyDate(object.getMetadata().getLastModified());
                log.info("sync obsFileDto: {}", obsFileDto);

                obsFileDtos.add(obsFileDto);
                log.info("sync obsFileDtos: {}", obsFileDtos);
            }
            listObjectsRequest.setMarker(objectListing.getNextMarker());
            listObjectsByPrefix(objectListing, obsFileDtos, bucketName);
        } while (objectListing.isTruncated());

    }

    private void listObjectsByPrefix(ObjectListing objectListing, List<ObsFileDto> obsFileDtos, String bucketName) {
        for (String commonPrefix : objectListing.getCommonPrefixes()) {
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
            listObjectsRequest.setPrefix(commonPrefix);
            listObjectsRequest.setMaxKeys(maxKey);
            listObjectsRequest.setDelimiter("/");
            log.info("listObjectsByPrefix listObjectsRequest: {}", listObjectsRequest);
            listObjects(listObjectsRequest, obsFileDtos, bucketName);
        }
    }

    @Override
    public void createPolicy(AgentObsProto.BucketPolicyRequest request,
                             StreamObserver<AgentObsProto.BucketPolicyResponse> responseObserver) {

    }

    @Override
    public void deletePolicy(AgentObsProto.BucketPolicyRequest request,
                             StreamObserver<AgentObsProto.BucketPolicyResponse> responseObserver) {

    }

    @Override
    public void queryPolicy(AgentObsProto.BucketPolicyRequest request,
                            StreamObserver<AgentObsProto.BucketPolicyResponse> responseObserver) {

    }

    @Override
    public void publishDataStorage(AgentObsProto.PublishDataStorageRequest request,
                                   StreamObserver<AgentObsProto.PublishDataStorageResponse> responseObserver) {

    }

    @Override
    public void subscribeDataStorage(AgentObsProto.SubscribeDataStorageRequest request,
                                     StreamObserver<AgentObsProto.SubscribeDataStorageResponse> responseObserver) {

    }

    @Override
    public void unsubscribeDataStorage(AgentObsProto.UnsubscribeDataStorageRequest request,
                                       StreamObserver<AgentObsProto.UnsubscribeDataStorageResponse> responseObserver) {

    }
}
