package com.cloudstar.service.grpcservice.server.exector.hcs;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.dao.mapper.user.AgentUsersMapper;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.integration.hcs.pojo.req.SyncDataObsReq;
import com.cloudstar.integration.hcs.pojo.resp.SyncDataObsResp;
import com.cloudstar.integration.hcs.service.facade.HcsObsService;
import com.cloudstar.service.grpc.AgentObsForBmsProto;
import com.cloudstar.service.grpcservice.server.exector.AgentObsForBmsExector;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;

/**
 * hcs agent obsForBms exector
 */
@Component
@Slf4j
public class HcsAgentObsForBmsExector extends AgentObsForBmsExector {

    public static final String BUCKET = "bucket";

    public static final String STRING = "-";

    @Resource
    private HcsObsService hcsObsService;

    @Resource
    private AgentUsersMapper agentUsersMapper;

    @Override
    public void syncCountData(AgentObsForBmsProto.SyncDataObsProtoRequest request,
                              StreamObserver<AgentObsForBmsProto.SyncDataObsProtoResponse> responseObserver) {
        List<AgentObsForBmsProto.SyncDataUser> syncDataUsersList = request.getSyncDataUsersList();
        AgentObsForBmsProto.SyncDataObsProtoResponse.Builder builder = AgentObsForBmsProto.SyncDataObsProtoResponse.newBuilder();
        log.info("请求参数:{}", request);
        for (AgentObsForBmsProto.SyncDataUser syncDataUser : syncDataUsersList) {
            try {
                final SyncDataObsReq req = new SyncDataObsReq();
                log.info("syncDataUser:{}", syncDataUser);
                AgentUsers agentUsers = agentUsersMapper.selectOne(new LambdaQueryWrapper<AgentUsers>()
                                                                           .eq(AgentUsers::getUserId, syncDataUser.getResourceId()));
                if (ObjectUtil.isEmpty(agentUsers)) {
                    log.error("未找到对应账号信息:{}", syncDataUser.getResourceId());
                    continue;
                }
                log.info("agentUsers:{}", JSONUtil.toJsonStr(agentUsers));
                req.setAk(agentUsers.getUserAk());
                req.setSk(agentUsers.getUserSk());
                req.setUserId(agentUsers.getUserId());
                req.setUserSid(syncDataUser.getUserId());
                req.setResourceId(syncDataUser.getResourceId());
                req.setBucketName(agentUsers.getObsId());
                log.info("查询obs信息:{}", req);
                SyncDataObsResp syncDataObsResp = hcsObsService.syncCountData(req);
                log.info("查询obs信息成功:{}", JSONUtil.toJsonStr(syncDataObsResp));
                AgentObsForBmsProto.ObsDataInfo obsDataInfo = AgentObsForBmsProto.ObsDataInfo.newBuilder()
                                                                                             .setUserId(syncDataObsResp.getObsDataInfo().getUserId())
                                                                                             .setResourceId(
                                                                                                     syncDataObsResp.getObsDataInfo().getResourceId())
                                                                                             .setUsedCapacity(syncDataObsResp.getObsDataInfo()
                                                                                                                             .getUsedCapacity())
                                                                                             .setObjectCount(syncDataObsResp.getObsDataInfo()
                                                                                                                            .getObjectCount())
                                                                                             .setBucketName(
                                                                                                     syncDataObsResp.getObsDataInfo().getBucketName())
                                                                                             .build();
                log.info("obsDataInfo:{}", JSONUtil.toJsonStr(obsDataInfo));
                builder.addObsDataInfo(obsDataInfo);
            } catch (Exception e) {
                log.error("查询obs信息失败:{}", e.getMessage());
            }
        }
        log.info("响应参数:{}", JSONUtil.toJsonStr(builder.build()));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
