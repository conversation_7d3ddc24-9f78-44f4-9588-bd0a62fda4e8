package com.cloudstar.service.grpcservice.server.exector.hcs;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.config.HcsClientConfig;
import com.cloudstar.dao.mapper.user.AgentUsersMapper;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.integration.hcs.pojo.HcsSdkParamBase;
import com.cloudstar.integration.hcs.pojo.req.CreateIamAccessKeyReq;
import com.cloudstar.integration.hcs.pojo.req.DeleteIamAccessKeyReq;
import com.cloudstar.integration.hcs.pojo.resp.QueryAkSkList;
import com.cloudstar.integration.hcs.pojo.resp.CreateIamAccessKeyResp;
import com.cloudstar.integration.hcs.pojo.resp.DeleteIamAccessKeyResp;
import com.cloudstar.integration.hcs.pojo.resp.QueryAkSkListResp;
import com.cloudstar.integration.hcs.service.facade.HcsIamService;
import com.cloudstar.integration.hcs.util.EndpointUtil;
import com.cloudstar.service.grpc.ServiceAccountProto;
import com.cloudstar.service.grpcservice.server.exector.ServiceAccountExector;
import com.huaweicloud.sdk.iam.v3.model.UpdatePermanentAccessKeyResponse;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * Hcs ServiceAccount Exector
 */
@Component
@Slf4j
public class HcsServiceAccountExector extends ServiceAccountExector {

    @Resource
    private HcsIamService hcsIamService;

    @Resource
    private AgentUsersMapper agentUsersMapper;

    @Resource
    private ConfigService configService;

    /**
     * 新增AK/SK密钥
     */
    @Override
    public void addServiceAccount(ServiceAccountProto.AddServiceAccountRequest request,
                                  StreamObserver<ServiceAccountProto.AddServiceAccountResponse> responseObserver) {
        ServiceAccountProto.AddServiceAccountResponse.Builder response = ServiceAccountProto.AddServiceAccountResponse.newBuilder();
        try {
            if (request.getTargetUser().isEmpty()) {
                throw new BizException("用户ID不能为空");
            }

            if (request.getAccessKey().isEmpty()) {
                response.setMessage("创建密钥失败");
                response.setSuccess(false);
                responseObserver.onNext(response.build());
                responseObserver.onCompleted();
                return;
            }

            AgentUsers agentUsers = agentUsersMapper.selectOne(new LambdaQueryWrapper<AgentUsers>()
                    .eq(AgentUsers::getUserId, request.getTargetUser()));
            log.info("创建agentUsers:{}", agentUsers);
            if (agentUsers == null) {
                throw new BizException("未找到目标用户");
            }

            final String endpoint = EndpointUtil.getIamEndpoint();
            final CreateIamAccessKeyReq createIamAccessKeyReq = new CreateIamAccessKeyReq();
            CreateIamAccessKeyReq.CredentialDetails credentialDetails = new CreateIamAccessKeyReq.CredentialDetails();
            credentialDetails.setUserId(agentUsers.getUserId());
            credentialDetails.setDescription(request.getDescription());
            log.info("credentialDetails:{}", credentialDetails);

            createIamAccessKeyReq.setSk(agentUsers.getUserSk());
            createIamAccessKeyReq.setAk(agentUsers.getUserAk());
            createIamAccessKeyReq.setCredential(credentialDetails);
            createIamAccessKeyReq.setEndpoint(endpoint);
            log.info("createIamAccessKeyReq:{}", createIamAccessKeyReq);

            CreateIamAccessKeyResp createIamAccessKeyResp = hcsIamService.createAccessKey(createIamAccessKeyReq);
            log.info("createIamAccessKeyResp:{}", createIamAccessKeyResp);
            if (ObjectUtil.isEmpty(createIamAccessKeyResp)) {
                throw new BizException("密钥创建个数超过限制");
            }

            response.setAccessKey(createIamAccessKeyResp.getCredential().getAccess());
            response.setSecretKey(createIamAccessKeyResp.getCredential().getSecret());
            response.setMessage("创建成功");
            response.setSuccess(true);

            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.info("创建密钥失败:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除AK/SK密钥
     *
     * @param request          请求
     * @param responseObserver 响应观察者
     */
    @Override
    public void deleteServiceAccount(ServiceAccountProto.DeleteServiceAccountRequest request,
                                     StreamObserver<ServiceAccountProto.DeleteServiceAccountResponse> responseObserver) {
        ServiceAccountProto.DeleteServiceAccountResponse.Builder response = ServiceAccountProto.DeleteServiceAccountResponse.newBuilder();
        try {
            if (request.getTargetUser().isEmpty()) {
                throw new Exception("用户ID不能为空");
            }
            if (!request.getAccessKey().isEmpty()) {
                DeleteIamAccessKeyReq deleteIamAccessKeyReq = new DeleteIamAccessKeyReq();
                final HcsSdkParamBase base = new HcsSdkParamBase();
                final String endpoint = EndpointUtil.getIamEndpoint();
                AgentUsers agentUsers = agentUsersMapper.selectOne(new LambdaQueryWrapper<AgentUsers>()
                        .eq(AgentUsers::getUserId, request.getTargetUser()));
                log.info("agentUsers:{}", agentUsers);

                deleteIamAccessKeyReq.setUserId(agentUsers.getUserId());
                deleteIamAccessKeyReq.setSk(agentUsers.getUserSk());
                deleteIamAccessKeyReq.setAk(agentUsers.getUserAk());
                deleteIamAccessKeyReq.setEndpoint(endpoint);
                log.info("deleteIamAccessKeyReq:{}", deleteIamAccessKeyReq);

                base.setAk(agentUsers.getUserAk());
                base.setSk(agentUsers.getUserSk());
                base.setEndpoint(endpoint);
                log.info("base:{}", base);

                QueryAkSkListResp queryAkSkListResp = hcsIamService.listAccessKeys(agentUsers.getUserId(), base);
                log.info("queryAkSkListResp:{}", queryAkSkListResp);

                if (queryAkSkListResp == null || queryAkSkListResp.getCredentials() == null || queryAkSkListResp.getCredentials().isEmpty()) {
                    response.setMessage("未找到密钥列表");
                    response.setSuccess(false);
                } else {
                    boolean keyDeleted = false;
                    for (QueryAkSkList key : queryAkSkListResp.getCredentials()) {
                        if (key.getAccess().equals(request.getAccessKey())) {
                            deleteIamAccessKeyReq.setAccessKey(key.getAccess());
                            DeleteIamAccessKeyResp deleteIamAccessKeyResp = hcsIamService.deleteAccessKey(deleteIamAccessKeyReq);
                            log.info("deleteIamAccessKeyResp:{}", deleteIamAccessKeyResp);

                            response.setMessage(deleteIamAccessKeyResp.getDeleteResult());
                            response.setSuccess(true);
                            keyDeleted = true;
                            break;
                        }
                    }
                    if (!keyDeleted) {
                        response.setMessage("删除密钥失败");
                        response.setSuccess(false);
                    }
                }
                responseObserver.onNext(response.build());
                responseObserver.onCompleted();
            }
        } catch (Exception e) {
            log.info("删除密钥失败:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 编辑AK/SK密钥
     *
     * @param request          请求
     * @param responseObserver 响应观察者
     */
    @Override
    public void editServiceAccount(ServiceAccountProto.EditServiceAccountRequest request,
                                   StreamObserver<ServiceAccountProto.EditServiceAccountResponse> responseObserver) {
        ServiceAccountProto.EditServiceAccountResponse.Builder response = ServiceAccountProto.EditServiceAccountResponse.newBuilder();
        String accessKey = request.getAccessKey();
        String userId = request.getTargetUser();
        try {
            if (accessKey.isEmpty()) {
                throw new Exception("密钥不能为空");
            }
            if (userId.isEmpty()) {
                throw new Exception("用户ID不能为空");
            }
            String newDescription = request.getNewDescription();
            String status = String.valueOf(request.getStatus());
            log.info("编辑密钥参数: accessKey={}, userId={}, newDescription={}, status={}", accessKey, userId, newDescription, status);

            AgentUsers agentUsers = agentUsersMapper.selectOne(new LambdaQueryWrapper<AgentUsers>()
                    .eq(AgentUsers::getUserId, userId));
            log.info("edit agentUsers:{}", agentUsers);

            HcsSdkParamBase base = new HcsSdkParamBase();
            base.setSk(agentUsers.getUserSk());
            base.setAk(agentUsers.getUserAk());
            log.info("edit base:{}", base);

            saveDescription(accessKey, newDescription, status, base);
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            responseObserver.onError(e);
        }
    }

    /**
     * 保存描述
     *
     * @param accessKey      accessKey
     * @param newDescription 新的描述
     * @param status         状态
     * @param base           HCS SDK参数
     */
    public void saveDescription(String accessKey, String newDescription, String status, HcsSdkParamBase base) {
        log.info("保存描述");
        try {
            UpdatePermanentAccessKeyResponse updatePermanentAccessKeyResponse
                    = hcsIamService.editPermanentAccessKey(accessKey, newDescription, status, base);
            log.info("HCS修改密钥响应:{}", updatePermanentAccessKeyResponse);
        } catch (BizException e) {
            throw new BizException("修改密钥失败");
        }
    }

    /**
     * 生成策略 策略模版，对权限进行修改赋予
     *
     * @param bucketName 存储桶的名称，用于指定策略适用的存储桶
     * @param prefix     策略前缀，用于限制对存储桶中对象的访问
     * @return 生成的策略 JSON 字符串
     */
    private String generatePolicyJson(String bucketName, String prefix) {
        // 创建新的策略
        String policyJson = "{\n"
                + "  \"Version\": \"2012-10-17\",\n"
                + "  \"Statement\": [\n"
                + "    {\n"
                + "      \"Effect\": \"Allow\",\n"
                + "      \"Action\": [\n"
                + "        \"s3:ListBucket\"\n"
                + "      ],\n"
                + "      \"Resource\": \"arn:aws:s3:::" + bucketName + "\",\n"
                + "      \"Condition\": {\n"
                + "        \"StringLike\": {\n"
                + "          \"s3:prefix\": [\"" + prefix + "/*\"]\n"
                + "        }\n"
                + "      }\n"
                + "    },\n"
                + "    {\n"
                + "      \"Effect\": \"Allow\",\n"
                + "      \"Action\": [\n"
                + "        \"s3:GetObject\",\n"
                + "        \"s3:PutObject\"\n"
                + "      ],\n"
                + "      \"Resource\": \"arn:aws:s3:::" + bucketName + "/" + prefix + "/*\"\n"
                + "    }\n"
                + "  ]\n"
                + "}";

        return policyJson;
    }

    /**
     * 获取系统region
     *
     * @return region
     */
    private String getSysRegion() {
        HcsClientConfig config = configService.getConfig(ConfigType.HCS_CLIENT_CONFIG);
        if (ObjectUtil.isEmpty(config) || config.getRegion() == null || ObjectUtil.isEmpty(config.getRegion().stringValue())) {
            throw new BizException("系统未配置region");
        }
        return config.getRegion().stringValue();
    }


}
