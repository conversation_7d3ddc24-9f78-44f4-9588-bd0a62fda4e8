package com.cloudstar.service.impl.access;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.mapper.access.UserAssertionPermissionMapper;
import com.cloudstar.dao.model.access.UserAssertionPermission;
import com.cloudstar.service.facade.access.UserAssertionPermissionService;
import org.springframework.stereotype.Service;

/**
 * 用户断言权限服务impl
 *
 * <AUTHOR>
 * @description 针对表【user_assertion_permission(用户断言权限关联表)】的数据库操作Service实现
 * @createDate 2022-08-17 10:52:39
 * @date 2022/08/17
 */
@Service
public class UserAssertionPermissionServiceImpl extends ServiceImpl<UserAssertionPermissionMapper, UserAssertionPermission>
        implements UserAssertionPermissionService {

}




