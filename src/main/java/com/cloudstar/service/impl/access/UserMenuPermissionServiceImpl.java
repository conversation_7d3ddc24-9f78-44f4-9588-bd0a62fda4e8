package com.cloudstar.service.impl.access;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.model.access.UserMenuPermission;
import com.cloudstar.service.facade.access.UserMenuPermissionService;
import com.cloudstar.dao.mapper.access.UserMenuPermissionMapper;
import org.springframework.stereotype.Service;

/**
 * 用户菜单权限服务impl
 *
 * <AUTHOR>
 * @description 针对表【user_menu_permission(租户菜单与权限表)】的数据库操作Service实现
 * @createDate 2022-08-20 10:39:16
 * @date 2022/08/20
 */
@Service
public class UserMenuPermissionServiceImpl extends ServiceImpl<UserMenuPermissionMapper, UserMenuPermission>
        implements UserMenuPermissionService {

}




