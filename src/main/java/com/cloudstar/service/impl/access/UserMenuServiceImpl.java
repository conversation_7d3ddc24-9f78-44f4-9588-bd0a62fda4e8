package com.cloudstar.service.impl.access;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.dto.SysMConfigDto;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.enums.SystemTypeEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.access.UserMenuMapper;
import com.cloudstar.dao.mapper.access.UserPermissionMapper;
import com.cloudstar.dao.model.access.UserMenu;
import com.cloudstar.dao.model.access.UserMenuPermission;
import com.cloudstar.dao.model.access.UserPermission;
import com.cloudstar.enums.MenuStatusEnum;
import com.cloudstar.enums.PermissionControllerEnum;
import com.cloudstar.enums.PermissionModuleEnum;
import com.cloudstar.service.facade.access.UserMenuPermissionService;
import com.cloudstar.service.facade.access.UserMenuService;
import com.cloudstar.service.pojo.dto.manager.ManagerPermissionDTO;
import com.cloudstar.service.pojo.vo.requestvo.manager.CreateMenuRequest;
import com.cloudstar.service.pojo.vo.requestvo.manager.ManagerMenuAuthorizeReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.UpdateMenuRequest;
import com.cloudstar.service.pojo.vo.responsevo.access.UserMenuDetailRes;
import com.cloudstar.service.pojo.vo.responsevo.access.UserMenuTreeRes;
import com.cloudstar.service.pojo.vo.responsevo.manager.MenuPermissionRes;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.RequiredArgsConstructor;

/**
 * 用户菜单服务impl
 *
 * <AUTHOR>
 * @description 针对表【user_menu(租户菜单表)】的数据库操作Service实现
 * @createDate 2022-08-16 20:51:08
 * @date 2022/08/17
 */
@Service
@RequiredArgsConstructor
public class UserMenuServiceImpl extends ServiceImpl<UserMenuMapper, UserMenu>
        implements UserMenuService {

    private final UserMenuMapper menuMapper;
    private final UserMenuPermissionService menuPermissionService;
    private final UserPermissionMapper permissionMapper;
    private final ConfigService configService;

    private final String suffix = "**";
    private final String devConfig = "menu.dev.config";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(CreateMenuRequest request) {
        if (!ObjectUtils.isEmpty(request.getRouteType()) && request.getRouteType().equalsIgnoreCase(devConfig)) {
            //检查开发配置
            checkDevConfig();
        }
        UserMenu userMenu = BeanUtil.copyProperties(request, UserMenu.class);
        checkMenu(userMenu);
        userMenu.setType(SystemTypeEnum.CUSTOMIZE.getType());
        menuMapper.insert(userMenu);
        if (request.getParentId().equals(0L)) {
            userMenu.setPathIds("/");
            userMenu.setLowerPathIds("/" + userMenu.getMenuSid() + "/");
        } else {
            //父级
            UserMenu parent = menuMapper.selectById(request.getParentId());
            if (ObjectUtils.isEmpty(parent)) {
                BizError.e(BizErrorEnum.MSG_1036_PARENT_MENU_NOT_EXIST);
            }
            userMenu.setPathIds(parent.getLowerPathIds());
            userMenu.setLowerPathIds(parent.getLowerPathIds() + userMenu.getMenuSid() + "/");
        }
        return this.updateById(userMenu);
    }

    @Override
    public List<UserMenuTreeRes> getManagerMenu(Long parentId) {
        List<UserMenu> managerMenus = menuMapper.selectList(
                SpecWrapperUtil.<UserMenu>builder()
                        .eq("parent_id", parentId)
                        //.eq("display", "true")
                        .orderByAsc("sort"));
        List<UserMenuTreeRes> list = BeanUtil.copyToList(managerMenus, UserMenuTreeRes.class);
        //子菜单填充数据
        return fillData(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updMenu(UpdateMenuRequest request) {
        UserMenu userMenu = menuMapper.selectById(request.getMenuSid());
        String oleMenuCode = userMenu.getCode();
        if (ObjectUtils.isEmpty(userMenu)) {
            BizError.notFound();
        }
        //老parentId
        Long oldParentId = userMenu.getParentId();
        //更新
        BeanUtil.copyProperties(request, userMenu, CopyOptions.create().setIgnoreNullValue(true));
        //如果菜单名称改变，检查重名
        if (!oleMenuCode.equalsIgnoreCase(userMenu.getCode())) {
            checkMenu(userMenu);
        }
        //如果父级没有改动，则直接更新
        if (ObjectUtils.isEmpty(request.getParentId()) || oldParentId.equals(request.getParentId())) {
            menuMapper.updateById(userMenu);
        }
        //如果父级改动，则改变所有链表
        if (request.getParentId().equals(0L)) {
            userMenu.setPathIds("/");
            userMenu.setLowerPathIds("/" + userMenu.getMenuSid() + "/");
        } else {
            //父级
            UserMenu parent = menuMapper.selectById(request.getParentId());
            if (ObjectUtils.isEmpty(parent)) {
                BizError.notFound();
            }
            userMenu.setPathIds(parent.getLowerPathIds());
            userMenu.setLowerPathIds(parent.getLowerPathIds() + userMenu.getMenuSid() + "/");
        }
        //更新
        menuMapper.updateById(userMenu);
        //更新子集
        updateChildMenus(userMenu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeMenu(Long menusId) {
        UserMenu userMenu = menuMapper.selectById(menusId);
        List<UserMenu> managerMenus = menuMapper.selectList(
                SpecWrapperUtil.<UserMenu>builder().eq("parent_id", menusId).eq("display", "1"));
        if (CollectionUtil.isNotEmpty(managerMenus)) {
            BizError.e(BizErrorEnum.MSG_1037_HAS_SUBMENU_CANNOT_DELETE);
        }
        //删除菜单和菜单权限
        menuMapper.delete(SpecWrapperUtil.<UserMenu>builder().eq("menu_sid", menusId));
        menuPermissionService.remove(SpecWrapperUtil.<UserMenuPermission>builder().eq("menu_sid", menusId));
        return true;
    }

    @Override
    public MenuPermissionRes selectPermissionByMenusSid(Long menusSid) {
        MenuPermissionRes res = new MenuPermissionRes();
        //查询所有权限
        List<UserPermission> permissions = permissionMapper.selectList(SpecWrapperUtil.<UserPermission>builder().orderByAsc("sort"));
        //查询当前菜单的权限
        List<UserPermission> ownPermission = permissionMapper.selectPermissionByMenusSid(menusSid);
        if (CollectionUtil.isNotEmpty(ownPermission)) {

            List<Long> ownPermissionSid = ownPermission.stream().map(UserPermission::getPermissionSid).distinct().collect(Collectors.toList());
            res.setOwnPermissionSid(ownPermissionSid);
            permissions.forEach(e -> {
                if (ownPermissionSid.contains(e.getPermissionSid())) {
                    e.setSelect(true);
                }
            });
        }

        //module_category =》controller_category =》ManagerPermissionDTO分组
        Map<String, List<UserPermission>> moduleMap = permissions.stream()
                .collect(Collectors.groupingBy(UserPermission::getModuleCategory, LinkedHashMap::new, Collectors.toList()));

        List<ManagerPermissionDTO> menuPermissionRes = new ArrayList<>();
        moduleMap.forEach((moduleKey, moduleList) -> {
            //填充module分类集合
            ManagerPermissionDTO permissionRes = new ManagerPermissionDTO();
            permissionRes.setName(PermissionModuleEnum.getDesc(moduleKey));
            permissionRes.setPermissionSid(permissionRes.getPermissionSid() == null
                    ? RandomUtil.randomNumbers(10) + suffix : permissionRes.getPermissionSid());

            //填充controller分类集合
            Map<String, List<UserPermission>> controllerMap = moduleList.stream()
                    .collect(Collectors.groupingBy(UserPermission::getControllerCategory, LinkedHashMap::new, Collectors.toList()));
            controllerMap.forEach((controllerKey, controllerList) -> {
                ManagerPermissionDTO moduleCategoryDto = new ManagerPermissionDTO();
                moduleCategoryDto.setName(PermissionControllerEnum.getDesc(controllerKey));
                //前端需要随机数id
                moduleCategoryDto.setPermissionSid(moduleCategoryDto.getPermissionSid() == null
                        ? RandomUtil.randomNumbers(10) + suffix : moduleCategoryDto.getPermissionSid());
                moduleCategoryDto.setChild(BeanUtil.copyToList(controllerList, ManagerPermissionDTO.class));
                permissionRes.getChild().add(moduleCategoryDto);
            });
            menuPermissionRes.add(permissionRes);
        });

        res.setPermissionData(menuPermissionRes);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean menuAuthorize(ManagerMenuAuthorizeReq req) {
        checkDevConfig();
        //删除菜单关联的权限
        menuPermissionService.remove(SpecWrapperUtil.<UserMenuPermission>builder().eq("menu_sid", req.getMenuSid()));
        //新增菜单关联的权限
        if (CollectionUtil.isNotEmpty(req.getPermissionSids())) {
            List<UserMenuPermission> menuPermissionList = new ArrayList();
            req.getPermissionSids().forEach(e -> {
                UserMenuPermission menuPermission = new UserMenuPermission();
                menuPermission.setMenuSid(req.getMenuSid());
                menuPermission.setPermissionSid(e);
                menuPermissionList.add(menuPermission);
            });
            menuPermissionService.saveBatch(menuPermissionList);
        }
        return true;
    }

    @Override
    public UserMenuDetailRes getUserMenuDetail(Long menuSid) {
        UserMenu userMenu = menuMapper.selectById(menuSid);
        return BeanUtil.copyProperties(userMenu, UserMenuDetailRes.class);
    }

    /**
     * 更新子集
     */
    private void updateChildMenus(UserMenu userMenu) {
        List<UserMenu> children = menuMapper.selectList(
                SpecWrapperUtil.<UserMenu>builder().eq("parent_id", userMenu.getMenuSid()));
        if (ObjectUtils.isEmpty(children)) {
            return;
        }
        children.forEach(v -> {
            v.setPathIds(userMenu.getLowerPathIds());
            v.setLowerPathIds(userMenu.getLowerPathIds() + v.getMenuSid() + "/");
            menuMapper.updateById(v);
            updateChildMenus(v);
        });
    }


    /**
     * 填充子菜单数据
     *
     * @param list 父级列表
     */
    private List<UserMenuTreeRes> fillData(List<UserMenuTreeRes> list) {
        list.forEach(v -> {
            v.setStatusName(MenuStatusEnum.getDesc(v.getStatus()));

            List<UserMenu> childMenu = menuMapper.selectList(
                    SpecWrapperUtil.<UserMenu>builder().eq("parent_id", v.getMenuSid()).eq("display", "true").orderByAsc("sort"));
            //查询是否有下级菜单
            v.setChild(false);
            if (CollectionUtil.isNotEmpty(childMenu)) {
                //如果有子菜单，递归查询子菜单
                v.setChild(true);
                List<UserMenuTreeRes> childList = BeanUtil.copyToList(childMenu, UserMenuTreeRes.class);
                v.setChildMenu(fillData(childList));
            }
        });
        return list;
    }

    /**
     * 检查菜单参数
     *
     * @param menu 菜单
     */
    private void checkMenu(UserMenu menu) {
        //检查重名
        long count = this.count(Wrappers.<UserMenu>lambdaQuery().eq(UserMenu::getCode, menu.getCode()));
        if (count > 0) {
            BizError.e(BizErrorEnum.MSG_1035_MENU_EXIST);
        }
    }

    /**
     * 检查开发配置
     */
    private void checkDevConfig() {
        SysMConfigDto config = configService.getConfigByConfigKey(devConfig);
        if (Objects.nonNull(config)) {
            if (config.getConfigValue().equalsIgnoreCase("false")) {
                BizError.e(BizErrorEnum.MSG_1011_TOKEN_ERROR);
            }
        }
    }
}




