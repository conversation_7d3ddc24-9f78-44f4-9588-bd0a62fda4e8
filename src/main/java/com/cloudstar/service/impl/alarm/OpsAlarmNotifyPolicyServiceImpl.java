package com.cloudstar.service.impl.alarm;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RightCloudResult;
import com.cloudstar.sdk.monitor.client.MonitorClient;
import com.cloudstar.sdk.monitor.form.OpsNotifyPolicyCreateForm;
import com.cloudstar.sdk.monitor.form.OpsNotifyPolicyGetNameForm;
import com.cloudstar.sdk.monitor.form.OpsNotifyPolicyQueryForm;
import com.cloudstar.sdk.monitor.form.OpsNotifyPolicyUpdateForm;
import com.cloudstar.sdk.monitor.result.OpsNotifyPolicyDetailResult;
import com.cloudstar.sdk.monitor.result.OpsNotifyPolicyGetByNameResult;
import com.cloudstar.sdk.monitor.result.OpsNotifyPolicyPageResult;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.OpsAlarmNotifyPolicyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
@Slf4j
public class OpsAlarmNotifyPolicyServiceImpl implements OpsAlarmNotifyPolicyService {

    private final MonitorClient monitorClient;

    @Override
    public Rest<PageResult<OpsNotifyPolicyPageResult>> getNotifyPolicyPage(OpsNotifyPolicyQueryForm queryNotifyPolicyForm) {
        return convertToRest(monitorClient.getNotifyPolicyPage(queryNotifyPolicyForm));
    }

    @Override
    public Rest<OpsNotifyPolicyDetailResult> getNotifyPolicyDetails(Long id) {
        return convertToRest(monitorClient.getNotifyPolicyDetails(id));
    }

    @Override
    public Rest<Long> createNotifyPolicy(OpsNotifyPolicyCreateForm notifyPolicyDetailsForm) {
        return convertToRest(monitorClient.createNotifyPolicy(notifyPolicyDetailsForm));
    }

    @Override
    public Rest<Boolean> updateNotifyPolicy(OpsNotifyPolicyUpdateForm notifyPolicyDetailsForm) {
        return convertToRest(monitorClient.updateNotifyPolicy(notifyPolicyDetailsForm));
    }

    @Override
    public Rest<Boolean> updateNotifyPolicyStatus(Long id) {
        return convertToRest(monitorClient.updateNotifyPolicyStatus(id));
    }

    @Override
    public Rest<Boolean> deleteNotifyPolicy(List<Long> ids) {
        return convertToRest(monitorClient.deleteNotifyPolicy(ids));
    }

    @Override
    public Rest<List<OpsNotifyPolicyGetByNameResult>> getNotifyPolicyList(OpsNotifyPolicyGetNameForm form) {
        return convertToRest(monitorClient.getNotifyPolicyList(form));
    }

    /**
     * 将 PageResult 转换为 Rest 响应对象
     */
    private <T> Rest<T> convertToRest(RightCloudResult<T> page) {
        Rest<T> rest = new Rest<>();
        rest.setCode(page.getCode());
        rest.setMessage(page.getMessage());
        rest.setData(page.getData());
        rest.setSuccess(page.isSuccess());
        return rest;
    }
}
