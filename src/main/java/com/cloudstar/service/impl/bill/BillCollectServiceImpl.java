package com.cloudstar.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudstar.common.base.enums.BizBillEnum;
import com.cloudstar.common.base.enums.ProductComponentEnum;
import com.cloudstar.dao.mapper.bill.HwsSdrCollectRecordMapper;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.model.bill.BizBillUsageItem;
import com.cloudstar.dao.model.bill.HwsSdrCollectRecord;
import com.cloudstar.dao.model.bill.HwsSdrDrpData;
import com.cloudstar.dao.model.bill.HwsSdrModelartsData;
import com.cloudstar.dao.model.bill.HwsSdrObsData;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.enums.CollectorTypeEnum;
import com.cloudstar.service.facade.bill.BillCollectService;
import com.cloudstar.service.facade.bill.BizBillUsageItemService;
import com.cloudstar.service.facade.bill.HwsSdrDrpDataService;
import com.cloudstar.service.facade.bill.HwsSdrModelartsDataService;
import com.cloudstar.service.facade.bill.HwsSdrObsDataService;
import com.cloudstar.service.facade.cluster.ClusterUserMappingService;
import com.cloudstar.service.pojo.dto.cluster.ClusterUserMappingBillDto;
import com.cloudstar.service.pojo.dto.collect.Collector;
import com.cloudstar.service.pojo.dto.collect.SdrCollectorAndRecord;
import com.cloudstar.service.utils.CsvUtil;
import com.cloudstar.service.utils.FileUtil;

import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 话单采集
 *
 * <AUTHOR>
 * @date 2022/12/5 15:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BillCollectServiceImpl implements BillCollectService {

    HwsSdrCollectRecordMapper hwsSdrCollectRecordMapper;

    ClusterUserMappingService clusterUserMappingService;

    ClusterEntityMapper clusterEntityMapper;

    BizBillUsageItemService usageItemService;

    HwsSdrObsDataService hwsSdrObsDataService;

    HwsSdrDrpDataService hwsSdrDrpDataService;

    HwsSdrModelartsDataService hwsSdrModelartsDataService;


    /**
     * 采集方式1 现用于obs,modelarts,sfs 话单采集
     *
     * @throws Exception 异常
     */
    @Override
    public void collector(String filePath, String adapterUuid, String fileDate) throws Exception {
        log.info("开始解压文件 dir:{}", replaceTarGz(filePath));
        FileUtil.deCompressGZipFile(filePath, replaceTarGz(filePath));
        // 获取到.tar.gz解压后文件夹地址
        log.info("获取到.tar.gz解压后文件夹地址 {}", filePath);
        String unTarGzPath = replaceTarGz(filePath);
        ClusterEntity clusterEntity = clusterEntityMapper.selectOne(
                new QueryWrapper<ClusterEntity>().lambda().eq(ClusterEntity::getAdapterUuid, adapterUuid));
        // 查询出所有租户
        List<ClusterUserMappingBillDto> users = clusterUserMappingService.getAllUserByClusterId(clusterEntity.getId());
        if (CollectionUtil.isNotEmpty(users)) {
            //解析modelarts
            analysisFile(unTarGzPath, CollectorTypeEnum.MODELARTS, adapterUuid, fileDate, users);
            //解析Obs
            analysisFile(unTarGzPath, CollectorTypeEnum.OBS, adapterUuid, fileDate, users);
        }
        // 采集完成后删除临时压缩文件
        FileUtil.deleteFile(replaceTarGz(filePath), filePath);

    }

    private void analysisFile(String unTarGzPath, CollectorTypeEnum collectorType, String adapterUuid, String fileDate,
                              List<ClusterUserMappingBillDto> users) throws Exception {
        // 获取到对应目录下全部话单zip压缩文件
        log.info("获取到对应目录下全部话单zip压缩文件 unTarGzPath:{}", unTarGzPath);
        String hwsZipPath = getHwsZipPath(unTarGzPath, collectorType);
        log.info("hwsZipPath:{}", hwsZipPath);
        Map<String, String> zipMap = FileUtil.zipPaths(hwsZipPath);
        log.info("zipMap:{}", zipMap.size());
        for (Map.Entry<String, String> entry : zipMap.entrySet()) {
            log.info("entry.getKey():{}", entry.getKey());
            // 校验是否是话单zip文件
            if (checkHwsZip(entry.getKey())) {
                // 解压话单zip文件
                log.info("开始解压zip文件,entry.getValue():{}", entry.getValue());
                ZipUtil.unzip(entry.getValue());
                String unzipPath = replaceZip(entry.getValue());
                log.info("unzipPath:{}", unzipPath);
                Map<String, String> zipMap1 = FileUtil.zipPaths(unzipPath);
                for (Map.Entry<String, String> entry1 : zipMap1.entrySet()) {
                    // 校验是否是话单zip文件,因为里面还有个md5文件
                    log.info("校验是否是话单zip文件,因为里面还有个md5文件");
                    if (checkHwsZip(entry1.getKey())) {
                        // 解压话单zip文件,解压后会生成话单csv文件
                        log.info("解压话单zip文件,解压后会生成话单csv文件");
                        ZipUtil.unzip(entry1.getValue());
                        String csvPath = replaceZip(entry1.getValue()) + "/" + replaceZipToCsv(entry1.getKey());
                        String csvName = replaceZipToCsv(entry1.getKey());
                        // 验证是否已经采集过此话单,没有采集过才入库
                        log.info("开始验证是否已经采集过此话单,没有采集过才入库csvPath:{},csvName:{}", csvPath, csvName);
                        //判断是否解析过该话单
                        LambdaQueryWrapper<HwsSdrCollectRecord> query = new QueryWrapper<HwsSdrCollectRecord>().lambda()
                                                                                                               .eq(HwsSdrCollectRecord::getFilePath,
                                                                                                                   csvPath)
                                                                                                               .eq(HwsSdrCollectRecord::getFileName,
                                                                                                                   csvName)
                                                                                                               .eq(HwsSdrCollectRecord::getType,
                                                                                                                   collectorType.getCode());
                        Long fileNum = hwsSdrCollectRecordMapper.selectCount(query);
                        if (fileNum == 0) {
                            log.info("开始解析话单");
                            // 解析话单
                            SdrCollectorAndRecord sdrCollectorAndRecord = CsvUtil.readCsv(csvPath, csvName, collectorType,
                                                                                          adapterUuid, fileDate);
                            if (CollectorTypeEnum.OBS.getCode().equals(collectorType.getCode())
                                    && CollectionUtil.isNotEmpty(sdrCollectorAndRecord.getCollectorList())) {
                                saveObs(sdrCollectorAndRecord.getCollectorList(), users, adapterUuid);
                            }
                            if (CollectorTypeEnum.MODELARTS.getCode().equals(collectorType.getCode())
                                    && CollectionUtil.isNotEmpty(sdrCollectorAndRecord.getCollectorList())) {
                                saveModelArts(sdrCollectorAndRecord.getCollectorList(), users, adapterUuid);
                            }
                            if (CollectionUtil.isNotEmpty(sdrCollectorAndRecord.getDrpCollectorList())) {
                                saveDrpData(sdrCollectorAndRecord.getDrpCollectorList(), users, adapterUuid);
                            }
                            if (ObjectUtil.isNotEmpty(sdrCollectorAndRecord.getSdrCollectionRecord())) {
                                hwsSdrCollectRecordMapper.insert(sdrCollectorAndRecord.getSdrCollectionRecord());
                            }
                            // 入库
                            log.info("开始入库");
                        }
                    }
                }
            }
        }
    }

    /**
     * 保存obs话单
     *
     * @param collectorList 专属资源池话单列表
     * @param users 用户
     * @param adapterUuid 集群id
     */
    private void saveObs(List<Collector> collectorList, List<ClusterUserMappingBillDto> users, String adapterUuid) {
        List<HwsSdrObsData> dataList = new ArrayList<>();
        List<BizBillUsageItem> billItemList = new ArrayList<>();
        collectorList.stream().forEach(collector -> {
            users.stream().forEach(user -> {
                if (collector.getAccountId().equals(user.getObsId())) {
                    HwsSdrObsData data = BeanUtil.copyProperties(collector, HwsSdrObsData.class);
                    data.setCsbparams(collector.getCsbParams());
                    data.setBillFlag("Y");
                    data.setUserId(user.getUserSid());
                    data.setAccountUuid(user.getUserId());
                    data.setAdapterUuid(adapterUuid);
                    dataList.add(data);
                    BizBillUsageItem billUsageItem = buildObsBillItem(collector, user.getUserSid(), adapterUuid);
                    if (Objects.nonNull(billUsageItem)) {
                        billItemList.add(billUsageItem);
                    }
                }
            });
        });
        hwsSdrObsDataService.saveBatch(dataList, 200);
        // 出账
        usageItemService.saveBatch(billItemList, 200);
    }

    private BizBillUsageItem buildObsBillItem(Collector collector, Long userSid, String adapterUUid) {
        if (StrUtil.isEmpty(collector.getResourceId())) {
            return null;
        }
        if (!StrUtil.equals(collector.getAccumulateFactorName(), "size")) {
            return null;
        }
        if (StrUtil.isEmpty(collector.getAccumulateFactorValue())
                || StrUtil.equals(collector.getAccumulateFactorValue(), "0")) {
            return null;
        }
        String value = collector.getAccumulateFactorValue();
        return BizBillUsageItem.builder()
                               .billNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSSS")))
                               .billingCycle(getBillingCycle(collector.getBeginTime()))
                               .jobId(collector.getResourceId())
                               .configuration(collector.getResourceSpecCode())
                               .ownerSid(userSid)
                               .product(ProductComponentEnum.OBS.getKey().get(0))
                               .summaryFlag("N")
                               .usageCount(new BigDecimal(value))
                               .usageStartDate(formatDate(collector.getBeginTime()))
                               .usageEndDate(formatDate(collector.getEndTime()))
                               .billType(BizBillEnum.RESOURCE_BILLING.getType())
                               .adapterUuid(adapterUUid)
                               .build();
    }

    /**
     * 保存modelarts话单
     *
     * @param collectorList 专属资源池话单列表
     * @param users 用户
     * @param adapterUuid 集群id
     */
    private void saveModelArts(List<Collector> collectorList, List<ClusterUserMappingBillDto> users, String adapterUuid) {
        List<HwsSdrModelartsData> dataList = new ArrayList<>();
        List<BizBillUsageItem> billItemList = new ArrayList<>();
        collectorList.stream().forEach(collector -> {
            users.stream().forEach(user -> {
                if (collector.getAccountId().equals(user.getProjectId())) {
                    HwsSdrModelartsData data = BeanUtil.copyProperties(collector, HwsSdrModelartsData.class);
                    if (StrUtil.startWithAnyIgnoreCase(data.getResourceId(), "cache")) {
                        return;
                    }
                    data.setBillFlag("Y");
                    data.setUserId(user.getUserSid());
                    data.setAccountUuid(user.getUserId());
                    data.setAdapterUuid(adapterUuid);
                    dataList.add(data);
                    BizBillUsageItem billUsageItem = buildModelArtsBillItem(collector, user.getUserSid(), adapterUuid);
                    if (Objects.nonNull(billUsageItem)) {
                        billItemList.add(billUsageItem);
                    }
                }
            });
        });
        hwsSdrModelartsDataService.saveBatch(dataList, 200);
        // 出账
        usageItemService.saveBatch(billItemList, 200);
    }

    private BizBillUsageItem buildModelArtsBillItem(Collector collector, Long userSid, String adapterUUid) {
        if (StrUtil.isEmpty(collector.getResourceId())) {
            return null;
        }
        if (!StrUtil.startWithAnyIgnoreCase(collector.getResourceId(), "train-", "training_")) {
            return null;
        }
        if (StrUtil.isEmpty(collector.getAccumulateFactorValue()) || StrUtil.equals(collector.getAccumulateFactorValue(),
                                                                                    "0")) {
            return null;
        }
        String value = collector.getAccumulateFactorValue();
        return BizBillUsageItem.builder()
                               .billNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSSS")))
                               .billingCycle(getBillingCycle(collector.getBeginTime()))
                               .jobId(collector.getResourceId().replace("train-", "").replace("training_", ""))
                               .configuration(collector.getResourceSpecCode())
                               .ownerSid(userSid)
                               .product(ProductComponentEnum.MODELARTS.getKey().get(0))
                               .summaryFlag("N")
                               .usageCount(new BigDecimal(value))
                               .usageStartDate(formatDate(collector.getBeginTime()))
                               .usageEndDate(formatDate(collector.getEndTime()))
                               .billType(BizBillEnum.RESOURCE_BILLING.getType())
                               .adapterUuid(adapterUUid)
                               .build();
    }

    /**
     * 保存专属资源池话单
     *
     * @param drpCollectorList 专属资源池话单列表
     * @param users 用户
     * @param adapterUuid 集群id
     */
    private void saveDrpData(List<Collector> drpCollectorList, List<ClusterUserMappingBillDto> users, String adapterUuid) {
        List<HwsSdrDrpData> dataList = new ArrayList<>();
        List<BizBillUsageItem> billItemList = new ArrayList<>();
        drpCollectorList.stream().forEach(collector -> {
            users.stream().forEach(user -> {
                if (collector.getAccountId().equals(user.getUserId())
                        || collector.getAccountId().equals(user.getObsId())
                        || collector.getAccountId().equals(user.getProjectId())) {
                    HwsSdrDrpData data = BeanUtil.copyProperties(collector, HwsSdrDrpData.class);
                    if (StrUtil.startWithAnyIgnoreCase(data.getResourceId(), "cache")) {
                        return;
                    }
                    data.setBillFlag("Y");
                    data.setUserId(user.getUserSid());
                    data.setAccountUuid(user.getUserId());
                    data.setAdapterUuid(adapterUuid);
                    dataList.add(data);
                    BizBillUsageItem billUsageItem = buildDrpDataBillItem(collector, user.getUserSid(), adapterUuid);
                    if (Objects.nonNull(billUsageItem)) {
                        billItemList.add(billUsageItem);
                    }
                }
            });
        });
        hwsSdrDrpDataService.saveBatch(dataList, 200);
        // 出账
        usageItemService.saveBatch(billItemList, 200);
    }

    private BizBillUsageItem buildDrpDataBillItem(Collector collector, Long userSid, String adapterUUid) {
        if (StrUtil.isEmpty(collector.getResourceId())) {
            return null;
        }
        if (StrUtil.isEmpty(collector.getAccumulateFactorValue()) || StrUtil.equals(collector.getAccumulateFactorValue(),
                                                                                    "0")) {
            return null;
        }
        String value = collector.getAccumulateFactorValue();
        return BizBillUsageItem.builder()
                               .billNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSSS")))
                               .billingCycle(getBillingCycle(collector.getBeginTime()))
                               .jobId(collector.getResourceId())
                               .configuration(collector.getResourceSpecCode())
                               .ownerSid(userSid)
                               .product(ProductComponentEnum.DEDICATEDRESOURCEPOOL.getKey().get(0))
                               .summaryFlag("N")
                               .usageCount(new BigDecimal(value))
                               .usageStartDate(formatDate(collector.getBeginTime()))
                               .usageEndDate(formatDate(collector.getEndTime()))
                               .billType(BizBillEnum.RESOURCE_BILLING.getType())
                               .adapterUuid(adapterUUid)
                               .build();
    }

    /**
     * 判断文件是否存在
     *
     * @param file 文件
     */
    private static void checkFileExists(File file) {
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 判断文件夹是否存在
     *
     * @param file 文件
     */
    private static void checkDirExists(File file) {
        if (!file.exists()) {
            file.mkdirs();
        }
    }


    private static String replaceTarGz(String tarGzPath) {
        return tarGzPath.replaceAll(".tar.gz", "");
    }

    private static String replaceZip(String zipPath) {
        return zipPath.replaceAll(".zip", "");
    }

    private static String replaceZipToCsv(String zipPath) {
        return zipPath.replaceAll(".zip", ".csv");
    }


    private static String getHwsZipPath(String unTarGzPath, CollectorTypeEnum collectorType) {
        StringBuffer hwsZipPath = new StringBuffer(unTarGzPath);
        hwsZipPath.append("/bakfiles/");
        switch (collectorType.getCode()) {
            case 1:
                hwsZipPath.append("obs");
                break;
            case 2:
                hwsZipPath.append("modelarts");
                break;
            case 3:
                hwsZipPath.append("sfs");
                break;
            case 4:
                hwsZipPath.append("hpc");
                break;
            default:
                log.info("getHwsZipPath 没有找到此采集类型!");
        }
        return hwsZipPath.toString();
    }

    /**
     * 校验是否是话单zip文件
     */
    private Boolean checkHwsZip(String hwsZipFileName) {
        if (!hwsZipFileName.startsWith("HWS")) {
            return false;
        }
        if (!hwsZipFileName.endsWith(".zip")) {
            return false;
        }
        return true;
    }

    private Date formatDate(String time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        DateTime dateTime;
        try {
            dateTime = new DateTime(dateFormat.parse(time));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return dateTime;
    }

    /**
     * 获取账单周期
     *
     * @param billingCycle 账单日期
     */
    private String getBillingCycle(String billingCycle) {
        DateTime parse = DateUtil.parse(billingCycle, DatePattern.PURE_DATETIME_PATTERN);
        return DateUtil.format(parse, DatePattern.NORM_MONTH_PATTERN);
    }


}
