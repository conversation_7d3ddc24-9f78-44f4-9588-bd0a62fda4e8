package com.cloudstar.service.impl.bill;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.AsyncDownloadService;
import com.cloudstar.AsyncTaskService;
import com.cloudstar.bean.dto.BizDownloadDto;
import com.cloudstar.bean.enums.BizOperationType;
import com.cloudstar.bean.pojo.AsyncFileInfoParam;
import com.cloudstar.bean.pojo.AsyncInfoParam;
import com.cloudstar.bean.req.BizDownloadReq;
import com.cloudstar.common.base.constant.UserTypeEnum;
import com.cloudstar.common.base.enums.BizBillEnum;
import com.cloudstar.common.base.enums.ProductComponentEnum;
import com.cloudstar.common.base.util.FileSizeUtil;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.mapper.bill.BizBillUsageItemSummaryMonthMapper;
import com.cloudstar.dao.model.bill.BizBillUsageItemSummaryMonth;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.bill.BizBillUsageItemSummaryMonthService;
import com.cloudstar.service.pojo.vo.requestvo.bill.BizBillUsageItemSummaryExportPageReq;
import com.cloudstar.service.pojo.vo.requestvo.bill.BizBillUsageItemSummaryPageReq;
import com.cloudstar.service.pojo.vo.responsevo.bill.BillDownloadResp;
import com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemSummaryExportResp;
import com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemSummaryPageResp;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;
import java.util.UUID;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * 作业计量统计
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class BizBillUsageItemSummaryMonthServiceImpl extends
        ServiceImpl<BizBillUsageItemSummaryMonthMapper, BizBillUsageItemSummaryMonth> implements
        BizBillUsageItemSummaryMonthService {

    private static final long NH = 1000 * 60 * 60;
    private static final long NM = 1000 * 60;


    AsyncDownloadService asyncDownloadService;

    /**
     * 获取作业计量统计列表
     *
     * @param req 要求事情
     *
     * @return {@link PageResult}<{@link BizBillUsageItemSummaryPageResp}>
     */
    @Override
    public PageResult<BizBillUsageItemSummaryPageResp> getBillUsageSummaryMonthList(
            BizBillUsageItemSummaryPageReq req) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Page<BizBillUsageItemSummaryPageResp> pageQuery = new Page<>(req.getPageNo(), req.getPageSize());
        if (!ObjectUtils.isEmpty(req.getSortDataField())) {
            if (WebUtil.checkFields(new BizBillUsageItemSummaryPageResp(), req.getSortDataField())) {
                req.setSortDataField(req.getSortDataField() + " " + (req.getAsc() ? "asc" : "desc"));
            }
        } else {
            req.setSortDataField("bill_no desc");
        }
        if (StrUtil.equals(authUser.getUserType(), UserTypeEnum.SUB_ACCOUNT.getType())) {
            req.setUserSid(authUser.getParentSid());
        } else {
            req.setUserSid(authUser.getUserSid());
        }
        Page<BizBillUsageItemSummaryPageResp> list = baseMapper.getBillUsageSummaryMonthList(pageQuery, req);
        list.getRecords().forEach(resp -> {
            resp.setBillTypeName(BizBillEnum.getDesc(resp.getBillType()));
            resp.setProductName(ProductComponentEnum.keyFromDesc(resp.getProduct()));
            long valueLong = resp.getUsageCount().longValue() * 1000;
            long hou = millsToHour(valueLong);
            long min = millsToMinute(valueLong);
            long sec = millsToSecond(valueLong);
            if (StrUtil.equals(resp.getProduct(), ProductComponentEnum.OBS.getKey().get(0))) {
                resp.setUsageCountStr(FileSizeUtil.getFileSize(resp.getUsageCount().toString()));
            } else if (StrUtil.equals(resp.getProduct(), ProductComponentEnum.BMS_OBS.getKey().get(0))) {
                resp.setUsageCountStr(FileSizeUtil.getFileSize(resp.getUsageCount().toString()));
            } else if (StrUtil.equals(resp.getProduct(), ProductComponentEnum.MODELARTS.getKey().get(0))) {
                resp.setUsageCountStr(convertHmsStr(hou, min, sec));
            } else if (StrUtil.equals(resp.getProduct(),
                                      ProductComponentEnum.DEDICATEDRESOURCEPOOL.getKey().get(0))) {
                resp.setUsageCountStr(convertHmsStr(hou, min, sec));
            } else if (StrUtil.equals(resp.getProduct(),
                                      ProductComponentEnum.AOSS.getKey().get(0))) {
                resp.setUsageCountStr(FileSizeUtil.getFileSize(resp.getUsageCount().toString()));
            } else if (StrUtil.equals(resp.getProduct(),
                                      ProductComponentEnum.SLURM_ACP.getKey().get(0))) {
                resp.setUsageCountStr(convertHmsStr(hou, min, sec));
            } else if (StrUtil.equals(resp.getProduct(),
                                      ProductComponentEnum.BMS_JOB.getKey().get(0))
                    || StrUtil.equals(resp.getProduct(),
                                      ProductComponentEnum.BMS_NOTEBOOK.getKey().get(0))
                    || StrUtil.equals(resp.getProduct(),
                                      ProductComponentEnum.BMS_STORAGE.getKey().get(0))) {
                resp.setUsageCountStr(convertHmsStr(hou, min, sec));
            }
        });
        return PageResult.of(list);
    }

    /**
     * 导出作业计量汇总
     *
     * @param req 要求事情
     */
    @Override
    public void exportBillSummaryMonth(BizBillUsageItemSummaryExportPageReq req) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        asyncDownloadService.asyncTask(new AsyncTaskService() {

            @Override
            public AsyncInfoParam info() {
                return AsyncInfoParam.builder()
                                     .userId(authUser.getUserSid())
                                     .userType(authUser.getUserType())
                                     .username(authUser.getAccount())
                                     .orgSid(authUser.getOrgSid())
                                     .email(authUser.getEmail())
                                     .operationType(BizOperationType.BILL_SUMMARY)
                                     .build();
            }

            @Override
            public AsyncFileInfoParam fileInfo() {
                return AsyncFileInfoParam.builder()
                                         .fileName("作业计量统计.xlsx")
                                         .compressPassword(UUID.randomUUID().toString().substring(0, 8))
                                         .build();
            }

            @Override
            public InputStream doTaskService() {
                BizBillUsageItemSummaryPageReq pageReq = BeanUtil.copyProperties(req,
                                                                                 BizBillUsageItemSummaryPageReq.class);
                pageReq.setSortDataField("bill_no desc");
                if (StrUtil.equals(authUser.getUserType(), UserTypeEnum.SUB_ACCOUNT.getType())) {
                    pageReq.setUserSid(authUser.getParentSid());
                } else {
                    pageReq.setUserSid(authUser.getUserSid());
                }
                Integer pageNo = 0;
                Integer pageSize = 10000;
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ExcelWriter excelWriter = EasyExcel.write(out, BizBillUsageItemSummaryExportResp.class).build();
                WriteSheet writeSheet = EasyExcel.writerSheet("作业计量统计").build();
                while (true) {
                    pageNo++;
                    Page<BizBillUsageItemSummaryExportResp> page = new Page<>(pageNo, pageSize);
                    Page<BizBillUsageItemSummaryExportResp> respPage = baseMapper.getBillSummaryMonthExportList(page,
                                                                                                                pageReq);
                    List<BizBillUsageItemSummaryExportResp> list = respPage.getRecords();
                    if (pageNo > 1 && ObjectUtils.isEmpty(list)) {
                        break;
                    }
                    list.forEach(resp -> {
                        resp.setBillTypeName(BizBillEnum.getDesc(resp.getBillType()));
                        resp.setProductName(ProductComponentEnum.keyFromDesc(resp.getProduct()));
                        long valueLong = resp.getUsageCount().longValue() * 1000;
                        long hou = millsToHour(valueLong);
                        long min = millsToMinute(valueLong);
                        long sec = millsToSecond(valueLong);
                        if (StrUtil.equals(resp.getProduct(), ProductComponentEnum.OBS.getKey().get(0))) {
                            resp.setUsageCountStr(FileSizeUtil.getFileSize(resp.getUsageCount().toString()));
                        } else if (StrUtil.equals(resp.getProduct(), ProductComponentEnum.MODELARTS.getKey().get(0))) {
                            resp.setUsageCountStr(convertHmsStr(hou, min, sec));
                        } else if (StrUtil.equals(resp.getProduct(),
                                                  ProductComponentEnum.DEDICATEDRESOURCEPOOL.getKey().get(0))) {
                            resp.setUsageCountStr(convertHmsStr(hou, min, sec));
                        } else if (StrUtil.equals(resp.getProduct(),
                                                  ProductComponentEnum.AOSS.getKey().get(0))) {
                            resp.setUsageCountStr(FileSizeUtil.getFileSize(resp.getUsageCount().toString()));
                        } else if (StrUtil.equals(resp.getProduct(),
                                                  ProductComponentEnum.SLURM_ACP.getKey().get(0))) {
                            resp.setUsageCountStr(convertHmsStr(hou, min, sec));
                        }
                    });
                    excelWriter.write(list, writeSheet);
                    if (pageNo == 1 && ObjectUtils.isEmpty(list)) {
                        return null;
                    }
                    list.clear();
                }
                excelWriter.finish();
                return new ByteArrayInputStream(out.toByteArray());
            }
        });
    }

    /**
     * 下载列表
     *
     * @param req 要求事情
     *
     * @return {@link PageResult}<{@link BillDownloadResp}>
     */
    @Override
    public PageResult<BillDownloadResp> downloadList(BizDownloadReq req) {
        //当前用户
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        req.setUserId(authUser.getUserSid());
        req.setOperationType(BizOperationType.BILL_SUMMARY);
        //查询
        PageResult<BizDownloadDto> page = asyncDownloadService.selectByQuery(req);
        return PageResult.of(page, BillDownloadResp.class);
    }

    /**
     * 下载
     *
     * @param downloadId 下载id
     * @param response 响应
     */
    @Override
    public void download(Long downloadId, HttpServletResponse response) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        asyncDownloadService.downloadById(downloadId, response, authUser.getEmail());
    }

    /**
     * 小时
     *
     * @param value 价值
     *
     * @return long
     */
    private long millsToHour(long value) {
        return value / NH;
    }

    /**
     * 米尔斯,分钟 毫秒，秒转小时
     *
     * @param value 价值
     *
     * @return long
     */
    private long millsToMinute(long value) {
        return (value % NH) / NM;
    }

    /**
     * 工厂第二 毫秒，秒转小时
     *
     * @param value 价值
     *
     * @return long
     */
    private long millsToSecond(long value) {
        return ((value % NH) % NM) / 1000;
    }


    /**
     * 转换hmsstr
     *
     * @param houLong 侯长
     * @param minLong 分钟长
     * @param secLong 秒长
     *
     * @return {@link String}
     */
    private String convertHmsStr(long houLong, long minLong, long secLong) {
        return (houLong > 0 ? houLong + "小时" + minLong + "分" : (minLong > 0 ? minLong + "分" : "")) + secLong + "秒";
    }

    public static String getFileSize(String size) {
        double length = Double.parseDouble(size);
        //如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (length < 1024) {
            return length + "B";
        } else {
            length = length / 1024.0;
        }
        //如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        //因为还没有到达要使用另一个单位的时候
        //接下去以此类推
        if (length < 1024) {
            return Math.round(length * 100) / 100.0 + "KB";
        } else {
            length = length / 1024.0;
        }
        if (length < 1024) {
            //因为如果以MB为单位的话，要保留最后1位小数，
            //因此，把此数乘以100之后再取余
            return Math.round(length * 100) / 100.0 + "MB";
        } else {
            //否则如果要以GB为单位的，先除于1024再作同样的处理
            return Math.round(length / 1024 * 100) / 100.0 + "GB";
        }
    }
}
