package com.cloudstar.service.impl.collect;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.mapper.collect.CollectAllocateGpuMapper;
import com.cloudstar.dao.model.collect.CollectAllocateGpu;
import com.cloudstar.service.facade.collect.CollectAllocateGpuService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Service
@DS("monitor")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class CollectAllocateGpuServiceImpl extends ServiceImpl<CollectAllocateGpuMapper, CollectAllocateGpu> implements
        CollectAllocateGpuService {

    CollectAllocateGpuMapper collectAllocateGpuMapper;

    @Override
    public Integer selectUnusedGpu(Long clusterId, String poolId) {
        return collectAllocateGpuMapper.selectUnusedGpu(clusterId, poolId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void dataCleanFromWeekAgo() {
        //查询一周前的每日平均值
        final List<CollectAllocateGpu> gpus = collectAllocateGpuMapper.selectDayAvgGpu();
        log.info("7天前每日gpu平均值数据:{}", gpus);
        if (CollectionUtil.isNotEmpty(gpus)) {
            //删除一周前的所有数据
            final Integer integer = collectAllocateGpuMapper.deleteWeekAgoData();
            log.info("7天前gpu数据已删除:{}条", integer);
            //批量新增数据
            List<List<CollectAllocateGpu>> partition = ListUtil.partition(gpus, 500);
            if (CollectionUtil.isNotEmpty(partition)) {
                partition.stream().forEach(list -> {
                    //批量新增
                    this.saveBatch(list);
                    log.info("已批量新增{}条gpu平均值数据", list.size());
                });
            }
        }

    }
}