package com.cloudstar.service.impl.datastorage;

import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.dao.mapper.datastorage.DataStorageResourcesMapper;
import com.cloudstar.service.facade.datastorage.DataStorageResourcesGrpcService;
import com.cloudstar.service.grpc.AgentObsProto.SyncDataStorageProtoResponse;
import com.cloudstar.service.grpc.AgentObsProto.existsFileOrFileDirResponse;
import com.cloudstar.service.grpcservice.facade.AgentObsService;
import com.cloudstar.service.pojo.dto.datastorage.DataStorageRedisDto;
import com.cloudstar.service.pojo.dto.datastorage.DataStorageResourcesDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * 数据存储资源服务--用于对接适配器的服务。
 * 对接数据库的服务，请放到training-job模块里边
 *
 * <AUTHOR>
 * @date 2022/08/26
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class DataStorageResourcesGrpcServiceImpl implements DataStorageResourcesGrpcService {

    RedisUtil redis;
    DataStorageResourcesMapper mapper;


    /**
     * 同步数据存储
     */
    @Override
    public void syncDataStorage() {
        List<DataStorageResourcesDto> list = mapper.getList(null);
        list.forEach(dataStorageResourcesDto -> {
            try {
                syncDataStorageById(dataStorageResourcesDto.getId());

            } catch (Exception e) {
                log.error("同步数据资源错误：{},{}", dataStorageResourcesDto.getId(), e.getMessage());
            }
        });
    }
    /**
     * 同步数据存储
     */
    @Override
    public void syncDataStorageById(Long resourceId) {
        DataStorageResourcesDto dto = mapper.getDataResourceById(resourceId);
        AgentObsService service = AgentObsService.build(dto.getAdapterUuid());
        String[] split = dto.getFilePath().split("prefix=");
        if (split.length < 2) {
            return;
        }
        try {
            if (dto.getAdapterUuid().equals("k8s-service")) {
                dto.setUserId(dto.getBucketName());
            }
            // 先查看文件是否存在
            existsFileOrFileDirResponse existsFile = service.existsFileOrFileDir(dto.getUserId(), split[1]);
            if (existsFile.getIsExists()) {
                SyncDataStorageProtoResponse response = service.syncDataStorage(dto.getUserId(), split[1],
                        true);
                String redisKey = String.format(RedisCacheKeyEnum.DATA_STORAGE.getKey(), dto.getClusterId(),
                        dto.getId());
                DataStorageRedisDto build = DataStorageRedisDto.builder()
                        .objectNum(response.getFileNum())
                        .storageUsage(response.getStorageUsage())
                        .ownerId(dto.getOwnerId())
                        .clusterType(dto.getClusterType())
                        .ownerName(dto.getOwnerName()).build();
                log.info("数据资源同步{}数据:{}", dto.getId(), response.getStorageUsage());
                redis.hPutAll(redisKey, BeanUtil.copyProperties(build, Map.class));
            }
        } catch (Exception e) {
            log.error("同步数据资源错误：{},{}", dto.getId(), e.getMessage());
        }
    }

    @Override
    public DataStorageRedisDto findUsageById(Long resourceId) {
        //1、先从redis取数据
        String clusterKey = String.format(RedisCacheKeyEnum.DATA_STORAGE.getKey(), "*", resourceId);
        Set<String> keys = redis.scan(clusterKey, 1);
        for (String key : keys) {
            DataStorageRedisDto dataStorageRedisResp = BeanUtil.copyProperties(redis.hGetAll(key),
                                                                               DataStorageRedisDto.class);
            return dataStorageRedisResp;
        }

        //2、如果redis没有数据，从适配器获取
        List<DataStorageResourcesDto> list = mapper.getList(resourceId);
        if (CollectionUtil.isNotEmpty(list)) {
            DataStorageResourcesDto dto = list.get(0);
            String[] split = dto.getFilePath().split("prefix=");
            if (split.length < 2) {
                return null;
            }
            AgentObsService service = AgentObsService.build(dto.getAdapterUuid());
            try {
                // 先查看文件是否存在
                existsFileOrFileDirResponse existsFile = service.existsFileOrFileDir(dto.getUserId(), split[1]);
                if (existsFile.getIsExists()) {
                    SyncDataStorageProtoResponse response = service.syncDataStorage(dto.getUserId(), split[1],
                                                                                    true);
                    String redisKey = String.format(RedisCacheKeyEnum.DATA_STORAGE.getKey(), dto.getClusterId(),
                                                    dto.getId());
                    DataStorageRedisDto build = DataStorageRedisDto.builder()
                                                                   .objectNum(response.getFileNum())
                                                                   .storageUsage(response.getStorageUsage())
                                                                   .ownerId(dto.getOwnerId())
                                                                   .ownerName(dto.getOwnerName()).build();
                    redis.hPutAll(redisKey, BeanUtil.copyProperties(build, Map.class));
                    return BeanUtil.copyProperties(build, DataStorageRedisDto.class);
                }
            } catch (Exception e) {
                log.error("同步数据资源错误：{},{}", dto.getId(), e.getMessage());
            }
        }
        return null;
    }

}
