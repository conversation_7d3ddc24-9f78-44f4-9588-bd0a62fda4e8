package com.cloudstar.service.impl.monitor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.cloudstar.common.base.constant.UserTypeEnum;
import com.cloudstar.common.base.enums.AccountTypeEnum;
import com.cloudstar.common.base.enums.ClusterFlagEnum;
import com.cloudstar.common.base.enums.ClusterResPoolTypeEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.cluster.ClusterFlavorMapper;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.mapper.monitor.MonitorMapper;
import com.cloudstar.dao.mapper.notebook.NotebookEntityMapper;
import com.cloudstar.dao.mapper.tenantmapping.ClusterUserMappingMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.bigscreen.CollectJobs;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterFlavor;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.dao.model.notebook.NotebookEntity;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.server.bss.PageOperationAnalysisReq;
import com.cloudstar.sdk.server.bss.TheBarChartReq;
import com.cloudstar.sdk.server.bss.TheLineChartReq;
import com.cloudstar.sdk.server.pojo.ExternalMonitorReq;
import com.cloudstar.sdk.server.pojo.NpuUsageResp;
import com.cloudstar.sdk.server.pojo.PoolJobStatusNumResp;
import com.cloudstar.sdk.server.pojo.PoolJobStatusReq;
import com.cloudstar.sdk.server.pojo.PoolJobStatusResp;
import com.cloudstar.sdk.server.pojo.ResourcePoolResp;
import com.cloudstar.sdk.server.pojo.SystemClusterEntityResDto;
import com.cloudstar.service.facade.bigscreen.CollectJobsService;
import com.cloudstar.service.facade.cluster.ClusterEntityService;
import com.cloudstar.service.facade.cluster.ClusterResourcePoolService;
import com.cloudstar.service.facade.monitor.ExternalMonitorService;
import com.cloudstar.service.pojo.dto.cluster.ClusterUserProjectDto;
import com.cloudstar.service.pojo.dto.monitor.JobProjectIdDto;
import com.cloudstar.service.pojo.dto.monitor.MonitorMetricDto;
import com.cloudstar.service.pojo.dto.monitor.PoolJobStatusDto;
import com.cloudstar.service.pojo.dto.monitor.PoolJobStatusNumDto;
import com.cloudstar.service.pojo.vo.requestvo.monitor.UserResourcePageReq;
import com.cloudstar.service.pojo.vo.responsevo.monitor.ClusterJobInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.PageOperationAnalysisResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.TheBarChartResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.TheLineChartResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.UserResourcePageResp;
import com.cloudstar.service.pojo.vo.responsevo.schedule.ResourceLoadResp;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 对外提供资源池监控数据
 *
 * <AUTHOR>
 * @date 2023/7/5 9:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExternalMonitorServiceImpl implements ExternalMonitorService {

    MonitorMapper monitorMapper;

    ClusterResourcePoolService poolService;

    ClusterEntityService clusterEntityService;

    ClusterUserMappingMapper clusterUserMappingMapper;

    ClusterFlavorMapper clusterFlavorMapper;

    ClusterResourcePoolMapper clusterResourcePoolMapper;

    UserEntityMapper userEntityMapper;

    CollectJobsService collectJobsService;
    private final TrainingJobEntityMapper trainingJobEntityMapper;
    private final NotebookEntityMapper notebookEntityMapper;

    @Override
    public NpuUsageResp getPoolNpuUsage(ExternalMonitorReq req) {
        NpuUsageResp resp = new NpuUsageResp();
        AuthUser authUser = getUser();
        //如果是子账号
        setParentSid(authUser);
        Long systemClusterId = getSystemClusterId();
        if (ClusterResPoolTypeEnum.SHARE.getType().equals(req.getPoolType())) {
            req.setPoolId(getSharePoolId(authUser, systemClusterId));
        } else {
            req.setPoolId(getDrpPoolId(authUser, systemClusterId, req.getPoolName()));
        }
        final ClusterResourcePool pool = getPool(authUser, systemClusterId, req.getPoolId());
        ResourceLoadResp npuDto = monitorMapper.getNpuSchedule(systemClusterId, req.getPoolId());
        log.info("npu使用率情况:{}", npuDto);
        if (ObjectUtil.isNotEmpty(npuDto)) {
            resp.setUnused(npuDto.getUnused());
            resp.setUsed(npuDto.getUsed());
        }
        MonitorMetricDto monitorMetricDto = monitorMapper.selectUnusedNpu(systemClusterId, req.getPoolId());
        log.info("npu使用情况:{}", monitorMetricDto);
        if (ObjectUtil.isNotEmpty(monitorMetricDto)) {
            resp.setNpuUsed(monitorMetricDto.getAllocated());
            resp.setNpuTotal(monitorMetricDto.getCapacity());
        }
        resp.setPoolId(pool.getPoolId());
        resp.setPoolName(pool.getPoolName());
        log.info("npu使用率返回值:{}", resp);
        return resp;
    }

    @Override
    public PoolJobStatusNumResp getPoolJobStatusNum(ExternalMonitorReq req) {
        PoolJobStatusNumResp resp = new PoolJobStatusNumResp();
        AuthUser authUser = getUser();
        //如果是子账号
        setParentSid(authUser);
        Long systemClusterId = getSystemClusterId();
        if (ClusterResPoolTypeEnum.SHARE.getType().equals(req.getPoolType())) {
            req.setPoolId(getSharePoolId(authUser, systemClusterId));
        } else {
            req.setPoolId(getDrpPoolId(authUser, systemClusterId, req.getPoolName()));
        }
        ClusterResourcePool pool = getPool(authUser, systemClusterId, req.getPoolId());
        List<PoolJobStatusNumDto> dtoList = monitorMapper.getJobStatusNum(systemClusterId, req.getPoolId());
        log.info("资源池作业列表:{}", dtoList);
        if (CollectionUtil.isNotEmpty(dtoList)) {
            Map<String, List<PoolJobStatusNumDto>> groupMap = dtoList.stream()
                    .collect(Collectors.groupingBy(
                            dto -> dto.getPoolId() + "-" + dto.getJobStatus()));
            List<PoolJobStatusNumDto> runningList = groupMap.get(pool.getPoolId() + "-" + TrainingJobStatusEnum.RUNNING.getType());
            List<PoolJobStatusNumDto> queueList = groupMap.get(pool.getPoolId() + "-" + TrainingJobStatusEnum.QUEUING.getType());
            resp.setRunningNum(CollectionUtil.isEmpty(runningList) ? 0 : runningList.get(0).getNum());
            resp.setQueueNum(CollectionUtil.isEmpty(queueList) ? 0 : queueList.get(0).getNum());
        }
        resp.setPoolId(pool.getPoolId());
        resp.setPoolName(pool.getPoolName());
        log.info("作业状态数量返回值:{}", resp);
        return resp;
    }

    @Override
    public List<PoolJobStatusResp> getPoolJob(PoolJobStatusReq req) {
        List<PoolJobStatusResp> respList = new ArrayList<>();
        AuthUser authUser = getUser();
        //如果是子账号
        setParentSid(authUser);
        Long systemClusterId = getSystemClusterId();
        if (ClusterResPoolTypeEnum.SHARE.getType().equals(req.getPoolType())) {
            req.setPoolId(getSharePoolId(authUser, systemClusterId));
        } else {
            req.setPoolId(getDrpPoolId(authUser, systemClusterId, req.getPoolName()));
        }
        //获取集群下的所有规格
        List<ClusterFlavor> flavorList = clusterFlavorMapper.selectList(
                new LambdaQueryWrapper<ClusterFlavor>().eq(ClusterFlavor::getClusterId, systemClusterId));
        log.info("资源池规格列表:{}", flavorList);
        Map<String, List<ClusterFlavor>> flavorMap = flavorList.stream().collect(Collectors.groupingBy(dto -> dto.getFlavorId()));
        //获取作业情况
        List<PoolJobStatusDto> jobStatusList = monitorMapper.getJobStatusList(systemClusterId, req.getPoolId(), req.getJobStatus());
        log.info("资源池作业状态列表:{}", jobStatusList);
        //获取用户projectId
        List<ClusterUserProjectDto> projectDtoList = new ArrayList<>();
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            projectDtoList = clusterUserMappingMapper.getUserProjectId(systemClusterId, authUser.getUserSid());
        } else {
            projectDtoList = clusterUserMappingMapper.getUserProjectId(systemClusterId, null);
        }
        log.info("用户projectId列表:{}", projectDtoList);
        Map<String, List<ClusterUserProjectDto>> projectMap = projectDtoList.stream()
                .filter(dto -> ObjectUtil.isNotEmpty(dto.getProjectId()))
                .collect(Collectors.groupingBy(dto -> dto.getProjectId()));
        if (CollectionUtil.isNotEmpty(jobStatusList)) {

            List<String> jobIds = jobStatusList.stream().map(PoolJobStatusDto::getJobId).collect(Collectors.toList());
            List<JobProjectIdDto> jobList = monitorMapper.getJobProjectId(systemClusterId, req.getPoolId(), jobIds);
            log.info("作业project_id信息:{}", jobList);
            Map<String, List<JobProjectIdDto>> projectIdMap = jobList.stream().collect(Collectors.groupingBy(JobProjectIdDto::getJobId));
            //组装数据
            jobStatusList.stream().forEach(dto -> {
                PoolJobStatusResp resp = new PoolJobStatusResp();
                resp.setJobId(dto.getJobId());
                resp.setJobName(dto.getJobName());
                resp.setJobDuration(dto.getJobDuration());
                resp.setJobStatus(dto.getJobStatus());
                resp.setCreateTime(dto.getCreateTime());
                resp.setWaitedTime(dto.getWaitedTime());
                //作业project_id可能为空 需补偿查询下
                if (ObjectUtil.isEmpty(dto.getProjectId())) {
                    log.info("作业project_id为空，作业信息:{}", dto);
                    String jobProjectId = projectIdMap.get(dto.getJobId()).get(0).getProjectId();
                    log.info("补偿查询作业project_id:{}", jobProjectId);
                    dto.setProjectId(jobProjectId);
                }
                List<ClusterUserProjectDto> projectDtos = projectMap.get(dto.getProjectId());
                if (CollectionUtil.isNotEmpty(projectDtos)) {
                    //设置账号名称
                    resp.setAccount(projectDtos.get(0).getAccount());
                }
                if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
                    if (CollectionUtil.isNotEmpty(projectDtos)) {
                        resp.setSelf(true);
                    } else {
                        resp.setSelf(false);
                    }
                }
                List<ClusterFlavor> flavor = flavorMap.get(dto.getFlavorId());
                if (CollectionUtil.isNotEmpty(flavor)) {
                    resp.setNpuFlavor(ObjectUtil.isEmpty(flavor.get(0).getNpuUnitNum()) ? 0 : Integer.parseInt(flavor.get(0).getNpuUnitNum()));
                } else {
                    resp.setNpuFlavor(0);
                }
                respList.add(resp);
            });
            log.info("作业列表返回值:{}", respList);
            //共享资源池，自己的作业直接展示，其他人提交的作业聚合展示（类似，几个作业运行中，占用了多大规格）
            //根据创建时间分段展示数据，将自己的作业和其他人作业进行分段处理
            log.info("请求资源池类型:{},登录人信息:{}", req.getPoolType(), authUser);
            if (ClusterResPoolTypeEnum.SHARE.getType().equals(req.getPoolType()) && AccountTypeEnum.USER.getType()
                    .equals(authUser.getAccountType())) {
                List<List<PoolJobStatusResp>> groupList = new ArrayList<>();
                for (int i = 0; i < respList.size(); i++) {
                    //如果是第一个数据，直接新增一个数组
                    if (i == 0) {
                        //新增个list
                        List<PoolJobStatusResp> list = new ArrayList<>();
                        list.add(respList.get(i));
                        groupList.add(list);
                    } else {
                        if (respList.get(i).isSelf()) {
                            List<PoolJobStatusResp> list = new ArrayList<>();
                            list.add(respList.get(i));
                            groupList.add(list);
                        } else {
                            //判断上一个数据是否是自己的数据，如果不是，讲数据加到上一个数组中去，如果是自己的数据，新增一个数据
                            if (respList.get(i - 1).isSelf()) {
                                List<PoolJobStatusResp> list = new ArrayList<>();
                                list.add(respList.get(i));
                                groupList.add(list);
                            } else {
                                groupList.get(groupList.size() - 1).add(respList.get(i));
                            }

                        }
                    }
                }
                log.info("共享资源池,未处理作业信息:{}", groupList);
                List<PoolJobStatusResp> tempList = new ArrayList<>();
                groupList.stream().forEach(l -> {
                    if (l.get(0).isSelf()) {
                        tempList.add(l.get(0));
                    } else {
                        PoolJobStatusResp resp = new PoolJobStatusResp();
                        resp.setSelf(l.get(0).isSelf());
                        AtomicReference<Integer> npuTotal = new AtomicReference<>(0);
                        l.stream().forEach(dto -> {
                            npuTotal.updateAndGet(v -> v + dto.getNpuFlavor());
                        });
                        resp.setNpuFlavor(npuTotal.get());
                        if (req.getJobStatus().equals(TrainingJobStatusEnum.RUNNING.getType())) {
                            resp.setJobName(l.size() + "个作业运行中");
                        } else {
                            resp.setJobName(l.size() + "个作业排队中");
                        }
                        tempList.add(resp);
                    }

                });
                log.info("共享资源池，作业列表返回值:{}", tempList);
                return tempList;
            }

        }
        return respList;
    }

    @Override
    public PageResult<UserResourcePageResp> userResourcePage(UserResourcePageReq req, PageForm pageForm) {
        Long systemClusterId = getSystemClusterId();
        Page<UserEntity> userEntityPage = clusterResourcePoolMapper.selectUserPoolPage(pageForm.pageRequest(), systemClusterId,
                req.getAccount());
        List<UserEntity> records = userEntityPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<UserResourcePageResp> respList = new ArrayList<>();
            //获取集群下的所有规格
            List<ClusterFlavor> flavorList = clusterFlavorMapper.selectList(
                    new LambdaQueryWrapper<ClusterFlavor>().eq(ClusterFlavor::getClusterId, systemClusterId));
            Map<String, List<ClusterFlavor>> flavorMap = flavorList.stream().collect(Collectors.groupingBy(dto -> dto.getFlavorId()));
            //获取用户projectId
            List<ClusterUserProjectDto> projectList = clusterUserMappingMapper.getUserProjectId(systemClusterId, null);
            Map<Long, List<ClusterUserProjectDto>> projectMap = projectList.stream().collect(Collectors.groupingBy(dto -> dto.getUserSid()));
            records.stream().forEach(userEntity -> {
                UserResourcePageResp resp = new UserResourcePageResp();
                resp.setUserName(userEntity.getAccount());
                List<ClusterUserProjectDto> projectDtos = projectMap.get(userEntity.getUserSid());
                String userProjectId = null;
                if (CollectionUtil.isNotEmpty(projectDtos)) {
                    userProjectId = projectDtos.get(0).getProjectId();
                }
                String finalUserProjectId = userProjectId;
                //获取用户的共享和专属资源池
                List<ClusterResourcePool> poolList = poolService.list(
                        new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId, systemClusterId)
                                .like(ClusterResourcePool::getUserIds, userEntity.getUserSid()));
                Map<String, List<ClusterResourcePool>> poolMap = poolList.stream().collect(Collectors.groupingBy(dto -> dto.getPoolType()));
                //共享资源池npu使用情况
                List<ClusterResourcePool> sharePoolList = poolMap.get(ClusterResPoolTypeEnum.SHARE.getType());
                if (CollectionUtil.isNotEmpty(sharePoolList)) {
                    //获取共享资源池运行中作业情况
                    List<PoolJobStatusDto> shareRunningJobList = monitorMapper.getJobStatusList(systemClusterId, sharePoolList.get(0).getPoolId(),
                            TrainingJobStatusEnum.RUNNING.getType());
                    if (CollectionUtil.isNotEmpty(shareRunningJobList)) {
                        //过滤自己的数据
                        shareRunningJobList = shareRunningJobList.stream()
                                .filter(dto -> dto.getProjectId().equals(finalUserProjectId))
                                .collect(Collectors.toList());
                        AtomicReference<Integer> shareNpuTotal = new AtomicReference<>(0);
                        if (CollectionUtil.isNotEmpty(shareRunningJobList)) {
                            shareRunningJobList.stream().forEach(dto -> {
                                List<ClusterFlavor> flavor = flavorMap.get(dto.getFlavorId());
                                Integer npuNum =
                                        ObjectUtil.isEmpty(flavor.get(0).getNpuUnitNum()) ? 0 : Integer.parseInt(flavor.get(0).getNpuUnitNum());

                                shareNpuTotal.updateAndGet(v -> v + npuNum);
                            });
                            resp.setShareNpuUsed(shareNpuTotal.get());
                        }
                    }
                }
                //专属资源池npu使用情况
                List<ClusterResourcePool> drpPoolList = poolMap.get(ClusterResPoolTypeEnum.EXCLUSIVE.getType());
                if (CollectionUtil.isNotEmpty(drpPoolList)) {
                    AtomicReference<Integer> npuTotal = new AtomicReference<>(0);
                    AtomicReference<Integer> npuUsed = new AtomicReference<>(0);
                    drpPoolList.stream().forEach(dto -> {
                        MonitorMetricDto monitorMetricDto = monitorMapper.selectUnusedNpu(systemClusterId, dto.getPoolId());
                        if (ObjectUtil.isNotEmpty(monitorMetricDto)) {
                            npuTotal.updateAndGet(v -> v + monitorMetricDto.getCapacity());
                            npuUsed.updateAndGet(v -> v + monitorMetricDto.getAllocated());
                        }
                    });
                    resp.setDrpNpuUsed(npuUsed.get());
                    resp.setDrpNpuTotal(npuTotal.get());
                }
                //获取排队作业数量和运行中作业数量
                if (CollectionUtil.isNotEmpty(poolList)) {
                    AtomicReference<Long> queueJobNumTotal = new AtomicReference<>(0L);
                    AtomicReference<Long> runningJobNumTotal = new AtomicReference<>(0L);
                    poolList.stream().forEach(pool -> {
                        //获取资源池运行中作业情况
                        List<PoolJobStatusDto> runningJobList = monitorMapper.getJobStatusList(systemClusterId, pool.getPoolId(),
                                TrainingJobStatusEnum.RUNNING.getType());
                        long runningCount = runningJobList.stream().filter(dto -> dto.getProjectId().equals(finalUserProjectId)).count();
                        runningJobNumTotal.updateAndGet(v -> v + runningCount);
                        //获取资源池排队中的作业数作业情况
                        List<PoolJobStatusDto> queueJobList = monitorMapper.getJobStatusList(systemClusterId, pool.getPoolId(),
                                TrainingJobStatusEnum.QUEUING.getType());
                        long queueCount = queueJobList.stream().filter(dto -> dto.getProjectId().equals(finalUserProjectId)).count();
                        queueJobNumTotal.updateAndGet(v -> v + queueCount);

                    });
                    resp.setQueueJobNum(queueJobNumTotal.get());
                    resp.setRunningJobNum(runningJobNumTotal.get());
                }
                respList.add(resp);
            });
            Page<UserResourcePageResp> respPage = new Page<>();
            respPage.setCurrent(userEntityPage.getCurrent());
            respPage.setSize(userEntityPage.getSize());
            respPage.setRecords(respList);
            respPage.setTotal(userEntityPage.getTotal());
            respPage.setPages(userEntityPage.getPages());
            return PageResult.of(respPage);
        }
        return PageResult.of(userEntityPage, UserResourcePageResp.class);
    }

    @Override
    public List<ResourcePoolResp> getResourcePool(ExternalMonitorReq req) {
        final List<ResourcePoolResp> list = new ArrayList<>();
        AuthUser user = getUser();
        //如果是子账号
        setParentSid(user);
        Long systemClusterId = getSystemClusterId();
        LambdaQueryWrapper<ClusterResourcePool> queryWrapper = new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId,
                systemClusterId);

        if (AccountTypeEnum.USER.getType().equals(user.getAccountType())) {
            queryWrapper.like(ClusterResourcePool::getUserIds, user.getUserSid());
        }
        if (ClusterResPoolTypeEnum.SHARE.getType().equals(req.getPoolType())) {
            queryWrapper.eq(ClusterResourcePool::getPoolType, ClusterResPoolTypeEnum.SHARE.getType());
        } else if (ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(req.getPoolType())) {
            queryWrapper.eq(ClusterResourcePool::getPoolType, ClusterResPoolTypeEnum.EXCLUSIVE.getType());
        }
        List<ClusterResourcePool> poolList = poolService.list(queryWrapper);
        log.info("资源池列表:{}", poolList);
        //查询用户名
        List<UserEntity> userEntities = userEntityMapper.selectList(
                new LambdaQueryWrapper<UserEntity>().ne(UserEntity::getStatus, UserStatusEnum.DELETED));
        Map<Long, List<UserEntity>> userMap = userEntities.stream().collect(Collectors.groupingBy(UserEntity::getUserSid));
        if (CollectionUtil.isNotEmpty(poolList)) {
            list.addAll(BeanUtil.copyToList(poolList, ResourcePoolResp.class));
        }
        Map<String, String> poolAccountMap = new HashMap<>();
        poolList.stream().filter(pool -> ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(pool.getPoolType())).forEach(pool -> {
            poolAccountMap.put(pool.getPoolId(),
                    ObjectUtil.isEmpty(userMap.get(Long.parseLong(pool.getUserIds()))) ? null
                            : userMap.get(Long.parseLong(pool.getUserIds())).get(0).getAccount());
        });
        List<PoolJobStatusNumDto> poolJobStatusNumDtoList = monitorMapper.getJobStatusNum(systemClusterId, null);
        log.info("作业状态数量:{}", poolJobStatusNumDtoList);
        Map<String, List<PoolJobStatusNumDto>> groupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(poolList)) {
            groupMap = poolJobStatusNumDtoList.stream().collect(Collectors.groupingBy(dto -> dto.getPoolId() + "-" + dto.getJobStatus()));
        }
        Map<String, List<PoolJobStatusNumDto>> finalGroupMap = groupMap;
        List<ResourcePoolResp> respList = list.stream().map(resp -> {
            List<PoolJobStatusNumDto> runningList = finalGroupMap.get(resp.getPoolId() + "-" + TrainingJobStatusEnum.RUNNING.getType());
            List<PoolJobStatusNumDto> queueList = finalGroupMap.get(resp.getPoolId() + "-" + TrainingJobStatusEnum.QUEUING.getType());
            resp.setRunningNum(CollectionUtil.isEmpty(runningList) ? 0 : runningList.get(0).getNum());
            resp.setQueueNum(CollectionUtil.isEmpty(queueList) ? 0 : queueList.get(0).getNum());
            ResourceLoadResp npuDto = monitorMapper.getNpuSchedule(systemClusterId, resp.getPoolId());
            if (ObjectUtil.isNotEmpty(npuDto)) {
                resp.setUsed(npuDto.getUsed() + "%");
            } else {
                resp.setUsed("0.0%");
            }
            resp.setAccount(poolAccountMap.get(resp.getPoolId()));
            return resp;
        }).collect(Collectors.toList());
        log.info("资源池作业情况列表:{}", respList);
        return respList;
    }

    @Override
    public ClusterJobInfoResp getClusterJobInfo() {
        ClusterJobInfoResp resp = new ClusterJobInfoResp();
        ClusterEntity systemCluster = clusterEntityService.getBaseMapper().selectOne(SpecWrapperUtil.<ClusterEntity>lambda()
                .eq(ClusterEntity::getClusterFlag,
                        ClusterFlagEnum.SYSTEM.getType()));
        Long completedJobTotal = collectJobsService.getBaseMapper().selectCount(new QueryWrapper<CollectJobs>()
                .select("distinct job_id")
                .eq("cluster_id", systemCluster.getId())
                .eq("job_status",
                        TrainingJobStatusEnum.COMPLETED.getType()));
        Long submittedJobTotal = collectJobsService.getBaseMapper().selectCount(new QueryWrapper<CollectJobs>()
                .select("distinct job_id")
                .eq("cluster_id", systemCluster.getId()));
        resp.setCompletedJobTotal(completedJobTotal);
        resp.setSubmittedJobTotal(submittedJobTotal);
        return resp;
    }

    @Override
    public List<PageOperationAnalysisResp> getAnalysis(PageOperationAnalysisReq req) {
        log.info("req:{}", JSONUtil.toJsonStr(req));
        List<PageOperationAnalysisResp> list = new ArrayList<>();
        List<UserEntity> userList = userEntityMapper.selectList(Wrappers.<UserEntity>lambdaQuery()
                .in(CollUtil.isNotEmpty(req.getUserIds()), UserEntity::getUserSid, req.getUserIds())
        );
        if (CollUtil.isEmpty(userList)) {
            return list;
        }

        SystemClusterEntityResDto clusterEntity = clusterEntityService.getSystemClusterEntity();
        List<ClusterResourcePool> clusterResourcePools = clusterResourcePoolMapper.selectList(Wrappers.<ClusterResourcePool>lambdaQuery()
                .eq(ClusterResourcePool::getClusterId, clusterEntity.getId())
        );
        Set<Long> sharePoolIds = clusterResourcePools.stream()
                .filter(pool -> ClusterResPoolTypeEnum.SHARE.getType().equals(pool.getPoolType()))
                .map(ClusterResourcePool::getId).collect(Collectors.toSet());
        Set<Long> exclusivePoolIds = clusterResourcePools.stream()
                .filter(pool -> ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(pool.getPoolType()))
                .map(ClusterResourcePool::getId).collect(Collectors.toSet());
        if (clusterResourcePools.size() != sharePoolIds.size() + exclusivePoolIds.size()) {
            throw new BizException("集群资源池配置有误");
        }

        List<Long> poolIds = clusterResourcePools.stream().map(ClusterResourcePool::getId).collect(Collectors.toList());

        Map<Long, ClusterFlavor> flavorMap = clusterFlavorMapper.selectList(Wrappers.<ClusterFlavor>lambdaQuery()
                        .eq(ClusterFlavor::getFlavorType, "NPU"))
                .stream().collect(Collectors.toMap(ClusterFlavor::getId, v -> v, (k1, k2) -> k1));
        List<TrainingJobEntity> trainingJobEntityList = trainingJobEntityMapper.selectList(Wrappers.<TrainingJobEntity>lambdaQuery()
                .in(CollUtil.isNotEmpty(req.getUserIds()), TrainingJobEntity::getUserSid, req.getUserIds())
                .in(CollUtil.isNotEmpty(poolIds), TrainingJobEntity::getPoolId, poolIds.stream().map(String::valueOf).collect(Collectors.toList()))
        );
        List<NotebookEntity> notebookEntityList = notebookEntityMapper.selectList(Wrappers.<NotebookEntity>lambdaQuery()
                .in(CollUtil.isNotEmpty(req.getUserIds()), NotebookEntity::getUserSid, req.getUserIds())
                .in(CollUtil.isNotEmpty(clusterResourcePools), NotebookEntity::getPoolId, poolIds)
        );
        Date date = new Date();
        DateTime beginOfDay = DateUtil.beginOfDay(date);
        DateTime beginOfWeek = DateUtil.beginOfWeek(date);
        DateTime beginOfMonth = DateUtil.beginOfMonth(date);
        List<String> runStatus = Arrays.asList(
                TrainingJobStatusEnum.RUNNING.getType(),
                TrainingJobStatusEnum.STOPPING.getType()
        );
        List<String> queueList = Collections.singletonList(TrainingJobStatusEnum.QUEUING.getType());
        List<String> failedStatus = Arrays.asList(
                TrainingJobStatusEnum.SCHEDULING_FAILED.getType(),
                TrainingJobStatusEnum.FAILED.getType()
        );
        for (UserEntity pool : userList) {

            List<TrainingJobEntity> runJob = getJobByUserIdAndStatus(pool.getUserSid(), runStatus, trainingJobEntityList);
            Integer npuJobShare = getAllocation(runJob.stream()
                    .filter(r -> sharePoolIds.contains(Long.valueOf(r.getPoolId())))
                    .map(r -> Long.valueOf(r.getSpec()))
                    .collect(Collectors.toList()), flavorMap, "npu");
            Integer npuJobExclusive = getAllocation(runJob.stream()
                    .filter(r -> exclusivePoolIds.contains(Long.valueOf(r.getPoolId())))
                    .map(r -> Long.valueOf(r.getSpec()))
                    .collect(Collectors.toList()), flavorMap, "npu");

            List<NotebookEntity> runNotebook = getNotebookByUserIdAndStatus(pool.getUserSid(), runStatus, notebookEntityList);
            Integer npuNotebookShare = getAllocation(runNotebook.stream()
                    .filter(r -> sharePoolIds.contains(r.getPoolId()))
                    .map(NotebookEntity::getFlavorId)
                    .collect(Collectors.toList()), flavorMap, "npu");
            Integer npuNotebookExclusive = getAllocation(runNotebook.stream()
                    .filter(r -> exclusivePoolIds.contains(r.getPoolId()))
                    .map(NotebookEntity::getFlavorId)
                    .collect(Collectors.toList()), flavorMap, "npu");
            PageOperationAnalysisResp resp = PageOperationAnalysisResp.builder()
                    .ownerId(pool.getUserSid())
                    .privateNpuCapacity(getUserPoolCapacity(clusterResourcePools, pool.getUserSid(), flavorMap))
                    .privateNpuAllocation(npuJobExclusive + npuNotebookExclusive)
                    .publicNpuAllocation(npuJobShare + npuNotebookShare)
                    .totalJob(runJob.size() + runNotebook.size())
                    .runningTrainingJob(runJob.size())
                    .runningNotebookJob(runNotebook.size())
                    .pendingTrainingJob(
                            getJobByUserIdAndStatus(pool.getUserSid(), queueList, trainingJobEntityList).size()
                                    + getNotebookByUserIdAndStatus(pool.getUserSid(), queueList, notebookEntityList).size())
                    .dayCreatedTrainingJob(
                            getJobByUserIdAndStatus(pool.getUserSid(), null, trainingJobEntityList, beginOfDay).size()
                                    + getNotebookByUserIdAndStatus(pool.getUserSid(), null, notebookEntityList, beginOfDay).size())
                    .dayFailedTrainingJob(getJobByUserIdAndStatus(pool.getUserSid(), failedStatus, trainingJobEntityList, beginOfDay).size()
                            + getNotebookByUserIdAndStatus(pool.getUserSid(), failedStatus, notebookEntityList, beginOfDay).size())
                    .weekCreatedTrainingJob(
                            getJobByUserIdAndStatus(pool.getUserSid(), null, trainingJobEntityList, beginOfWeek).size()
                                    + getNotebookByUserIdAndStatus(pool.getUserSid(), null, notebookEntityList, beginOfWeek).size())
                    .weekFailedTrainingJob(getJobByUserIdAndStatus(pool.getUserSid(), failedStatus, trainingJobEntityList, beginOfWeek).size()
                            + getNotebookByUserIdAndStatus(pool.getUserSid(), failedStatus, notebookEntityList, beginOfWeek).size())
                    .monthCreatedTrainingJob(
                            getJobByUserIdAndStatus(pool.getUserSid(), null, trainingJobEntityList, beginOfMonth).size()
                                    + getNotebookByUserIdAndStatus(pool.getUserSid(), null, notebookEntityList, beginOfMonth).size())
                    .monthFailedTrainingJob(
                            getJobByUserIdAndStatus(pool.getUserSid(), failedStatus, trainingJobEntityList, beginOfMonth).size()
                                    + getNotebookByUserIdAndStatus(pool.getUserSid(), failedStatus, notebookEntityList, beginOfMonth).size())
                    .build();
            list.add(resp);
        }
        return list;
    }

    @Override
    public List<TheLineChartResp> getTheLineChart(TheLineChartReq req) {
        log.info("req:{}", JSONUtil.toJsonStr(req));
        if (Objects.isNull(req.getOwnerId())) {
            return Collections.emptyList();
        }
        List<TheLineChartResp> list = new LinkedList<>();
        SystemClusterEntityResDto clusterEntity = clusterEntityService.getSystemClusterEntity();
        List<ClusterResourcePool> clusterResourcePools = clusterResourcePoolMapper.selectList(Wrappers.<ClusterResourcePool>lambdaQuery()
                .eq(ClusterResourcePool::getClusterId, clusterEntity.getId())
        );
        Set<Long> sharePoolIds = clusterResourcePools.stream()
                .filter(pool -> ClusterResPoolTypeEnum.SHARE.getType().equals(pool.getPoolType()))
                .map(ClusterResourcePool::getId).collect(Collectors.toSet());
        Set<Long> exclusivePoolIds = clusterResourcePools.stream()
                .filter(pool -> ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(pool.getPoolType()))
                .map(ClusterResourcePool::getId).collect(Collectors.toSet());
        if (clusterResourcePools.size() != sharePoolIds.size() + exclusivePoolIds.size()) {
            throw new BizException("集群资源池配置有误");
        }

        List<Long> poolIds = clusterResourcePools.stream().map(ClusterResourcePool::getId).collect(Collectors.toList());

        Map<Long, ClusterFlavor> flavorMap = clusterFlavorMapper.selectList(Wrappers.<ClusterFlavor>lambdaQuery()
                        .eq(ClusterFlavor::getFlavorType, "NPU"))
                .stream().collect(Collectors.toMap(ClusterFlavor::getId, v -> v, (k1, k2) -> k1));
        List<TrainingJobEntity> trainingJobEntityList = trainingJobEntityMapper.selectList(Wrappers.<TrainingJobEntity>lambdaQuery()
                .eq(TrainingJobEntity::getUserSid, req.getOwnerId())
                .in(CollUtil.isNotEmpty(poolIds), TrainingJobEntity::getPoolId, poolIds.stream().map(String::valueOf).collect(Collectors.toList()))
                .between(TrainingJobEntity::getJobStartTime, req.getStartTime(), req.getEndTime())
                .orderByDesc(TrainingJobEntity::getCreatedDt)
        );
        List<NotebookEntity> notebookEntityList = notebookEntityMapper.selectList(Wrappers.<NotebookEntity>lambdaQuery()
                .in(NotebookEntity::getUserSid, req.getOwnerId())
                .in(CollUtil.isNotEmpty(clusterResourcePools), NotebookEntity::getPoolId, poolIds)
                .between(NotebookEntity::getStartTime, req.getStartTime(), req.getEndTime())
                .orderByDesc(NotebookEntity::getCreatedDt)
        );
        Map<String, TheLineChartResp> map = new HashMap<>();
        for (TrainingJobEntity job : trainingJobEntityList) {
            Integer jobAllocation = getJobAllocation(flavorMap, job.getJobStartTime(), job.getSpec());
            String format = DateUtil.format(job.getJobStartTime(), "yyyy-MM-dd HH");
            String key = format + ":00";
            TheLineChartResp resp = MapUtil.get(map, key, TheLineChartResp.class, new TheLineChartResp());
            resp.setCreatedDate(job.getJobStartTime());
            if (NumberUtil.isLong(job.getPoolId()) && sharePoolIds.contains(Long.valueOf(job.getPoolId()))) {
                resp.setPublicNpuAllocation(resp.getPublicNpuAllocation() + jobAllocation);
            } else {
                resp.setPrivateNpuAllocation(resp.getPrivateNpuAllocation() + jobAllocation);
            }
            map.put(key, resp);
        }

        for (NotebookEntity job : notebookEntityList) {
            Integer jobAllocation = getJobAllocation(flavorMap, job.getStartTime(), String.valueOf(job.getFlavorId()));
            String format = DateUtil.format(job.getStartTime(), "yyyy-MM-dd HH");
            String key = format + ":00";
            TheLineChartResp resp = MapUtil.get(map, key, TheLineChartResp.class, new TheLineChartResp());
            resp.setCreatedDate(job.getStartTime());
            if (Objects.nonNull(job.getPoolId()) && sharePoolIds.contains(job.getPoolId())) {
                resp.setPublicNpuAllocation(resp.getPublicNpuAllocation() + jobAllocation);
            } else {
                resp.setPrivateNpuAllocation(resp.getPrivateNpuAllocation() + jobAllocation);
            }
            map.put(key, resp);
        }
        for (Map.Entry<String, TheLineChartResp> entry : map.entrySet()) {
            list.add(entry.getValue());
        }
        list.sort(Comparator.comparing(TheLineChartResp::getCreatedDate));
        return list;
    }

    @Override
    public List<TheBarChartResp> getTheBarChart(TheBarChartReq req) {
        log.info("req:{}", JSONUtil.toJsonStr(req));
        List<TheBarChartResp> list = new LinkedList<>();
        SystemClusterEntityResDto clusterEntity = clusterEntityService.getSystemClusterEntity();
        List<ClusterResourcePool> clusterResourcePools = clusterResourcePoolMapper.selectList(Wrappers.<ClusterResourcePool>lambdaQuery()
                .eq(ClusterResourcePool::getClusterId, clusterEntity.getId())
        );
        Set<Long> sharePoolIds = clusterResourcePools.stream()
                .filter(pool -> ClusterResPoolTypeEnum.SHARE.getType().equals(pool.getPoolType()))
                .map(ClusterResourcePool::getId).collect(Collectors.toSet());
        Set<Long> exclusivePoolIds = clusterResourcePools.stream()
                .filter(pool -> ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(pool.getPoolType()))
                .map(ClusterResourcePool::getId).collect(Collectors.toSet());
        if (clusterResourcePools.size() != sharePoolIds.size() + exclusivePoolIds.size()) {
            throw new BizException("集群资源池配置有误");
        }

        List<Long> poolIds = clusterResourcePools.stream().map(ClusterResourcePool::getId).collect(Collectors.toList());
        List<TrainingJobEntity> trainingJobEntityList = trainingJobEntityMapper.selectList(Wrappers.<TrainingJobEntity>lambdaQuery()
                .eq(TrainingJobEntity::getUserSid, req.getOwnerId())
                .in(CollUtil.isNotEmpty(poolIds), TrainingJobEntity::getPoolId, poolIds.stream().map(String::valueOf).collect(Collectors.toList()))
                .between(TrainingJobEntity::getCreatedDt, req.getSearchStartTime(), req.getSearchEndTime())
                .orderByDesc(TrainingJobEntity::getCreatedDt)
        );
        List<NotebookEntity> notebookEntityList = notebookEntityMapper.selectList(Wrappers.<NotebookEntity>lambdaQuery()
                .in(NotebookEntity::getUserSid, req.getOwnerId())
                .in(CollUtil.isNotEmpty(clusterResourcePools), NotebookEntity::getPoolId, poolIds)
                .between(NotebookEntity::getCreatedDt, req.getSearchStartTime(), req.getSearchEndTime())
                .orderByDesc(NotebookEntity::getCreatedDt)
        );
        boolean flag = Objects.nonNull(req.getType()) && req.getType() == 3;
        Map<String, TheBarChartResp> map = new HashMap<>();
        for (TrainingJobEntity job : trainingJobEntityList) {
            String key = getTheBarChartKey(job.getCreatedDt(), req.getType());
            TheBarChartResp resp = MapUtil.get(map, key, TheBarChartResp.class, new TheBarChartResp());
            if (TrainingJobStatusEnum.FAILED.getType().equalsIgnoreCase(job.getStatus())) {
                resp.setFailedJob(resp.getFailedJob() + 1);
            } else {
                resp.setCreatedJob(resp.getCreatedJob() + 1);
            }
            if (Objects.isNull(resp.getSearchStartTime()) || Objects.isNull(resp.getSearchEndTime())) {
                resp.setSearchStartTime(DateUtil.parseDateTime(key + (flag ? "-01" : "") + " 00:00:00"));
                resp.setSearchEndTime(DateUtil.parseDateTime(key + (flag ? "-01" : "") + " 00:00:00"));
                if (flag) {
                    resp.setSearchEndTime(DateUtil.offsetMonth(resp.getSearchStartTime(), 1));
                } else {
                    resp.setSearchEndTime(DateUtil.offsetDay(resp.getSearchStartTime(), 1));
                }
            }
            map.put(key, resp);
        }
        for (NotebookEntity job : notebookEntityList) {
            String key = getTheBarChartKey(job.getCreatedDt(), req.getType());
            TheBarChartResp resp = MapUtil.get(map, key, TheBarChartResp.class, new TheBarChartResp());
            if (TrainingJobStatusEnum.FAILED.getType().equalsIgnoreCase(job.getStatus())) {
                resp.setFailedJob(resp.getFailedJob() + 1);
            } else {
                resp.setCreatedJob(resp.getCreatedJob() + 1);
            }
            if (Objects.isNull(resp.getSearchStartTime()) || Objects.isNull(resp.getSearchEndTime())) {
                resp.setSearchStartTime(DateUtil.parseDateTime(key + (flag ? "-01" : "") + " 00:00:00"));
                resp.setSearchEndTime(DateUtil.parseDateTime(key + (flag ? "-01" : "") + " 00:00:00"));
                if (flag) {
                    resp.setSearchEndTime(DateUtil.offsetMonth(resp.getSearchStartTime(), 1));
                } else {
                    resp.setSearchEndTime(DateUtil.offsetDay(resp.getSearchStartTime(), 1));
                }
            }
            map.put(key, resp);
        }
        for (Map.Entry<String, TheBarChartResp> entry : map.entrySet()) {
            list.add(entry.getValue());
        }
        list.sort(Comparator.comparing(TheBarChartResp::getSearchStartTime));
        return list;
    }

    private String getTheBarChartKey(Date time, Integer type) {
        //3为按月分组
        boolean flag = Objects.nonNull(type) && type == 3;
        return DateUtil.format(time, "yyyy-MM" + (!flag ? "-dd" : ""));
    }

    private Integer getJobAllocation(Map<Long, ClusterFlavor> flavorMap, Date startTime, String flavorId) {
        int num = 0;
        if (Objects.isNull(startTime)) {
            return num;
        }
        if (!NumberUtil.isLong(flavorId)) {
            return num;
        }
        ClusterFlavor clusterFlavor = flavorMap.get(Long.parseLong(flavorId));
        if (Objects.isNull(clusterFlavor)) {
            return num;
        }
        return NumberUtil.isInteger(clusterFlavor.getNpuUnitNum()) ? Integer.parseInt(clusterFlavor.getNpuUnitNum()) : 0;
    }

    private Integer getUserPoolCapacity(List<ClusterResourcePool> clusterResourcePools, Long userId, Map<Long, ClusterFlavor> flavorMap) {
        int value = 0;
        if (CollUtil.isEmpty(clusterResourcePools)) {
            return value;
        }
        for (ClusterResourcePool pool : clusterResourcePools) {
            if (ClusterResPoolTypeEnum.EXCLUSIVE.getType().equals(pool.getPoolType())
                    && StrUtil.isNotBlank(pool.getUserIds())
                    && pool.getUserIds().contains(String.valueOf(userId))) {
                ClusterFlavor clusterFlavor = MapUtil.get(flavorMap, pool.getFlavorId(), ClusterFlavor.class);
                if (Objects.nonNull(clusterFlavor) && NumberUtil.isInteger(clusterFlavor.getNpuUnitNum())) {
                    value += Integer.parseInt(clusterFlavor.getNpuUnitNum());
                }
            }
        }
        return value;
    }

    private Integer getAllocation(List<Long> flavorIds, Map<Long, ClusterFlavor> flavorMap, String type) {
        int num = 0;
        for (Long key : flavorMap.keySet()) {
            if (flavorIds.contains(key)) {
                ClusterFlavor clusterFlavor = flavorMap.get(key);
                String x = StrUtil.EMPTY;
                if ("npu".equalsIgnoreCase(type)) {
                    x = clusterFlavor.getNpuUnitNum();
                }
                if ("gpu".equalsIgnoreCase(type)) {
                    x = clusterFlavor.getGpuUnitNum();
                }
                num += NumberUtil.isInteger(x) ? Integer.parseInt(x) : 0;
            }
        }
        return num;
    }

    private List<TrainingJobEntity> getJobByUserIdAndStatus(Long userId, List<String> status, List<TrainingJobEntity> trainingJobEntityList) {
        return getJobByUserIdAndStatus(userId, status, trainingJobEntityList, null);
    }

    private List<TrainingJobEntity> getJobByUserIdAndStatus(Long userId, List<String> status,
                                                            List<TrainingJobEntity> trainingJobEntityList,
                                                            DateTime dateTime) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        return trainingJobEntityList.stream()
                .filter(job -> job.getUserSid().equals(userId)
                        && (CollUtil.isEmpty(status) || status.contains(job.getStatus()))
                        && (Objects.isNull(dateTime) || job.getCreatedDt().after(dateTime))
                ).collect(Collectors.toList());
    }

    private List<NotebookEntity> getNotebookByUserIdAndStatus(Long userId, List<String> status,
                                                              List<NotebookEntity> notebookEntityList) {
        return getNotebookByUserIdAndStatus(userId, status, notebookEntityList, null);
    }

    private List<NotebookEntity> getNotebookByUserIdAndStatus(Long userId, List<String> status,
                                                              List<NotebookEntity> notebookEntityList,
                                                              DateTime dateTime) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        return notebookEntityList.stream()
                .filter(job -> job.getUserSid().equals(userId)
                        && (CollUtil.isEmpty(status) || status.contains(job.getStatus()))
                        && (Objects.isNull(dateTime) || job.getCreatedDt().after(dateTime))
                ).collect(Collectors.toList());
    }

    /**
     * 获取用户信息
     */
    private AuthUser getUser() {
        //获得登录用户
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        return authUser;
    }

    /**
     * 获取系统集群id
     *
     * @return id
     */
    private Long getSystemClusterId() {
        ClusterEntity clusterEntity = clusterEntityService.getOne(
                new LambdaQueryWrapper<ClusterEntity>().eq(ClusterEntity::getClusterFlag, ClusterFlagEnum.SYSTEM.getType()));
        log.info("集群信息:{}", clusterEntity);
        if (ObjectUtil.isEmpty(clusterEntity)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        return clusterEntity.getId();
    }

    /**
     * 获取用户共享资源池id
     *
     * @param authUser        用户
     * @param systemClusterId 系统集群id
     */
    private String getSharePoolId(AuthUser authUser, Long systemClusterId) {
        LambdaQueryWrapper<ClusterResourcePool> query = new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId,
                        systemClusterId)
                .eq(ClusterResourcePool::getPoolType,
                        ClusterResPoolTypeEnum.SHARE.getType());
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            query.like(ClusterResourcePool::getUserIds, authUser.getUserSid());
        }
        ClusterResourcePool pool = poolService.getOne(query);
        log.info("共享资源池信息:{}", pool);
        if (ObjectUtil.isEmpty(pool)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        return pool.getPoolId();
    }

    /**
     * 获取用户专属资源池id
     *
     * @param authUser        用户
     * @param systemClusterId 系统集群id
     */
    private String getDrpPoolId(AuthUser authUser, Long systemClusterId, String poolName) {
        LambdaQueryWrapper<ClusterResourcePool> query = new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId,
                        systemClusterId)
                .eq(ClusterResourcePool::getPoolType,
                        ClusterResPoolTypeEnum.EXCLUSIVE.getType())
                .like(ClusterResourcePool::getPoolName,
                        poolName);
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            query.like(ClusterResourcePool::getUserIds, authUser.getUserSid());
        }
        ClusterResourcePool pool = poolService.getOne(query);
        log.info("专属资源池信息:{}", pool);
        if (ObjectUtil.isEmpty(pool)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        return pool.getPoolId();
    }


    /**
     * 获取资源池信息
     *
     * @param authUser        用户
     * @param systemClusterId 系统集群id
     * @param poolId          资源池id
     */
    private ClusterResourcePool getPool(AuthUser authUser, Long systemClusterId, String poolId) {
        LambdaQueryWrapper<ClusterResourcePool> queryWrapper = new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId,
                        systemClusterId)
                .eq(ClusterResourcePool::getPoolId,
                        poolId);
        if (AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            queryWrapper.like(ClusterResourcePool::getUserIds, authUser.getUserSid());
        }
        ClusterResourcePool pool = poolService.getOne(queryWrapper);
        log.info("资源池信息:{}", pool);
        if (ObjectUtil.isEmpty(pool)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        return pool;
    }

    /**
     * 设置主账号id
     *
     * @param authUser 登录用户
     */
    private void setParentSid(AuthUser authUser) {
        if (UserTypeEnum.SUB_ACCOUNT.getType().equals(authUser.getUserType())
                && AccountTypeEnum.USER.getType().equals(authUser.getAccountType())) {
            //获取主账号的userid，并设置进来
            UserEntity userEntity = userEntityMapper.selectById(authUser.getUserSid());
            authUser.setUserSid(userEntity.getParentSid());
        }
    }

}
