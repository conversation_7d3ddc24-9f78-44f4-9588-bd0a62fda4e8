package com.cloudstar.service.impl.notice;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.MsgCd;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.Criteria;
import com.cloudstar.common.base.pojo.SensitiveWordDetector;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.SysMConfigMapper;
import com.cloudstar.dao.mapper.notice.SysMNoticeMapper;
import com.cloudstar.dao.model.SysMConfig;
import com.cloudstar.dao.model.notice.SysMNotice;
import com.cloudstar.enums.NoticeStatusEnum;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.service.facade.notice.SysMNoticeService;
import com.cloudstar.service.pojo.vo.requestvo.notice.NoticePageListRequest;
import com.cloudstar.service.pojo.vo.requestvo.notice.NoticeUpdateRequest;
import com.cloudstar.service.pojo.vo.responsevo.notice.NoticeDetailResponse;
import com.cloudstar.service.pojo.vo.responsevo.notice.NoticeListResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 公告管理
 *
 * <AUTHOR>
 * @description 针对表【sys_m_notice(公告表)】的数据库操作Service实现
 * @createDate 2022-11-03 15:36:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SysMNoticeServiceImpl extends ServiceImpl<SysMNoticeMapper, SysMNotice>
        implements SysMNoticeService {

    SysMNoticeMapper sysMNoticeMapper;

    SysMConfigMapper sysMConfigMapper;

    @Override
    public PageResult<NoticeListResponse> page(NoticePageListRequest noticePageRequest) {
        //若前端不传排序参数，则默认以publishDt降序展示
        if (ObjectUtil.isEmpty(noticePageRequest.getSortDataField()) && ObjectUtil.isEmpty(
                noticePageRequest.getAsc())) {
            noticePageRequest.setSortDataField("publishDt");
            noticePageRequest.setAsc(false);
        }
        //分页查询公告列表
        Page<SysMNotice> sysMNoticePage = sysMNoticeMapper.selectPage(noticePageRequest.pageRequest(),
                                                                      SpecWrapperUtil.filter(noticePageRequest));

        sysMNoticePage.getRecords().forEach(resp -> {
            resp.setStatusName(NoticeStatusEnum.getByCode(resp.getNoticeStatus()).getMessage());
        });
        return PageResult.of(sysMNoticePage, NoticeListResponse.class);
    }

    @Override
    public NoticeDetailResponse getNoticeDetail(String id) {
        SysMNotice sysMNotice = sysMNoticeMapper.selectById(id);
        NoticeDetailResponse noticeDetailResponse = BeanUtil.copyProperties(sysMNotice, NoticeDetailResponse.class);
        return noticeDetailResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rest<String> saveNotice(NoticeUpdateRequest noticeUpdateRequest) {
        log.info("NoticeController.save:入参-{}", noticeUpdateRequest);
        String noticeContent = noticeUpdateRequest.getNoticeContent();
        if (4194304 < noticeContent.getBytes().length) {
            return Rest.e(BizErrorEnum.MSG_1062_NOTICE_TOO_LARGE.getDescribe());
        }
        noticeUpdateRequest.setNoticeContent(noticeContent);
        Long noticeId = noticeUpdateRequest.getNoticeId();
        SysMNotice sysMNotice;
        //若noticeId为null，则是创建，不为null，则为编辑
        if (noticeId == null) {
            sysMNotice = new SysMNotice();
        } else {
            sysMNotice = sysMNoticeMapper.selectById(noticeId);
        }
        if (ObjectUtil.isNull(sysMNotice)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND.getDescribe());
        } else {
            BeanUtils.copyProperties(noticeUpdateRequest, sysMNotice);
        }

        //检测公告中的敏感词
        Set<String> checkSensitiveWord = checkSensitiveWord(sysMNotice);
        if (CollectionUtils.isNotEmpty(checkSensitiveWord)) {
            log.info("公告中敏感词：" + checkSensitiveWord);
            throw new BizException("公告标题或内容中存在敏感词：" + checkSensitiveWord + "，请修改。");
        }

        Date date = new Date();
        if (StringUtils.isEmpty(noticeUpdateRequest.getPublishDt())) {
            sysMNotice.setPublishDt(date);
        } else {
            sysMNotice.setPublishDt(DateUtil.parseDateTime(noticeUpdateRequest.getPublishDt()));
        }
        String account = ThreadAuthUserHolder.getAuthUser().getAccount();
        sysMNotice.setUpdateDt(date);
        sysMNotice.setUpdateBy(account);

        if (noticeId == null) {
            sysMNotice.setCreateDt(date);
            sysMNotice.setCreateBy(account);
            sysMNotice.setNoticeStatus("0");
            sysMNoticeMapper.insert(sysMNotice);
            return Rest.ok(WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
        } else {
            sysMNoticeMapper.updateById(sysMNotice);
            return Rest.ok(WebUtil.getMessage(MsgCd.INFO_EDIT_SUCCESS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(String noticeId) {
        SysMNotice sysMNotice = sysMNoticeMapper.selectById(noticeId);
        if (NoticeStatusEnum.TODOPUBLISH.getCode().equals(sysMNotice.getNoticeStatus())) {
            sysMNoticeMapper.deleteById(noticeId);
        } else {
            throw new BizException("已发布的公告不能删除！");
        }

    }

    @Override
    public Set<String> checkSensitiveWord(SysMNotice sysMNotice) {
        Criteria criteria = new Criteria();
        criteria.put("configType", "sensitive_word_config");
        List<SysMConfig> configs = sysMConfigMapper.displaySystemConfigList(criteria);
        ArrayList<String> sensitiveWordList = new ArrayList<>();
        configs.forEach(sysConfig -> {
            List<String> strings = Arrays.stream(sysConfig.getConfigValue().replace("；", ";").split(";")).collect(
                    Collectors.toList());
            sensitiveWordList.addAll(strings);
        });

        SensitiveWordDetector sensitiveWordDetector = new SensitiveWordDetector(sensitiveWordList);

        //校验公告标题
        Set<String> noticeTitle = sensitiveWordDetector.getSensitiveWord(sysMNotice.getNoticeTitle(), 2);

        //校验公告内容
        Set<String> noticeContent = sensitiveWordDetector.getSensitiveWord(sysMNotice.getNoticeContent(), 2);

        noticeContent.addAll(noticeTitle);

        return noticeContent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(String noticeId, String status) {
        if (noticeId == null) {
            throw new BizException(BizErrorEnum.MSG_1064_NOTICE_ID_NOT_NULL.getDescribe());
        }
        if (StringUtils.isEmpty(status)) {
            throw new BizException(BizErrorEnum.MSG_1065_STATUS_NOT_NULL.getDescribe());
        } else if (NoticeStatusEnum.getByCode(status) == null) {
            throw new BizException(BizErrorEnum.MSG_1063_STATUS_NOT_EXIST.getDescribe());
        }

        //发布公告需要校验敏感词
        if (NoticeStatusEnum.PUBLISH.getCode().equals(status)) {
            SysMNotice sysMNotice = sysMNoticeMapper.selectById(noticeId);
            Set<String> checkSensitiveWord = checkSensitiveWord(sysMNotice);
            if (CollectionUtils.isNotEmpty(checkSensitiveWord)) {
                log.info("公告中敏感词：" + checkSensitiveWord);
                throw new BizException("公告标题或内容中存在敏感词：" + checkSensitiveWord + "，请修改。");
            }
        }

        SysMNotice sysMNotice = new SysMNotice();
        sysMNotice.setId(Long.valueOf(noticeId));
        sysMNotice.setNoticeStatus(status);
        sysMNotice.setUpdateDt(new Date());
        sysMNotice.setUpdateBy(ThreadAuthUserHolder.getAuthUser().getAccount());
        sysMNoticeMapper.updateById(sysMNotice);
    }
}




