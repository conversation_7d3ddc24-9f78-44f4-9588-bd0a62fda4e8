package com.cloudstar.service.impl.subuser;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.subuser.SysMPolicyMapper;
import com.cloudstar.dao.model.subuser.SysMPolicy;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.subuser.SysMPolicyService;
import com.cloudstar.service.pojo.vo.requestvo.subuser.CreatePolicyReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.PolicyInfoReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.UpdatePolicyReq;
import com.cloudstar.service.pojo.vo.responsevo.subuser.PolicyDetailRes;
import com.cloudstar.service.pojo.vo.responsevo.subuser.PolicyInfoRes;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * sys_m_policy服务impl
 *
 * <AUTHOR>
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SysMPolicyServiceImpl extends ServiceImpl<SysMPolicyMapper, SysMPolicy>
        implements SysMPolicyService {

    SysMPolicyMapper sysMPolicyMapper;

    /**
     * 获取权限策略列表
     */
    @Override
    public PageResult<PolicyInfoRes> selectPolicyPage(PolicyInfoReq req) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Page<SysMPolicy> policies = sysMPolicyMapper.selectPage(req.pageRequest(),
                                                                SpecWrapperUtil.<SysMPolicy>filter(req)
                                                                               .lambda()
                                                                               .eq(SysMPolicy::getOrgSid, authUser.getOrgSid())
                                                                               .or()
                                                                               .eq(SysMPolicy::getPolicyType, "system"));
        return PageResult.of(policies, PolicyInfoRes.class);
    }


    /**
     * 获取权限策略列表
     */
    @Override
    public List<PolicyInfoRes> selectPolicies(PolicyInfoReq req) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        List<SysMPolicy> policies = sysMPolicyMapper.selectList(SpecWrapperUtil.<SysMPolicy>filter(req)
                                                                               .lambda()
                                                                               .eq(SysMPolicy::getOrgSid, authUser.getOrgSid())
                                                                               .or()
                                                                               .eq(SysMPolicy::getPolicyType, "system"));
        return BeanUtil.copyToList(policies, PolicyInfoRes.class);
    }


    /**
     * 获取权限策略详情
     */
    @Override
    public PolicyDetailRes selectPolicyById(Long policyId) {
        return null;
    }


    /**
     * 创建权限策略
     */
    @Override
    public Boolean createPolicy(CreatePolicyReq req) {
        return null;
    }


    /**
     * 更新权限策略
     */
    @Override
    public Boolean updatePolicy(UpdatePolicyReq req) {
        return null;
    }


    /**
     * 删除权限策略
     */
    @Override
    public void deletePolicy(String policyIds) {

    }
}




