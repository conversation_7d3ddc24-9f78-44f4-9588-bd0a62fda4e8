package com.cloudstar.service.impl.user;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.service.facade.user.UserEntityService;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 用户service
* <AUTHOR>
* @description 针对表【user_entity(租户及租户子用户表)】的数据库操作Service实现
* @createDate 2022-10-18 15:03:26
*/
@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class UserEntityServiceImpl extends ServiceImpl<UserEntityMapper, UserEntity> implements UserEntityService {

    UserEntityMapper userEntityMapper;

    @Override
    public List<UserEntity> selectExpiredUser() {
        List<UserEntity> userEntityList = userEntityMapper.selectExpiredUser();
        return  userEntityList;
    }

    @Override
    public List<UserEntity> selectUserByThresholdValue(long thresholdValue) {
        return userEntityMapper.selectUserByThresholdValue(thresholdValue);
    }
}




