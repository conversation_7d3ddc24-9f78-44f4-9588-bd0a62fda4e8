package com.cloudstar.service.pojo.vo.requestvo.access;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 编辑断言请求
 *
 * <AUTHOR>
 * Created on 2022/8/23
 * @date 2022/08/23
 */
@Data
public class UpdAssertionReq {
    /**
     * 断言SID
     */
    @NotNull
    private Long assertionSid;

    /**
     * 断言类型;view[可见权],mgt[管理权],del[删除权]
     */
    @NotBlank
    private String assertionType;

    /**
     * 操作名称;如：云主机创建，云主机删除等
     */
    @NotBlank
    private String opName;

    /**
     * 操作代码;ListUser，createVm等接口操作
     */
    @NotBlank
    @Pattern(regexp = "^[a-zA-Z_]+$", message = "操作代码有误")
    private String opCode;

    /**
     * 产品代码;ecs,ebs,ticket等资源
     */
    private String serviceCode;
    /**
     * 资源代码;子资源代码
     */
    private String resourceCode;
    /**
     * 限制资源;[*]暂时只支持全部资源
     */
    private String resource;

    /**
     * 控制效果;allow[允许]deny[拒绝]
     */
    private String effect;
}
