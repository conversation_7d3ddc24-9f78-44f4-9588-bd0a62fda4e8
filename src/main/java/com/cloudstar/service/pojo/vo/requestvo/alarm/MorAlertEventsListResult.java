package com.cloudstar.service.pojo.vo.requestvo.alarm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
@EqualsAndHashCode
public class MorAlertEventsListResult implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 是否恢复 0 未恢复，1 已恢复
     */
    private Integer isRecovered;

    private String statusStr;

    /**
     * 告警hash
     */
    private String hash;

    /**
     * 告警规则ID
     */
    private Long ruleId;

    /**
     * 告警规则名称；告警标题
     */
    private String ruleName;

    /**
     * 告警规则描述；告警内容
     */
    private String ruleDescription;

    /**
     * 标签；告警对象: instance=cm3r0r87oy9570958riktv2m6
     */
    private String tags;

    /**
     * 告警级别 1: 紧急 Emergency 2: 重要 significance 提示 3: pointOut 次要 4: minor
     */
    private Integer severity;

    private String severityStr;

    /**
     * 恢复时间
     */
    private Long recoverTime;

    /**
     * 首次告警时间
     */
    private Long firstTriggerTime;

    /**
     * 最后告警时间
     */
    private Long triggerTime;

    /**
     * 恢复时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recoverTimeDate;

    /**
     * 首次告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstTriggerTimeDate;

    /**
     * 最后告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date triggerTimeDate;

    /**
     * 处理状态: to_be_confirmed 待确认, processing 处理中, resolve 已解决' AFTER `first_trigger_time`
     */
    private String processingStatus;

    private String target;

    private String durationStr;

    private Long duration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date resolveTime;

    private String confirmContent;

    private String resolveContent;

}
