package com.cloudstar.service.pojo.vo.requestvo.manager;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateManagerEmailReq {

    /**
     * 新手机号
     */
    @NotBlank(message = "邮箱不能为空") String newEmail;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空") String validCode;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空") Long userSid;

}
