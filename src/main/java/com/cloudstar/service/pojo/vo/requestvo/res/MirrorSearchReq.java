package com.cloudstar.service.pojo.vo.requestvo.res;

import com.cloudstar.common.util.page.PageForm;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 镜像搜索参数
 *
 * <AUTHOR>
 * @date 2024/07/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MirrorSearchReq extends PageForm {
    /**
     * 镜像名称
     */
    private String imageName;

    /**
     * 镜像标签
     */
    private String tag;

    /**
     * 所属仓库
     */
    private String repository;

    /**
     * 版本数
     */
    private Integer versionCount;

    /**
     * 已使用空间
     */
    private String usedSpace;

    /**
     * 更新时间
     */
    private String updateTime;
}
