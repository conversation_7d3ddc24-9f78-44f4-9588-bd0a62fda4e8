package com.cloudstar.service.pojo.vo.requestvo.schedule;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 权重req
 *
 * <AUTHOR>
 * @date 2022/9/14 9:33
 */
@Data
public class SchedulePolicyWeightReq {

    /**
     * 集群id
     */
    @NotNull
    private Long clusterId;

    /**
     * 系数 系数越低 性能代表越好
     */
    @NotNull
    @Min(value = 0)
    @Max(value = 100)
    private Integer weight;
}
