package com.cloudstar.service.pojo.vo.responsevo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

import lombok.Data;

/**
 * 告警数据excel
 *
 * @author: wanglang
 * @date: 2023/6/6 3:26 PM
 */
@Data
public class MonitorAlarmDataExcelResponse implements Serializable {
    /**
     * 告警级别
     * 从Code中获取：故障、严重、一般
     */
    @ExcelProperty("告警级别")
    private String level;

    /**
     * 告警名称
     */
    @ExcelProperty("告警名称")
    private String name;

    /**
     * 告警对象
     */
    @ExcelProperty("告警对象")
    private String target;

    /**
     * 告警对象类型
     * 从CMDB组件中获取：如云主机、云数据库、硬盘
     */
    @ExcelProperty("对象类型")
    private String targetType;

    /**
     * 告警来源
     */
    @ExcelProperty("告警来源")
    private String source;

    /**
     * 告警状态
     */
    @ExcelProperty("告警状态")
    private String status;

    /**
     * 开始时间
     */
    @ExcelProperty("开始时间")
    private String startTime;

    /**
     * 持续时长
     */
    @ExcelProperty("持续时长")
    private String duration;

    /**
     * 告警发生次数
     */
    @ExcelProperty("告警发生次数")
    private Integer count;

    /**
     * 最新发生时间
     */
    @ExcelProperty("最后发生时间")
    private String occurTime;

    /**
     * 告警通知策略
     */
    @ExcelProperty("通知策略")
    private String notifyPolicyName;

    /**
     * 告警处理状态
     */
    @ExcelProperty("处理状态")
    private String processingStatus;

    /**
     * 告警处理人
     */
    @ExcelProperty("处理人")
    private String processingUser;

    /**
     * 确认时间
     */
    @ExcelProperty("确认时间")
    private String confirmTime;

    /**
     * 告警规则id
     */
    @ExcelIgnore
    private Long opsAlarmRuleId;

    /**
     * 告警对象实例id
     */
    @ExcelIgnore
    private String objectInstanceId;
}
