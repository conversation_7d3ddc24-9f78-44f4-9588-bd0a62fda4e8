package com.cloudstar.service.pojo.vo.responsevo.access;

import lombok.Data;

/**
 * 断言签证官
 *
 * <AUTHOR>
 * Created on 2022/8/16
 * @date 2022/08/16
 */
@Data
public class AssertionVO {
    /**
     * 断言sid
     */
    private Long assertionSid;

    /**
     * 断言类型 view[可见权],mgt[管理权],del[删除权]
     */
    private String assertionType;

    /**
     * 操作名称 如：云主机创建，云主机删除等
     */
    private String opName;

    /**
     * 操作代码 ListUser，createVm等接口操作
     */
    private String opCode;

    /**
     * 产品名称 ecs,ebs,ticket等资源
     */
    private String serviceCode;

    /**
     * 产品名称 云主机，云硬盘等产品名称
     */
    private String serviceName;

    /**
     * 资源代码 子资源代码
     */
    private String resourceCode;

    /**
     * 资源名称 子资源名称，如：vpc下可单独定义port
     */
    private String resourceName;

    /**
     * 限制资源 [*]暂时只支持全部资源
     */
    private String resource;

    /**
     * 控制效果 allow[允许]deny[拒绝]
     */
    private String effect;

    /**
     * 状态 0禁用1启用
     */
    private Boolean status;

    /**
     * 所属模块SID 所属关联模块SID
     */
    private String moduleSid;

    /**
     * 默认策略 1默认策略 0非默认策略，默认策略默认不在页面显示，授权时会自动关联添加
     */
    private Boolean isDefault;

    /**
     * 排序号
     */
    private Integer sortRank;

    /**
     * 是否拥有断言
     */
    private boolean select;
}
