package com.cloudstar.service.pojo.vo.responsevo.alarm;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>描述: [OpsAlarmRule 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_alarm_rule")
public class AlarmRuleResultDto implements Serializable {
    /**
     * id;id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 告警名称
     */
    private String name;

    /**
     * 采集规则维度id
     */
    private Long opsCollectRuleDimensionsId;
    /**
     * 告警对象类型;根据cmdb数据结构code进行关联查询
     */
    private String alarmTargetType;
    /**
     * 告警对象类型范围;all_alarm_object 所有告警对象 cloud_env_alarm_object 指定云环境下所有告警对象 alarm_object 指定告警对象
     */
    private String alarmTargetScope;
    /**
     * 告警检测类型;static_wide_value 静态阔值 promql 自定义PromQL
     */
    private String alarmDetectionType;
    /**
     * 状态;enable 启用 disable 禁用
     */
    private String status;
    /**
     * PromQL;当告警检测类型为自定义PromQL时才会有值
     */
    private String promQl;

    private String triggerRule;
    /**
     * 持续时间(分钟);当告警条件满足时，直接产生告警：0 当告警条件满足持续多久才产生告警：具体值
     */
    private Integer duration;
    /**
     * 告警级别状态
     */
    private String alarmLevelStatus;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 告警通知策略id
     */
    private Long opsNotifyPolicyId;

    /**
     * 组装id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long orgId;

    /**
     * 告警规则配置
     */
    private String ruleConfig;

    /**
     * 执行频率 单位 s
     */
    private Integer promEvalInterval;

    /**
     * 留观时长 单位 s
     */
    private Integer recoverDuration;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Long version;

}
