package com.cloudstar.service.pojo.vo.responsevo.bigscreen;


import com.cloudstar.service.pojo.vo.base.bigscreen.ClusterInfoVo;

import java.util.List;

import lombok.Data;

@Data
public class ClusterStatisticInfoResponse {

    /**
     * cluster name
     */
    private String  clusterName;

    /**
     * 总算力
     * total compute power
     */
    private Integer totalComputePower;

    /**
     * 实时算力
     * current compute power
     */
    private Double currentComputePower;

    /**
     * 总作业数
     * total job num
     */
    private Long totalJobNum;

    /**
     * 完成作业数
     * total completed job num
     */
    private Long totalCompletedJobNum;

    /**
     * 总存储容量
     * total storage capacity
     */
    private String totalStorageCapacity;

    /**
     * 集群连线信息
     * cluster info vo list
     */
    private List<ClusterInfoVo> clusterInfoVoList;

}
