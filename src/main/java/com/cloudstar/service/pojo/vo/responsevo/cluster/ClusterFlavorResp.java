package com.cloudstar.service.pojo.vo.responsevo.cluster;

import com.cloudstar.common.base.enums.ClusterResPoolTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

/**
 * 集群规格响应值
 */
@Setter
@Getter
public class ClusterFlavorResp {

    /**
     * id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 资源池类型 share:共享资源池,exclusive:专属资源池,all：两者都是
     */
    private ClusterResPoolTypeEnum resPoolType;

    /**
     * 资源池类型名称
     */
    private String resPoolTypeName;


    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群类型
     */
    private String clusterType;

    /**
     * 底层资源规格id
     */
    private String flavorId;

    /**
     * 资源规格类型;CPU,NPU,GPU
     */
    private String flavorType;

    /**
     * 资源规格最大节点数
     */
    private Integer maxNum;

    /**
     * CPU架构
     */
    private String cpuArch;

    /**
     * CPU核数
     */
    private String cpuCoreNum;

    /**
     * GPU卡数
     */
    private String gpuUnitNum;

    /**
     * GPU产品名称
     */
    private String gpuProductName;

    /**
     * GPU内存
     */
    private String gpuMemory;

    /**
     * NPU卡数
     */
    private String npuUnitNum;

    /**
     * NPU产品名称
     */
    private String npuProductName;

    /**
     * NPU内存
     */
    private String npuMemory;

    /**
     * 内存大小
     */
    private Object memorySize;

    /**
     * 内存单位
     */
    private String memoryUnit;

    /**
     * 磁盘大小
     */
    private Object diskSize;

    /**
     * 磁盘单位
     */
    private String diskUnit;
    

    /**
     * 是否可见 true:可见 false:不可见
     */
    private String isVisible;

    /**
     * 是否是同步数据;true:是,false:不是（手动维护）
     */
    private String isSync;

    /**
     * 资源规格显示名称
     */
    private String displayName;

    /**
     * 虚拟化百分比
     */
    private Integer virtualizationPercentage;

    /**
     * 是否虚拟化 1:是 0:否
     */
    private Integer isVirtualized;

    /**
     * 计算卡型号
     */
    private String computeProductName;

    /**
     * 节点用途
     */
    private String nodePurpose;


    public String getResPoolTypeName() {
        if (resPoolType == null) {
            return null;
        }
        return resPoolType.getDesc();
    }
}
