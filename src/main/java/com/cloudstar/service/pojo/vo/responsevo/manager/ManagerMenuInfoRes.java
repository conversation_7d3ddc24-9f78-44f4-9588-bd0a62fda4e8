package com.cloudstar.service.pojo.vo.responsevo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;


/**
 * 管理员菜单
 */
@Data
public class ManagerMenuInfoRes {
    /**
     * 菜单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long menuSid;

    /**
     * 菜单编码
     */
    private String code;

    /**
     * 菜单名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 菜单英文名称
     */
    private String engName;

    /**
     * 菜单类型
     */
    private String menuType;

    /**
     * 菜单图标
     */
    private String ico;

    /**
     * 路由类型;路由:ROUTE   外联:OUTREACH   页面嵌入:PAGE_EMBEDDING
     */
    private String routeType;

    /**
     * 路由地址
     */
    private String routePath;

    /**
     * 组件地址
     */
    private String modulePath;

    /**
     * 源数据
     */
    private String meta;

    /**
     * 是否显示;true:显示 不显示:false
     */
    private String display;

    /**
     * 状态;启用:ENABLE  禁用:DISABLED
     */
    private String status;

    /**
     * 描述
     */
    private String description;

    /**
     * 父级id, 默认0
     */
    private Long parentId;

    /**
     * 关系链表ids
     */
    private String pathIds;

    /**
     * 下级关系链表ids
     */
    private String lowerPathIds;

    /**
     * 角色类型;SYSTEM：系统内置，customize：自定义
     */
    private String type;

    /**
     * 创建人id
     */
    private Long createById;

    /**
     * 排序
     */
    private Long sort;

}
