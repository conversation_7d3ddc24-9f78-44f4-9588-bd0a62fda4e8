package com.cloudstar.service.pojo.vo.responsevo.notice;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 公告列表
 *
 * <AUTHOR>
 * @date 2022/11/3 18:08
 */
@Data
public class NoticeListResponse {

    /**
     * 公告ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 公告标题
     */
    private String noticeTitle;
    /**
     * 发布时间
     */
    private Date publishDt;
    /**
     * 创建时间
     */
    private Date createDt;
    /**
     * 状态(0-待发布，1-已发布)
     */
    private String noticeStatus;
    /**
     * 状态名
     */
    private String statusName;
    /**
     * 创建人
     */
    private String createBy;
}
