package com.cloudstar.service.pojo.vo.responsevo.schedule;

import com.cloudstar.enums.SchedulePolicyStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.List;

import lombok.Data;

/**
 * 调度策略详情响应值
 *
 * <AUTHOR>
 * @date 2022/9/14 10:13
 */
@Data
public class SchedulePolicyDetailResp {

    /**
     * id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 策略名称
     */
    private String policyName;

    /**
     * 策略代码;COST:成本优先 QUEUE:排队时长  RESOURCE:资源分配
     */
    private String policyCode;

    /**
     * 状态;ENABLE:启用 DISABLE:禁用
     */
    private SchedulePolicyStatusEnum status;

    /**
     * 状态中文
     */
    private String statusName;

    /**
     * 备注
     */
    private String remark;

    private List<SchedulePolicyWeightResp> weightList;

    public String getStatusName() {
        if (status == null) {
            return null;
        }
        return status.getDesc();
    }
}
