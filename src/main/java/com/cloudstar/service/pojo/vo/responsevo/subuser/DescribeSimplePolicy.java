package com.cloudstar.service.pojo.vo.responsevo.subuser;


import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户权限列表
 *
 * <AUTHOR>
 * @date 2022/08/15
 */
@Data
public class DescribeSimplePolicy {
    /**
     * 策略SID
     */
    private Long policySid;

    /**
     * 策略标识
     */
    private String policyName;

    /**
     * 资源类型 具体涉及到的策略资源类型如：全部 all或者 ecs,ebs多个以逗号分隔
     */
    private List<String> resourceTypes;

    /**
     * 描述
     */
    private String displayName;

    /**
     * 授权时间
     */
    private Date authorizedTime;

}
