package com.cloudstar.service.pojo.vo.responsevo.ticket;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 工单分页查询返回
 *
 * <AUTHOR>
 * @date 2022-09-08 17:27
 */
@Data
public class TicketPageResp {


    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 工单编号
     */
    private String ticketNo;

    /**
     * 工单类型
     */
    private String ticketType;
    /**
     * 工单类型
     */
    private String ticketTypeName;

    /**
     * 工单标题
     */
    private String ticketTitle;

    /**
     * 状态
     */
    private String status;
    /**
     * 状态
     */
    private String statusName;

    /**
     * 提交人名字
     */
    private String ownerName;

    /**
     * 分配人
     */
    private String allocationTicketUser;

    /**
     * 处理人用户名
     */
    private String handlerUserName;

    /**
     * 处理程序用户id
     */
    private Long handlerUserId;


    /**
     * 是否是处理用户
     */
    private Boolean isHandlerUser;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;

}
