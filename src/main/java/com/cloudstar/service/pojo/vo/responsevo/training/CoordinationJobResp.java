package com.cloudstar.service.pojo.vo.responsevo.training;

import com.cloudstar.common.base.enums.TrainingJobGroupStatusEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 协同作业返回resp
 *
 * @author: zengxin
 * @date: 2022/11/11 15:01
 */
@Data
public class CoordinationJobResp {
    /**
     * 作业id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userSid;
    /**
     * 作业名称
     */
    private String name;
    /**
     * 所属作业组id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long jobGroupId;
    /**
     * 所属作业组name
     */
    private String jobGroupName;
    /**
     * 所属作业组状态
     */
    private TrainingJobGroupStatusEnum jobGroupStatus;
    /**
     * 作业状态
     */
    private TrainingJobStatusEnum status;
    /**
     * 是否就绪
     */
    private String isReady;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 作业类型
     */
    private String jobType;
    /**
     * 作业启动时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date jobStartTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDt;

    /**
     * 运行时长
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long runDuration;

    public String getStatusName() {
        if (status != null) {
            return status.getDesc();
        }
        return null;
    }

    public String getJobGroupStatusName() {
        if (jobGroupStatus != null) {
            return jobGroupStatus.getDesc();
        }
        return null;
    }
}
