package com.cloudstar.service.pojo.vo.responsevo.training;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 统计
 *
 * <AUTHOR>
 * @date 2022/9/14 18:29
 */
@Data
public class TrainingJobStatisticsResp {

    /**
     * 集群id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 调度中数量
     */
    private Integer schedulingNum = 0;

    /**
     * 排队中数量
     */
    private Integer queuingNum = 0;

    /**
     * 运行中数量
     */
    private Integer runningNum = 0;

    /**
     * 失败数量
     */
    private Integer failedNum = 0;

    /**
     * 已完成数量
     */
    private Integer completedNum = 0;


    /**
     * 已终止数量
     */
    private Integer terminatedNum = 0;


    /**
     * 停止中数量
     */
    private Integer stoppingNum = 0;


    /**
     * 删除中数量
     */
    private Integer deleteNum = 0;

    /**
     * 已删除的数量
     */
    private Integer deletedNum = 0;

    /**
     * 已取消
     */
    private Integer canceledNum = 0;

    /**
     * 创建中
     */
    private Integer creatingNum = 0;

    /**
     * 作业总数
     */
    private Integer total = 0;
}
