syntax = "proto3";

package protocol;

option java_multiple_files = false;
option java_package = "com.cloudstar.service.grpc";
option java_outer_classname = "MirrorBuildProto";

service MirrorBuildService {
  rpc ExecuteBuild(MirrorBuildMessage) returns (stream MirrorBuildResponse);
}

message MirrorBuildMessage {
  string trainingFramework = 1;
  string trainingDriver = 2;
  string platformArchitecture = 3;
  string operatingSystem = 4;
  string mirrorName = 5;
  int64 orgSid = 6;
  string account = 7;
  string customContent = 8;
}

message MirrorBuildResponse {
  bool success = 1;
  string message = 2;
  string status = 3;
  string content= 4;
}