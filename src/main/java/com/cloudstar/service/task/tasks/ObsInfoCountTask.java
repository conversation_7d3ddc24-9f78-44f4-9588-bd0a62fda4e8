package com.cloudstar.service.task.tasks;


import com.cloudstar.service.facade.ObsInfoCountService;
import com.cloudstar.service.task.base.bean.AddJobParam;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
public class ObsInfoCountTask implements Job {


    @Resource
    ObsInfoCountService obsInfoCountService;


    /**
     * 定时任务配置
     */
    public static AddJobParam initJobParam() {
        return AddJobParam.builder()
                          // 每半个小时执行一次
                          .cronExpression("0 0/30 * * * ? *")
                          .jobName("obs-info-count-job")
                          .jobGroup("obs-info-count-job-group")
                          .triggerName("obs-info-count-job-trigger")
                          .triggerGroup("obs-info-count-job-trigger-group")
                          .description("统计obs信息")
                          .clazz(ObsInfoCountTask.class)
                          .build();

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("OBS统计开始");
        obsInfoCountService.obsCount();
        log.info("OBS统计完成");
    }
}
