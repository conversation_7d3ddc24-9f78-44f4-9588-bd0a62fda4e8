package com.cloudstar.service.task.tasks;

import com.cloudstar.service.facade.datastorage.DataStorageResourcesGrpcService;
import com.cloudstar.service.task.base.bean.AddJobParam;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 同步数据资源
 *
 * <AUTHOR>
 * @date 2022-09-14 09:39
 */
@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SyncDataStorageTask implements Job {

    /**
     * 定时任务配置
     * @return
     */
    public static AddJobParam initJobParam() {
        return AddJobParam.builder()
                          //十分钟同步一次
                          .cronExpression("0 0/10 * * * ?  ")
                          .jobName("sync-datastorage-job")
                          .jobGroup("sync-datastorage-job-group")
                          .triggerName("sync-datastorage-trigger")
                          .triggerGroup("sync-datastorage-trigger-group")
                          .description("sync datastorage")
                          .clazz(SyncDataStorageTask.class)
                          .build();


    }

    DataStorageResourcesGrpcService service;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        service.syncDataStorage();
    }
}
