package com.cloudstar.service.utils;

import com.cloudstar.common.base.exception.BizException;

import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 校验模型url以及key是否有效
 */
public class ValidateApiKeyUtil {

    /**
     * 校验
     * @param apiUrl url
     * @param apiKey key
     */
    public static void validateApiKey(String apiUrl, String apiKey) {

        String reqUrl = apiUrl + "/models";

        try {
            URL url = new URL(reqUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(5000);
            connection.setRequestProperty("Authorization", "Bearer " + apiKey);

            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                throw new BizException("APIKEY无效，请重新输入");
            }
        } catch (Exception e) {
            throw new BizException("APIKEY无效，请重新输入");
        }
    }
}