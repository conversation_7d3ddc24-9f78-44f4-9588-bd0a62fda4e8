spring:
  config:
    activate:
      on-profile: server-only
  datasource:
    dynamic:
      primary: cfn
      strict: false
      datasource:
        cfn:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://${cloudstar.db.url:***********:31633/agent}?currentSchema=public&stringtype=unspecified
          username: ${cloudstar.db.username:cfn}
          password: ${cloudstar.db.password:QGrePyjOZs8eYf8LR7RCZjYU1Qd^^q}
  redis:
    host: ${cloudstar.redis.host:127.0.0.1}
    port: ${cloudstar.redis.port:6379}
    password: ${cloudstar.redis.password:}
    database: 15
    jedis:
      pool:
        max-active: 128
        max-idle: 32
        min-idle: 1
  elasticsearch:
    uris: https://************:31690
    connection-timeout: 5000
    socket-timeout: 30000
    username: elastic
    password: A182TJ8eQNR8N1fFSv7109gP
  rabbitmq:
    host: ${cloudstar.mq.host:***********}
    port: ${cloudstar.mq.port:30006}
    username: ${cloudstar.mq.username:admin}
    password: ${cloudstar.mq.password:D3oysv9A6Vxc&ctE8iT%}
    requested-heartbeat: 60s
    virtual-host: cfn
    listener:
      simple:
        retry:
          initial-interval: 10000ms
          enabled: true
          max-attempts: 3
server:
  port: 9002
