spring:
  config:
    activate:
      on-profile: local
  datasource:
    dynamic:
      primary: cfn
      strict: false
      datasource:
        cfn:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://${cloudstar.db.url:***********:31633/cfn}?currentSchema=public&stringtype=unspecified
          username: ${cloudstar.db.username:postgres}
          password: ${cloudstar.db.password:grJ2lHcZOuDGXMSe8h3m}
#        monitor:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          url: jdbc:postgresql://${cloudstar.db.monitor.url:***********:31633/cfn}?currentSchema=public&stringtype=unspecified
#          username: ${cloudstar.db.username:postgres}
#          password: ${cloudstar.db.password:grJ2l<PERSON><PERSON>ZOuDGXMSe8h3m}
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  redis:
    host: ${cloudstar.redis.host:127.0.0.1}
    port: ${cloudstar.redis.port:6379}
    password: ${cloudstar.redis.password:}
    database: 15
    jedis:
      pool:
        max-active: 128
        max-idle: 32
        min-idle: 1
  elasticsearch:
    uris: https://************:31690
    connection-timeout: 5000
    socket-timeout: 30000
    username: elastic
    password: A182TJ8eQNR8N1fFSv7109gP
  rabbitmq:
    host: ${cloudstar.mq.host:***********}
    port: ${cloudstar.mq.port:30006}
    username: ${cloudstar.mq.username:admin}
    password: ${cloudstar.mq.password:D3oysv9A6Vxc&ctE8iT%}
    requested-heartbeat: 60s
    virtual-host: cfn
server:
  port: 9001
feign:
  url:
    cfn-schedule: 127.0.0.1:9002
    cfn-console-server: 127.0.0.1:9003
    cfn-iam: 127.0.0.1:9010
    cfn-monitor: 127.0.0.1:9004
---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    dynamic:
      primary: cfn
      strict: false
      datasource:
        cfn:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: ***********************************************************************************
          username: cfn
          password: QGrePyjOZs8eYf8LR7RCZjYU1Qd^^q
#        monitor:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          url: ***********************************************************************************
#          username: cfn
#          password: QGrePyjOZs8eYf8LR7RCZjYU1Qd^^q
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  redis:
    host: ***********
    port: 30005
    password: 45T#BoK5DU*xHIqt!Ca4
    database: 15
    jedis:
      pool:
        max-active: 128
        max-idle: 32
        min-idle: 1
  rabbitmq:
    host: ***********
    port: 32174
    username: admin
    password: D3oysv9A6Vxc&ctE8iT%
    requested-heartbeat: 60s
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        #决定被拒绝的消息是否重新入队
        default-requeue-rejected: true
        retry:
          #时间间隔5秒
          initial-interval: 5000ms
          enabled: true
          #最大重试次数
          max-attempts: 3
server:
  port: 9001
feign:
  url:
    cfn-schedule: 127.0.0.1:9002
    cfn-console-server: 127.0.0.1:9003
    cfn-iam: 127.0.0.1:9010
    cfn-monitor: 127.0.0.1:9004
---
server:
  servlet:
    context-path: /api/v1/server
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.cloudstar.dao.model
  configuration:
    log-impl: ${mybatis.log.impl:org.apache.ibatis.logging.nologging.NoLoggingImpl}
feign:
  circuitbreaker:
    enabled: true
upload:
  base:
    path: /opt/cmp/files
  url:
    prefix: /files/
spring:
  application:
    name: cfn-server
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
#  sql:
#    init:
file:
  storage:
    active: local
    local:
      path: /opt/data/cfn/
      prefix: /cfn/
#      path: D:/cfn/
#      prefix: /cfn/
cfn-rightcloud:
  role:
    OPERATION_USER: USER_ADMIN
    SYS_ADMIN: ADMIN
    OPERATION_ADMIN: OPERATION_ADMIN
    OPERATION_GENERAL: OPERATION_ADMIN
    CUSTOMER_SERVICE: AUDIT_ADMIN
    DISTRIBUTOR_ADMIN: MAINTEN_ADMIN
    DEFAULT_ROLE_CODE: ""

retrofit:
  bss:
    base-url: ${BSS_BASE_URL:https://***********:81}
cfn:
  mq:
    start: ${CFN_MQ_START:false}
