<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.utils.data.mapper.user.AuthUserUtilMapper">



    <select id="hasAdminCount" resultType="java.lang.Integer">
        SELECT
            COUNT(*) AS adminRoleCount
        FROM
            sys_user_role_rela
        WHERE
            role_id = 100
          AND user_id = #{userid}
    </select>

    <select id="findByUserAccount" resultType="com.cloudstar.rightcloud.common.pojo.user.AuthUser">
        SELECT
            user_id as userid ,
            user_type as userType,
            account as account,
            real_name as realName,
            sex as sex,
            email as email,
            mobile as mobile,
            STATUS as status,
            project_id as projectId,
            company_id as companyId
        FROM
            sys_user
        WHERE
            account = #{account}
    </select>
    <select id="findByUserId" resultType="com.cloudstar.rightcloud.common.pojo.user.AuthUser">
        SELECT
            user_id as userid ,
            user_type as userType,
            account as account,
            real_name as realName,
            sex as sex,
            email as email,
            mobile as mobile,
            STATUS as status,
            project_id as projectId,
            company_id as companyId
        FROM
            sys_user
        WHERE
            user_id = #{userid}
    </select>
</mapper>
