apiVersion: v1
kind: ConfigMap
metadata:
  name: volcano-admission-configmap
  namespace: volcano-system
data:
  volcano-admission.conf: |
    resourceGroups:
        - resourceGroup: Local Group
      object:
        key: annotation
        value:
        - "Compute Node-NVIDIA A100"
      schedulerName: Local Device Scheduler 
    - resourceGroup: Test Group
      object:
        key: annotation
        value:
        - "Testing Node-AMD EPYC"
      schedulerName: Test Scheduler 
    - resourceGroup: Shanghai Group
      object:
        key: annotation
        value:
        - "Production Node-Intel Xeon"
      schedulerName: Shanghai Supercomputer 
