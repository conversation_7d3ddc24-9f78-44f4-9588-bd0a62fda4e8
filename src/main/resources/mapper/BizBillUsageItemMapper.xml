<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.bill.BizBillUsageItemMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.bill.BizBillUsageItem">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="billNo" column="bill_no" jdbcType="VARCHAR"/>
        <result property="orgSid" column="org_sid" jdbcType="BIGINT"/>
        <result property="jobId" column="job_id" jdbcType="VARCHAR"/>
        <result property="billingCycle" column="billing_cycle" jdbcType="VARCHAR"/>
        <result property="billType" column="bill_type" jdbcType="VARCHAR"/>
        <result property="ownerSid" column="owner_sid" jdbcType="BIGINT"/>
        <result property="product" column="product" jdbcType="VARCHAR"/>
        <result property="usageStartDate" column="usage_start_date" jdbcType="TIMESTAMP"/>
        <result property="usageEndDate" column="usage_end_date" jdbcType="TIMESTAMP"/>
        <result property="usageCount" column="usage_count" jdbcType="NUMERIC"/>
        <result property="summaryFlag" column="summary_flag" jdbcType="CHAR"/>
        <result property="configuration" column="configuration" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,bill_no,org_sid,
        job_id,billing_cycle,bill_type,
        owner_sid,product,usage_start_date,
        usage_end_date,usage_count,summary_flag,
        configuration,version,created_by,
        created_dt,updated_by,updated_dt
    </sql>

    <sql id="UsageItemList">
        SELECT
        bui.billing_cycle,
        bui.bill_no,
        bui.product,
        ue.account as ownerName,
        ce.cluster_name,
        ce.cluster_type,
        bui.usage_count,
        bui.bill_type,
        bui.usage_start_date,
        bui.usage_end_date,
        bui.configuration,
        bui.job_id,
        cf.display_name
        <if test="req.flavorType != null and req.flavorType != ''">
            ,bui.usage_count *
            <choose>
                <when test="req.flavorType == 'GPU'">
                    COALESCE(cf.gpu_unit_num::INTEGER, 0)
                </when>
                <when test="req.flavorType == 'NPU'">
                    COALESCE(cf.npu_unit_num::INTEGER, 0)
                </when>
                <otherwise>
                    0
                </otherwise>
            </choose> AS calculated_usage
        </if>
        FROM
        biz_bill_usage_item bui
        LEFT JOIN user_entity ue ON bui.owner_sid = ue.user_sid
        LEFT JOIN cluster_entity ce on bui.adapter_uuid = ce.adapter_uuid
        left join cluster_flavor cf on ce."id" = cf.cluster_id and bui."configuration" = cf.flavor_id
        <where>
            <if test="req.startDate != null and req.startDate!= ''">
                and to_char(bui.usage_start_date,'yyyy-mm-dd') &gt;= #{req.startDate}
            </if>
            <if test="req.endDate != null and req.endDate != ''">
                and to_char(bui.usage_start_date,'yyyy-mm-dd') &lt;= #{req.endDate}
            </if>
            <if test="req.billNo != null and req.billNo !=''">
                and bui.bill_no like concat('%',#{req.billNo}::text,'%')
            </if>
            <if test="req.billingCycle != null and req.billingCycle !=''">
                and bui.billing_cycle = #{req.billingCycle}
            </if>
            <if test="req.clusterName != null and req.clusterName !=''">
                and ce.cluster_name like concat('%',#{req.clusterName}::text,'%')
            </if>
            <if test="req.ownerName != null and req.ownerName !=''">
                and ue.account like concat('%',#{req.ownerName}::text,'%')
            </if>
            <if test="req.flavorType != null and req.flavorType !=''">
                and cf.flavor_type = #{req.flavorType}
            </if>
            <if test="req.product != null and req.product !=''">
                and bui.product in
                <foreach collection="req.product" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="req.sortDataField != null and req.sortDataField != ''">
            order by ${req.sortDataField}
        </if>
    </sql>

    <select id="getBillUsageItemList"
        resultType="com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemPageResp">
        <include refid="UsageItemList"></include>
    </select>

    <select id="getBillUsageItemExportList"
        resultType="com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemExportResp">
        <include refid="UsageItemList"></include>
    </select>

    <!--  获得集群使用量数据  -->
    <select id="getClusterUsage"
        resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUsageResp">
        SELECT ac.cluster_name as clusterName,
        COALESCE(it.usage,0) as usageCount
        FROM CLUSTER_ENTITY ac
        LEFT JOIN (SELECT round(sum(bui.usage_count ::numeric / 3600 *
        (coalesce(cf.gpu_unit_num::numeric, 0) +
        coalesce(cf.npu_unit_num::numeric, 0))),
        2) AS usage,
        ce.cluster_name
        FROM
        biz_bill_usage_item bui
        left join cluster_flavor cf on cf.flavor_id=bui.configuration
        LEFT JOIN cluster_entity ce on bui.adapter_uuid = ce.adapter_uuid
        where bui.product <![CDATA[<>]]> 'obs'
        <if test="time != null and time != ''">
            AND bui.usage_start_date &gt;= cast(#{time} as date)
        </if>
        group by ce.cluster_name) it on it.cluster_name = ac.cluster_name
    </select>

    <select id="getMeasuringStatus"
            resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterMeasuringStatusResp">
        SELECT bbui."product" as serviceName,
               COUNT (1) as sumAcquisition,
               MAX (bbui.usage_start_date) as endAcquisitionTime
        FROM "biz_bill_usage_item" bbui
                 LEFT JOIN "cluster_entity" ce ON ce.adapter_uuid = bbui.adapter_uuid
        WHERE ce."id" = #{clusterId}
        GROUP BY bbui."product"
    </select>
</mapper>
