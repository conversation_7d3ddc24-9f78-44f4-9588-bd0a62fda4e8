<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.ManagerEntityMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.ManagerEntity">
            <id property="userSid" column="user_sid" jdbcType="OTHER"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="realName" column="real_name" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
            <result property="lastLoginIp" column="last_login_ip" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="isResetPassword" column="is_reset_password" jdbcType="VARCHAR"/>
            <result property="passwordExpiresAt" column="password_expires_at" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_sid,uuid,account,
        real_name,password,email,
        mobile,status,last_login_time,
        last_login_ip,start_time,end_time,
        is_reset_password,password_expires_at,version,
        created_by,created_dt,updated_by,
        updated_dt
    </sql>

    <select id="selectPermissionByUserSid" resultType="com.cloudstar.pojo.user.dto.PermissionDTO">
        SELECT e.url    path,
               e.method method
        FROM
            manager_entity A,
            manager_entity_role b,
            manager_role C,
            manager_role_permission d,
            manager_permission e
        WHERE
            A.user_sid = b.user_sid
          AND b.role_sid = C.role_sid
          AND C.role_sid = d.role_sid
          AND d.permission_sid = e.permission_sid
          AND A.user_sid = #{userSid}
    </select>
    <select id="selectPublicPermission" resultType="com.cloudstar.pojo.user.dto.PermissionDTO">
        SELECT e.url    path,
               e.method method
        FROM
            manager_permission e
        WHERE
            e.is_public = 'public'
    </select>
</mapper>
