<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.manager.ManagerPermissionMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.manager.ManagerPermission">
        <result property="permissionSid" column="permission_sid" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="SMALLINT"/>
        <result property="moduleCategory" column="module_category" jdbcType="VARCHAR"/>
        <result property="controllerCategory" column="controller_category" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        permission_sid
        ,name,url,
        method,sort,module_category,controller_category
    </sql>
    <select id="selectPermissionByMenuSid" resultType="com.cloudstar.dao.model.manager.ManagerPermission">
        SELECT a.*
        FROM manager_permission a,
             manager_menu_permission b,
             manager_menu c
        where a.permission_sid = b.permission_sid
          and b.menu_sid = c.menu_sid
          and c.menu_sid = #{menuSid}
          and a.is_public = 'private'
        order by a.sort
    </select>
    <select id="selectPermissionByRoleSid" resultType="java.lang.Long">
        SELECT distinct A.permission_sid
        FROM manager_permission A,
             manager_role_permission b,
             manager_role C
        WHERE A.permission_sid = b.permission_sid
          AND b.role_sid = C.role_sid
          and C.role_sid = #{roleSid}
    </select>
    <select id="selectPermissionByMenusSid" resultType="com.cloudstar.dao.model.manager.ManagerPermission">
        SELECT a.*
        from manager_permission a,
             manager_menu_permission b
        where a.permission_sid = b.permission_sid
          and b.menu_sid = #{menusSid}
    </select>
</mapper>
