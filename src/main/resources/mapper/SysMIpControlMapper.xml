<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.SysMIpControlMapper">
  <resultMap id="BaseResultMap" type="com.cloudstar.common.base.pojo.SysMIpControl">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="control_type" jdbcType="VARCHAR" property="controlType" />
    <result column="ip_request_limit" jdbcType="INTEGER" property="ipRequestLimit" />
    <result column="ip_control_flg" jdbcType="BIT" property="ipControlFlg" />
    <result column="owner_id" jdbcType="VARCHAR" property="ownerId" />
    <result column="org_sid" jdbcType="BIGINT" property="orgSid" />
    <result column="created_org_sid" jdbcType="VARCHAR" property="createdOrgSid" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, control_type, ip_request_limit, ip_control_flg, owner_id, org_sid, created_org_sid,
    version, created_by, created_dt, updated_by, updated_dt
  </sql>
  <sql id="Example_Where_Clause">
    <trim prefix="where" prefixOverrides="and|or">
      <if test="condition.controlType != null">
        and control_type = #{condition.controlType}
      </if>
    </trim>
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from sys_m_ip_control
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByParam" parameterType="com.cloudstar.common.base.pojo.Criteria" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from sys_m_ip_control
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from sys_m_ip_control
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cloudstar.common.base.pojo.SysMIpControl" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_m_ip_control (control_type, ip_request_limit, ip_control_flg,
      owner_id, org_sid, created_org_sid,
      version, created_by, created_dt,
      updated_by, updated_dt)
    values (#{controlType,jdbcType=VARCHAR}, #{ipRequestLimit,jdbcType=INTEGER}, #{ipControlFlg,jdbcType=BIT},
      #{ownerId,jdbcType=VARCHAR}, #{orgSid,jdbcType=BIGINT}, #{createdOrgSid,jdbcType=VARCHAR},
      #{version,jdbcType=BIGINT}, #{createdBy,jdbcType=VARCHAR}, #{createdDt,jdbcType=TIMESTAMP},
      #{updatedBy,jdbcType=VARCHAR}, #{updatedDt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cloudstar.common.base.pojo.SysMIpControl" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_m_ip_control
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="controlType != null">
        control_type,
      </if>
      <if test="ipRequestLimit != null">
        ip_request_limit,
      </if>
      <if test="ipControlFlg != null">
        ip_control_flg,
      </if>
      <if test="ownerId != null">
        owner_id,
      </if>
      <if test="orgSid != null">
        org_sid,
      </if>
      <if test="createdOrgSid != null">
        created_org_sid,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDt != null">
        created_dt,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedDt != null">
        updated_dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="controlType != null">
        #{controlType,jdbcType=VARCHAR},
      </if>
      <if test="ipRequestLimit != null">
        #{ipRequestLimit,jdbcType=INTEGER},
      </if>
      <if test="ipControlFlg != null">
        #{ipControlFlg,jdbcType=BIT},
      </if>
      <if test="ownerId != null">
        #{ownerId,jdbcType=VARCHAR},
      </if>
      <if test="orgSid != null">
        #{orgSid,jdbcType=BIGINT},
      </if>
      <if test="createdOrgSid != null">
        #{createdOrgSid,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDt != null">
        #{createdDt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDt != null">
        #{updatedDt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="com.cloudstar.common.base.pojo.SysMIpControl">
    <!--@mbg.generated-->
    update sys_m_ip_control
    <set>
      <if test="ipRequestLimit != null">
        ip_request_limit = #{ipRequestLimit,jdbcType=INTEGER},
      </if>
      <if test="ipControlFlg != null">
        ip_control_flg = #{ipControlFlg,jdbcType=BIT},
      </if>
      <if test="ownerId != null">
        owner_id = #{ownerId,jdbcType=VARCHAR},
      </if>
      <if test="orgSid != null">
        org_sid = #{orgSid,jdbcType=BIGINT},
      </if>
      <if test="createdOrgSid != null">
        created_org_sid = #{createdOrgSid,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDt != null">
        created_dt = #{createdDt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDt != null">
        updated_dt = #{updatedDt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where control_type = #{controlType,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cloudstar.common.base.pojo.SysMIpControl">
    <!--@mbg.generated-->
    update sys_m_ip_control
    set control_type = #{controlType,jdbcType=VARCHAR},
      ip_request_limit = #{ipRequestLimit,jdbcType=INTEGER},
      ip_control_flg = #{ipControlFlg,jdbcType=BIT},
      owner_id = #{ownerId,jdbcType=VARCHAR},
      org_sid = #{orgSid,jdbcType=BIGINT},
      created_org_sid = #{createdOrgSid,jdbcType=VARCHAR},
      version = #{version,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_dt = #{createdDt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_dt = #{updatedDt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
