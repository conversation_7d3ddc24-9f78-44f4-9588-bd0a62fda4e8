<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.notify.SysNotifyRecordMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.notify.SysNotifyRecord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="notifyConfigId" column="notify_config_id" jdbcType="BIGINT"/>
        <result property="bssType" column="bss_type" jdbcType="VARCHAR"/>
        <result property="thresholdCategory" column="threshold_category" jdbcType="VARCHAR"/>
        <result property="symbol" column="symbol" jdbcType="VARCHAR"/>
        <result property="thresholdValueType" column="threshold_value_type" jdbcType="VARCHAR"/>
        <result property="thresholdValue" column="threshold_value" jdbcType="BIGINT"/>
        <result property="snapshotValue" column="snapshot_value" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="receiver" column="receiver" jdbcType="VARCHAR"/>
        <result property="sendCount" column="send_count" jdbcType="OTHER"/>
        <result property="notifyType" column="notify_type" jdbcType="VARCHAR"/>
        <result property="expireStrategy" column="expire_strategy" jdbcType="VARCHAR"/>
        <result property="mailSendDt" column="mail_send_dt" jdbcType="TIMESTAMP"/>
        <result property="mailSendStatus" column="mail_send_status" jdbcType="VARCHAR"/>
        <result property="smsSendDt" column="sms_send_dt" jdbcType="TIMESTAMP"/>
        <result property="smsSendStatus" column="sms_send_status" jdbcType="VARCHAR"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="accountSid" column="account_sid" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,notify_config_id,bss_type,
        threshold_category,symbol,threshold_value_type,
        threshold_value,snapshot_value,content,
        receiver,send_count,notify_type,
        expire_strategy,mail_send_dt,mail_send_status,
        sms_send_dt,sms_send_status,updated_by,
        created_dt,updated_dt,version,
        account_sid
    </sql>

    <update id="updateByPrimaryKeySelective">
        update sys_m_notify_record
        <set>
            <if test="notifyConfigId != null">
                notify_config_id = #{notifyConfigId,jdbcType=BIGINT},
            </if>
            <if test="bssType != null">
                bss_type = #{bssType,jdbcType=VARCHAR},
            </if>
            <if test="thresholdCategory != null">
                threshold_category = #{thresholdCategory,jdbcType=VARCHAR},
            </if>
            <if test="symbol != null">
                symbol = #{symbol,jdbcType=VARCHAR},
            </if>
            <if test="thresholdValueType != null">
                threshold_value_type = #{thresholdValueType,jdbcType=VARCHAR},
            </if>
            <if test="thresholdValue != null">
                threshold_value = #{thresholdValue,jdbcType=BIGINT},
            </if>
            <if test="snapshotValue != null">
                snapshot_value = #{snapshotValue,jdbcType=BIGINT},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="receiver != null">
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="sendCount != null">
                send_count = #{sendCount,jdbcType=INTEGER},
            </if>
            <if test="notifyType != null">
                notify_type = #{notifyType,jdbcType=VARCHAR},
            </if>
            <if test="expireStrategy != null">
                expire_strategy = #{expireStrategy,jdbcType=VARCHAR},
            </if>
            <if test="mailSendDt != null">
                mail_send_dt = #{mailSendDt,jdbcType=TIMESTAMP},
            </if>
            <if test="mailSendStatus != null">
                mail_send_status = #{mailSendStatus,jdbcType=VARCHAR},
            </if>
            <if test="smsSendDt != null">
                sms_send_dt = #{smsSendDt,jdbcType=TIMESTAMP},
            </if>
            <if test="smsSendStatus != null">
                sms_send_status = #{smsSendStatus,jdbcType=VARCHAR},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  根据主键查询  -->
    <select id="selectByPrimaryKey" resultType="com.cloudstar.dao.model.notify.SysNotifyRecord">
        select
        <include refid="Base_Column_List"/>
        from sys_m_notify_record
        where id = #{id,jdbcType=BIGINT}
    </select>
</mapper>
