<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.access.UserPermissionMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.access.UserPermission">
            <result property="permissionSid" column="permission_sid" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="method" column="method" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        permission_sid,name,url,
        method,sort
    </sql>
    <select id="selectPermissionByRoleSid" resultType="java.lang.Long">
        SELECT DISTINCT A
                            .permission_sid
        FROM user_permission A,
             user_assertion_permission b,
             sys_m_assertion C
        WHERE A.permission_sid = b.permission_sid
          AND b.assertion_sid = C.assertion_sid
          AND C.assertion_sid = #{assertionSid}
          and A.is_public = 'private'
    </select>
    <select id="selectPermissionByMenuSid" resultType="com.cloudstar.dao.model.access.UserPermission">
        SELECT A
                   .*
        FROM user_permission A,
             user_menu_permission b,
             user_menu C
        WHERE A.permission_sid = b.permission_sid
          AND b.menu_sid = C.menu_sid
          AND C.menu_sid = #{menuSid}
        ORDER BY A.sort
    </select>
    <select id="selectPermissionByMenusSid" resultType="com.cloudstar.dao.model.access.UserPermission">
        SELECT a.*
        from user_permission a,
             user_menu_permission b
        where a.permission_sid = b.permission_sid
          and b.menu_sid = #{menusSid}
    </select>
</mapper>
