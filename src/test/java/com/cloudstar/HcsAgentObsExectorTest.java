package com.cloudstar;

import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.dao.mapper.user.AgentUsersMapper;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.service.grpc.AgentObsProto;
import com.cloudstar.service.grpcservice.server.exector.hcs.HcsAgentObsExector;
import com.obs.services.ObsClient;
import com.obs.services.model.ListObjectsRequest;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.ObsObject;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HcsAgentObsExectorTest {

    @Mock
    private AgentUsersMapper agentUsersMapper;

    @Mock
    private ObsClient obsClient;

    @InjectMocks
    private HcsAgentObsExector hcsAgentObsExector;

    @Test
    void existsFileOrFileDir_shouldReturnTrue_whenFileExists() {
        AgentObsProto.existsFileOrFileDirRequest request = AgentObsProto.existsFileOrFileDirRequest.newBuilder()
                .setUserId("83952b28e5474122bd391252ea852688-bucket")
                .setObjectKey("/test-tlh/")
                .build();

        AgentUsers agentUsers = new AgentUsers();
        agentUsers.setUserAk("SQCLOEEO5X5XYGMVSSO0");
        agentUsers.setUserSk("yih9LfMNEiEt1C4UFGlgZmq8KO93Q8TkPfkErDRZ");

        ListObjectsRequest listObjectsRequest = new ListObjectsRequest("83952b28e5474122bd391252ea852688-bucket");
        listObjectsRequest.setPrefix("/test-tlh/");
        listObjectsRequest.setMaxKeys(1);
        listObjectsRequest.setDelimiter("/");

        ObjectListing objectListing = mock(ObjectListing.class);
        ObsObject obsObject = new ObsObject();
        obsObject.setObjectKey("file.txt");
        when(objectListing.getObjects()).thenReturn(Collections.singletonList(obsObject));

        when(agentUsersMapper.selectOne(any())).thenReturn(agentUsers);
        when(obsClient.listObjects(any(ListObjectsRequest.class))).thenReturn(objectListing);

        StreamObserver<AgentObsProto.existsFileOrFileDirResponse> responseObserver = mock(StreamObserver.class);

        hcsAgentObsExector.existsFileOrFileDir(request, responseObserver);

        verify(responseObserver).onNext(AgentObsProto.existsFileOrFileDirResponse.newBuilder()
                .setIsExists(true)
                .build());
        verify(responseObserver).onCompleted();
    }

    @Test
    void existsFileOrFileDir_shouldReturnFalse_whenFileDoesNotExist() {
        AgentObsProto.existsFileOrFileDirRequest request = AgentObsProto.existsFileOrFileDirRequest.newBuilder()
                .setUserId("user-123")
                .setObjectKey("nonexistent.txt")
                .build();

        AgentUsers agentUsers = new AgentUsers();
        agentUsers.setUserAk("test-ak");
        agentUsers.setUserSk("test-sk");

        ObjectListing objectListing = mock(ObjectListing.class);
        when(objectListing.getObjects()).thenReturn(Collections.emptyList());

        when(agentUsersMapper.selectOne(any())).thenReturn(agentUsers);
        when(obsClient.listObjects(any(ListObjectsRequest.class))).thenReturn(objectListing);

        StreamObserver<AgentObsProto.existsFileOrFileDirResponse> responseObserver = mock(StreamObserver.class);

        hcsAgentObsExector.existsFileOrFileDir(request, responseObserver);

        verify(responseObserver).onNext(AgentObsProto.existsFileOrFileDirResponse.newBuilder()
                .setIsExists(false)
                .build());
        verify(responseObserver).onCompleted();
    }

    @Test
    void existsFileOrFileDir_shouldThrowException_whenUserIdIsEmpty() {
        AgentObsProto.existsFileOrFileDirRequest request = AgentObsProto.existsFileOrFileDirRequest.newBuilder()
                .setUserId("")
                .setObjectKey("file.txt")
                .build();

        StreamObserver<AgentObsProto.existsFileOrFileDirResponse> responseObserver = mock(StreamObserver.class);

        assertThrows(BizException.class, () -> hcsAgentObsExector.existsFileOrFileDir(request, responseObserver));
    }

    @Test
    void existsFileOrFileDir_shouldThrowException_whenAgentUserNotFound() {
        AgentObsProto.existsFileOrFileDirRequest request = AgentObsProto.existsFileOrFileDirRequest.newBuilder()
                .setUserId("user-123")
                .setObjectKey("file.txt")
                .build();

        when(agentUsersMapper.selectOne(any())).thenReturn(null);

        StreamObserver<AgentObsProto.existsFileOrFileDirResponse> responseObserver = mock(StreamObserver.class);

        assertThrows(BizException.class, () -> hcsAgentObsExector.existsFileOrFileDir(request, responseObserver));
    }
}