package com.cloudstar;

import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.controller.TestController;
import com.cloudstar.dao.mapper.access.SysMPolicyMapper;
import com.cloudstar.dao.model.access.SysMPolicy;
import com.cloudstar.service.pojo.vo.requestvo.TestVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Locale;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;


/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
//@SpringBootTest(classes = ServerAssembly.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@TestPropertySource(properties = {"spring.config.location=classpath:application.yml"})
@ActiveProfiles("local")
public class ServerTest {

    @Autowired
    private MessageSource messageSource;
    @Autowired
    private TestController controller;
    @Autowired
    private SysMPolicyMapper sysMPolicyMapper;

    @Test
    public void test() {
        List<SysMPolicy> sysMPolicies = sysMPolicyMapper.selectList(SpecWrapperUtil.builder());
        System.out.println(sysMPolicies);
    }

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testRedis() throws JsonProcessingException {

        TestVo testVo = new TestVo();
        testVo.setName("张三");
        testVo.setAge(121);
    }

    @Test
    public void test2() throws JsonProcessingException {

    }

    public static void main(String[] args) {
        System.out.println(CrytoUtilSimple.encrypt("123"));
        System.out.println(CrytoUtilSimple.decrypt("123"));
        //String info = "张三132";
        //
        ////byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.DES.getValue()).getEncoded();
        ////String encode = Base64.encode(key);
        ////System.out.println(encode);
        //byte[] key = Base64.decode("dLv06zKOoGxX+yWwi0g/Kb6zOMB4H4uz6jyqPteJiKU=");
        //
        //String s = SecureUtil.aes(key).encryptHex(info);
        //System.out.println("encrypted ：" + s);
        //
        //System.out.println(SecureUtil.aes(key).decryptStr("a71157faada6fabdad162602d0a02566"));
    }
}
